import requests
import base64
import json

def test_web_api():
    """Test the web API with the test image"""
    
    # Test image path
    image_path = "test-images/image.png"
    
    try:
        # Read and encode image
        with open(image_path, "rb") as image_file:
            image_data = base64.b64encode(image_file.read()).decode()
            image_base64 = f"data:image/png;base64,{image_data}"
        
        # Test API status first
        print("Testing API status...")
        status_response = requests.get("http://localhost:5000/api/status")
        print(f"Status: {status_response.json()}")
        
        # Test detection API
        print("\nTesting helmet detection API...")
        detection_data = {
            "image_base64": image_base64
        }
        
        response = requests.post(
            "http://localhost:5000/api/detect",
            json=detection_data,
            headers={"Content-Type": "application/json"}
        )
        
        if response.status_code == 200:
            result = response.json()
            print("Detection successful!")
            print(f"Response: {json.dumps(result, indent=2)}")
        else:
            print(f"Error: {response.status_code}")
            print(f"Response: {response.text}")
            
    except Exception as e:
        print(f"Error testing API: {e}")

if __name__ == "__main__":
    test_web_api()
