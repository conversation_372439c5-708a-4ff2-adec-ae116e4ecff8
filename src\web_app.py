"""
Flask Web Application for Helmet Detection System
"""

from flask import Flask, render_template, request, jsonify, send_file
from flask_cors import CORS
import os
import cv2
import base64
import numpy as np
from datetime import datetime
import json
from helmet_detector import HelmetDetector
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = Flask(__name__)
CORS(app)

# Initialize helmet detector
detector = HelmetDetector()

# Configuration
UPLOAD_FOLDER = 'uploads'
OUTPUT_FOLDER = 'outputs'
ALLOWED_EXTENSIONS = {'png', 'jpg', 'jpeg', 'gif', 'bmp', 'mp4', 'avi', 'mov'}

# Create necessary directories
os.makedirs(UPLOAD_FOLDER, exist_ok=True)
os.makedirs(OUTPUT_FOLDER, exist_ok=True)
os.makedirs('templates', exist_ok=True)
os.makedirs('static/css', exist_ok=True)
os.makedirs('static/js', exist_ok=True)

def allowed_file(filename):
    """Check if file extension is allowed."""
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

def encode_image_to_base64(image_path):
    """Convert image to base64 for web display."""
    try:
        with open(image_path, "rb") as image_file:
            encoded_string = base64.b64encode(image_file.read()).decode()
        return f"data:image/jpeg;base64,{encoded_string}"
    except Exception as e:
        logger.error(f"Error encoding image: {e}")
        return None

@app.route('/')
def index():
    """Main page."""
    return render_template('index.html')

@app.route('/upload', methods=['POST'])
def upload_file():
    """Handle file upload and process for helmet detection."""
    try:
        if 'file' not in request.files:
            return jsonify({'error': 'No file provided'}), 400
        
        file = request.files['file']
        if file.filename == '':
            return jsonify({'error': 'No file selected'}), 400
        
        if file and allowed_file(file.filename):
            # Generate unique filename
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"{timestamp}_{file.filename}"
            filepath = os.path.join(UPLOAD_FOLDER, filename)
            
            # Save uploaded file
            file.save(filepath)
            logger.info(f"File uploaded: {filepath}")
            
            # Process the image
            output_filename = f"processed_{filename}"
            output_path = os.path.join(OUTPUT_FOLDER, output_filename)
            
            result = detector.process_image(filepath, output_path)
            
            if 'error' not in result:
                compliance = result['compliance']
                detections = result['detections']
                
                # Encode images for web display
                original_image_b64 = encode_image_to_base64(filepath)
                processed_image_b64 = encode_image_to_base64(output_path)
                
                response_data = {
                    'success': True,
                    'filename': filename,
                    'compliance': compliance,
                    'detections': detections,
                    'original_image': original_image_b64,
                    'processed_image': processed_image_b64,
                    'timestamp': timestamp
                }
                
                # Log violation if detected
                if not compliance['is_compliant']:
                    logger.warning(f"HELMET VIOLATION DETECTED in {filename}")
                    logger.warning(f"Compliance rate: {compliance['compliance_rate']:.2%}")
                
                return jsonify(response_data)
            else:
                return jsonify({'error': result['error']}), 500
        else:
            return jsonify({'error': 'Invalid file type'}), 400
            
    except Exception as e:
        logger.error(f"Error in upload_file: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/status')
def api_status():
    """API status endpoint."""
    return jsonify({
        'status': 'online',
        'model_loaded': detector.model is not None,
        'timestamp': datetime.now().isoformat()
    })

@app.route('/api/detect', methods=['POST'])
def api_detect():
    """API endpoint for helmet detection."""
    try:
        data = request.get_json()
        
        if 'image_base64' not in data:
            return jsonify({'error': 'No image data provided'}), 400
        
        # Decode base64 image
        image_data = base64.b64decode(data['image_base64'].split(',')[1])
        nparr = np.frombuffer(image_data, np.uint8)
        image = cv2.imdecode(nparr, cv2.IMREAD_COLOR)
        
        if image is None:
            return jsonify({'error': 'Invalid image data'}), 400
        
        # Detect objects
        detections = detector.detect_objects(image)
        compliance = detector.check_helmet_compliance(detections)
        
        return jsonify({
            'success': True,
            'detections': detections,
            'compliance': compliance,
            'timestamp': datetime.now().isoformat()
        })
        
    except Exception as e:
        logger.error(f"Error in api_detect: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/download/<filename>')
def download_file(filename):
    """Download processed files."""
    try:
        file_path = os.path.join(OUTPUT_FOLDER, filename)
        if os.path.exists(file_path):
            return send_file(file_path, as_attachment=True)
        else:
            return jsonify({'error': 'File not found'}), 404
    except Exception as e:
        return jsonify({'error': str(e)}), 500

if __name__ == '__main__':
    print("🌐 Starting Helmet Detection Web Server...")
    print("📍 Access the application at: http://localhost:5000")
    print("🔧 API endpoints:")
    print("   - GET  /api/status - Check system status")
    print("   - POST /api/detect - Detect helmets in base64 image")
    print("   - POST /upload - Upload and process image file")
    print("=" * 50)
    
    app.run(debug=True, host='0.0.0.0', port=5000)
