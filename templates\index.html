<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Helmet Detection System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .main-container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            margin: 20px auto;
            padding: 30px;
            max-width: 1200px;
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
            color: #333;
        }
        
        .header h1 {
            color: #4a5568;
            font-weight: 700;
            margin-bottom: 10px;
        }
        
        .upload-area {
            border: 3px dashed #cbd5e0;
            border-radius: 15px;
            padding: 40px;
            text-align: center;
            background: #f8f9fa;
            transition: all 0.3s ease;
            cursor: pointer;
        }
        
        .upload-area:hover {
            border-color: #667eea;
            background: #e6f3ff;
        }
        
        .upload-area.dragover {
            border-color: #667eea;
            background: #e6f3ff;
            transform: scale(1.02);
        }
        
        .results-section {
            margin-top: 30px;
            display: none;
        }
        
        .compliance-badge {
            font-size: 1.2em;
            padding: 10px 20px;
            border-radius: 25px;
            font-weight: bold;
        }
        
        .compliance-good {
            background: #d4edda;
            color: #155724;
            border: 2px solid #c3e6cb;
        }
        
        .compliance-bad {
            background: #f8d7da;
            color: #721c24;
            border: 2px solid #f5c6cb;
        }
        
        .image-container {
            position: relative;
            margin: 15px 0;
        }
        
        .image-container img {
            max-width: 100%;
            height: auto;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }
        
        .detection-stats {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
        }
        
        .stat-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid #e9ecef;
        }
        
        .stat-item:last-child {
            border-bottom: none;
        }
        
        .loading {
            display: none;
            text-align: center;
            padding: 20px;
        }
        
        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #667eea;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto 15px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .violation-alert {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
            padding: 15px;
            border-radius: 10px;
            margin: 15px 0;
        }
        
        .success-alert {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
            padding: 15px;
            border-radius: 10px;
            margin: 15px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="main-container">
            <div class="header">
                <h1><i class="fas fa-hard-hat"></i> Helmet Detection System</h1>
                <p class="lead">AI-Powered Safety Compliance Monitoring</p>
                <p class="text-muted">Upload images or videos to detect helmet compliance using advanced ML</p>
            </div>
            
            <div class="row">
                <div class="col-md-12">
                    <div class="upload-area" id="uploadArea">
                        <i class="fas fa-cloud-upload-alt fa-3x text-muted mb-3"></i>
                        <h4>Drop files here or click to upload</h4>
                        <p class="text-muted">Supports: JPG, PNG, GIF, MP4, AVI, MOV</p>
                        <input type="file" id="fileInput" accept="image/*,video/*" style="display: none;">
                        <button class="btn btn-primary btn-lg mt-3" onclick="document.getElementById('fileInput').click()">
                            <i class="fas fa-upload"></i> Choose File
                        </button>
                    </div>
                </div>
            </div>
            
            <div class="loading" id="loading">
                <div class="spinner"></div>
                <h5>Processing image for helmet detection...</h5>
                <p class="text-muted">This may take a few seconds</p>
            </div>
            
            <div class="results-section" id="resultsSection">
                <div class="row">
                    <div class="col-md-6">
                        <h4><i class="fas fa-image"></i> Original Image</h4>
                        <div class="image-container">
                            <img id="originalImage" src="" alt="Original Image">
                        </div>
                    </div>
                    <div class="col-md-6">
                        <h4><i class="fas fa-search"></i> Detection Results</h4>
                        <div class="image-container">
                            <img id="processedImage" src="" alt="Processed Image">
                        </div>
                    </div>
                </div>
                
                <div class="row mt-4">
                    <div class="col-md-12">
                        <div id="complianceStatus"></div>
                        
                        <div class="detection-stats">
                            <h5><i class="fas fa-chart-bar"></i> Detection Statistics</h5>
                            <div class="stat-item">
                                <span><i class="fas fa-users"></i> Total Persons:</span>
                                <span id="totalPersons" class="badge bg-primary">0</span>
                            </div>
                            <div class="stat-item">
                                <span><i class="fas fa-hard-hat text-success"></i> With Helmets:</span>
                                <span id="withHelmets" class="badge bg-success">0</span>
                            </div>
                            <div class="stat-item">
                                <span><i class="fas fa-exclamation-triangle text-danger"></i> Without Helmets:</span>
                                <span id="withoutHelmets" class="badge bg-danger">0</span>
                            </div>
                            <div class="stat-item">
                                <span><i class="fas fa-percentage"></i> Compliance Rate:</span>
                                <span id="complianceRate" class="badge bg-info">0%</span>
                            </div>
                        </div>
                        
                        <div id="detectionDetails" class="mt-3"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        const uploadArea = document.getElementById('uploadArea');
        const fileInput = document.getElementById('fileInput');
        const loading = document.getElementById('loading');
        const resultsSection = document.getElementById('resultsSection');

        // Drag and drop functionality
        uploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadArea.classList.add('dragover');
        });

        uploadArea.addEventListener('dragleave', () => {
            uploadArea.classList.remove('dragover');
        });

        uploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
            const files = e.dataTransfer.files;
            if (files.length > 0) {
                handleFile(files[0]);
            }
        });

        uploadArea.addEventListener('click', () => {
            fileInput.click();
        });

        fileInput.addEventListener('change', (e) => {
            if (e.target.files.length > 0) {
                handleFile(e.target.files[0]);
            }
        });

        function handleFile(file) {
            if (!file) return;

            // Show loading
            loading.style.display = 'block';
            resultsSection.style.display = 'none';

            // Create FormData
            const formData = new FormData();
            formData.append('file', file);

            // Upload and process
            fetch('/upload', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                loading.style.display = 'none';
                
                if (data.success) {
                    displayResults(data);
                } else {
                    alert('Error: ' + (data.error || 'Unknown error'));
                }
            })
            .catch(error => {
                loading.style.display = 'none';
                console.error('Error:', error);
                alert('Error processing file: ' + error.message);
            });
        }

        function displayResults(data) {
            // Display images
            document.getElementById('originalImage').src = data.original_image;
            document.getElementById('processedImage').src = data.processed_image;

            // Display compliance status
            const compliance = data.compliance;
            const complianceStatus = document.getElementById('complianceStatus');
            
            if (compliance.is_compliant) {
                complianceStatus.innerHTML = `
                    <div class="success-alert">
                        <h5><i class="fas fa-check-circle"></i> COMPLIANCE: GOOD</h5>
                        <p>All detected persons are wearing helmets. Safety standards met!</p>
                    </div>
                `;
            } else {
                complianceStatus.innerHTML = `
                    <div class="violation-alert">
                        <h5><i class="fas fa-exclamation-triangle"></i> VIOLATION DETECTED!</h5>
                        <p>Some persons are not wearing helmets. Immediate action required!</p>
                    </div>
                `;
            }

            // Update statistics
            document.getElementById('totalPersons').textContent = compliance.total_persons;
            document.getElementById('withHelmets').textContent = compliance.persons_with_helmets;
            document.getElementById('withoutHelmets').textContent = compliance.persons_without_helmets;
            document.getElementById('complianceRate').textContent = (compliance.compliance_rate * 100).toFixed(1) + '%';

            // Display detection details
            const detectionDetails = document.getElementById('detectionDetails');
            if (data.detections.length > 0) {
                let detailsHtml = '<h6><i class="fas fa-list"></i> All Detections:</h6><ul class="list-group">';
                data.detections.forEach((detection, index) => {
                    const badgeClass = detection.class_name.includes('NO-') ? 'bg-danger' : 'bg-success';
                    detailsHtml += `
                        <li class="list-group-item d-flex justify-content-between align-items-center">
                            ${detection.class_name}
                            <span class="badge ${badgeClass}">${(detection.confidence * 100).toFixed(1)}%</span>
                        </li>
                    `;
                });
                detailsHtml += '</ul>';
                detectionDetails.innerHTML = detailsHtml;
            } else {
                detectionDetails.innerHTML = '<p class="text-muted">No objects detected.</p>';
            }

            // Show results
            resultsSection.style.display = 'block';
        }

        // Check API status on page load
        fetch('/api/status')
            .then(response => response.json())
            .then(data => {
                console.log('API Status:', data);
                if (!data.model_loaded) {
                    alert('Warning: ML model not loaded properly');
                }
            })
            .catch(error => {
                console.error('Error checking API status:', error);
            });
    </script>
</body>
</html>
