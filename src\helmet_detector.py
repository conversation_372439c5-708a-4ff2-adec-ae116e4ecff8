"""
Helmet Detection Module using YOLOv8
This module provides helmet detection functionality for the ESP32 camera project.
"""

import cv2
import numpy as np
from ultralytics import YOLO
import os
from typing import List, Tuple, Dict, Optional
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class HelmetDetector:
    """
    A class for detecting helmets and safety equipment in images using YOLOv8.
    """
    
    def __init__(self, model_path: str = "helmet_detection_model/models/best.pt"):
        """
        Initialize the helmet detector.
        
        Args:
            model_path (str): Path to the trained YOLOv8 model
        """
        self.model_path = model_path
        self.model = None
        self.class_names = [
            'Hardhat', 'Mask', 'NO-Hardhat', 'NO-Mask', 'NO-Safety Vest', 
            'Person', 'Safety Cone', 'Safety Vest', 'machinery', 'vehicle'
        ]
        self.load_model()
    
    def load_model(self):
        """Load the YOLOv8 model."""
        try:
            if os.path.exists(self.model_path):
                self.model = YOLO(self.model_path)
                logger.info(f"Model loaded successfully from {self.model_path}")
            else:
                # Fallback to pre-trained YOLOv8 model
                self.model = YOLO('yolov8n.pt')
                logger.warning(f"Custom model not found at {self.model_path}, using pre-trained YOLOv8n")
        except Exception as e:
            logger.error(f"Error loading model: {e}")
            raise
    
    def detect_objects(self, image: np.ndarray, confidence_threshold: float = 0.5) -> List[Dict]:
        """
        Detect objects in an image.
        
        Args:
            image (np.ndarray): Input image
            confidence_threshold (float): Minimum confidence for detections
            
        Returns:
            List[Dict]: List of detected objects with their properties
        """
        if self.model is None:
            raise ValueError("Model not loaded")
        
        try:
            # Run inference
            results = self.model(image, conf=confidence_threshold)
            
            detections = []
            for result in results:
                boxes = result.boxes
                if boxes is not None:
                    for box in boxes:
                        # Extract box coordinates
                        x1, y1, x2, y2 = box.xyxy[0].cpu().numpy()
                        confidence = box.conf[0].cpu().numpy()
                        class_id = int(box.cls[0].cpu().numpy())
                        class_name = self.class_names[class_id]
                        
                        detection = {
                            'bbox': [int(x1), int(y1), int(x2), int(y2)],
                            'confidence': float(confidence),
                            'class_id': class_id,
                            'class_name': class_name
                        }
                        detections.append(detection)
            
            return detections
        
        except Exception as e:
            logger.error(f"Error during detection: {e}")
            return []
    
    def check_helmet_compliance(self, detections: List[Dict]) -> Dict:
        """
        Check helmet compliance based on detections.
        
        Args:
            detections (List[Dict]): List of detected objects
            
        Returns:
            Dict: Compliance analysis results
        """
        persons = []
        helmets = []
        no_helmets = []
        
        for detection in detections:
            class_name = detection['class_name']
            if class_name == 'Person':
                persons.append(detection)
            elif class_name == 'Hardhat':
                helmets.append(detection)
            elif class_name == 'NO-Hardhat':
                no_helmets.append(detection)
        
        # Simple compliance check
        total_persons = len(persons)
        persons_with_helmets = len(helmets)
        persons_without_helmets = len(no_helmets)
        
        compliance_rate = 0.0
        if total_persons > 0:
            compliance_rate = persons_with_helmets / total_persons
        
        result = {
            'total_persons': total_persons,
            'persons_with_helmets': persons_with_helmets,
            'persons_without_helmets': persons_without_helmets,
            'compliance_rate': compliance_rate,
            'is_compliant': compliance_rate >= 0.8,  # 80% compliance threshold
            'violations': no_helmets
        }
        
        return result
    
    def draw_detections(self, image: np.ndarray, detections: List[Dict]) -> np.ndarray:
        """
        Draw detection boxes and labels on the image.
        
        Args:
            image (np.ndarray): Input image
            detections (List[Dict]): List of detected objects
            
        Returns:
            np.ndarray: Image with drawn detections
        """
        result_image = image.copy()
        
        # Define colors for different classes
        colors = {
            'Hardhat': (0, 255, 0),      # Green
            'NO-Hardhat': (0, 0, 255),   # Red
            'Person': (255, 0, 0),       # Blue
            'Mask': (255, 255, 0),       # Cyan
            'NO-Mask': (0, 255, 255),    # Yellow
            'Safety Vest': (255, 0, 255), # Magenta
            'NO-Safety Vest': (128, 0, 128), # Purple
            'Safety Cone': (255, 165, 0), # Orange
            'machinery': (128, 128, 128), # Gray
            'vehicle': (0, 128, 255)     # Light Blue
        }
        
        for detection in detections:
            bbox = detection['bbox']
            confidence = detection['confidence']
            class_name = detection['class_name']
            
            x1, y1, x2, y2 = bbox
            color = colors.get(class_name, (255, 255, 255))
            
            # Draw bounding box
            cv2.rectangle(result_image, (x1, y1), (x2, y2), color, 2)
            
            # Draw label
            label = f"{class_name}: {confidence:.2f}"
            label_size = cv2.getTextSize(label, cv2.FONT_HERSHEY_SIMPLEX, 0.5, 2)[0]
            cv2.rectangle(result_image, (x1, y1 - label_size[1] - 10), 
                         (x1 + label_size[0], y1), color, -1)
            cv2.putText(result_image, label, (x1, y1 - 5), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 2)
        
        return result_image
    
    def process_image(self, image_path: str, output_path: Optional[str] = None) -> Dict:
        """
        Process a single image for helmet detection.
        
        Args:
            image_path (str): Path to input image
            output_path (Optional[str]): Path to save output image
            
        Returns:
            Dict: Detection and compliance results
        """
        try:
            # Load image
            image = cv2.imread(image_path)
            if image is None:
                raise ValueError(f"Could not load image from {image_path}")
            
            # Detect objects
            detections = self.detect_objects(image)
            
            # Check compliance
            compliance = self.check_helmet_compliance(detections)
            
            # Draw detections
            result_image = self.draw_detections(image, detections)
            
            # Save output image if path provided
            if output_path:
                cv2.imwrite(output_path, result_image)
                logger.info(f"Output image saved to {output_path}")
            
            return {
                'detections': detections,
                'compliance': compliance,
                'image_shape': image.shape,
                'processed_image': result_image
            }
            
        except Exception as e:
            logger.error(f"Error processing image {image_path}: {e}")
            return {'error': str(e)}

if __name__ == "__main__":
    # Test the helmet detector
    detector = HelmetDetector()
    
    # Test with sample images from the model repository
    test_images = [
        "helmet_detection_model/source_files/construction-safety.jpg",
        "helmet_detection_model/source_files/portrait-of-woman-with-mask-and-man-with-safety-glasses-on-a-construction-HX01FH.jpg"
    ]
    
    for image_path in test_images:
        if os.path.exists(image_path):
            print(f"\nProcessing: {image_path}")
            result = detector.process_image(image_path, f"output_{os.path.basename(image_path)}")
            
            if 'error' not in result:
                compliance = result['compliance']
                print(f"Total persons: {compliance['total_persons']}")
                print(f"Persons with helmets: {compliance['persons_with_helmets']}")
                print(f"Persons without helmets: {compliance['persons_without_helmets']}")
                print(f"Compliance rate: {compliance['compliance_rate']:.2%}")
                print(f"Is compliant: {compliance['is_compliant']}")
            else:
                print(f"Error: {result['error']}")
