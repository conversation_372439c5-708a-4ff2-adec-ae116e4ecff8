"""
Helmet Detection Module using Gemini AI
This module provides helmet detection functionality for the ESP32 camera project.
"""

import cv2
import numpy as np
import os
from typing import List, Tuple, Dict, Optional
import logging
import google.generativeai as genai
from PIL import Image
import json
import re

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class HelmetDetector:
    """
    A class for detecting helmets and safety equipment in images using Gemini AI.
    """

    def __init__(self, api_key: str = "AIzaSyBntkJYuOFqBaUqhcDB1umoL34Kl3kkKEU"):
        """
        Initialize the helmet detector.

        Args:
            api_key (str): Gemini API key
        """
        self.api_key = api_key
        self.model = None
        self.load_model()
    
    def load_model(self):
        """Load the Gemini AI model."""
        try:
            genai.configure(api_key=self.api_key)
            self.model = genai.GenerativeModel('gemini-1.5-flash')
            logger.info("Gemini AI model loaded successfully")
        except Exception as e:
            logger.error(f"Error loading Gemini model: {e}")
            raise
    
    def detect_objects(self, image: np.ndarray, confidence_threshold: float = 0.5) -> List[Dict]:
        """
        Detect objects in an image using Gemini AI.

        Args:
            image (np.ndarray): Input image
            confidence_threshold (float): Minimum confidence for detections

        Returns:
            List[Dict]: List of detected objects with their properties
        """
        if self.model is None:
            raise ValueError("Model not loaded")

        try:
            # Convert OpenCV image to PIL Image
            image_rgb = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
            pil_image = Image.fromarray(image_rgb)

            # Create prompt for helmet detection
            prompt = """
            Analyze this image for safety helmet detection. Please provide a detailed JSON response with the following structure:
            {
                "people_detected": number_of_people,
                "helmets_detected": number_of_people_with_helmets,
                "no_helmet_detected": number_of_people_without_helmets,
                "confidence": "high/medium/low",
                "description": "description of what you see",
                "detections": [
                    {
                        "class_name": "Person" or "Hardhat" or "NO-Hardhat",
                        "confidence": 0.95,
                        "bbox": [x1, y1, x2, y2]
                    }
                ]
            }

            Look carefully for:
            - People in the image
            - Whether they are wearing safety helmets/hard hats
            - Estimate bounding box coordinates for each detection
            - Provide confidence scores

            Return ONLY the JSON response, no additional text.
            """

            # Generate response
            response = self.model.generate_content([prompt, pil_image])

            # Parse JSON response
            response_text = response.text.strip()

            # Extract JSON from response (in case there's extra text)
            json_match = re.search(r'\{.*\}', response_text, re.DOTALL)
            if json_match:
                json_str = json_match.group()
                result = json.loads(json_str)

                # Convert to our expected format
                detections = []
                if 'detections' in result:
                    for det in result['detections']:
                        detection = {
                            'bbox': det.get('bbox', [0, 0, 100, 100]),
                            'confidence': det.get('confidence', 0.8),
                            'class_name': det.get('class_name', 'Person')
                        }
                        detections.append(detection)

                return detections
            else:
                logger.warning("Could not parse JSON from Gemini response")
                return []

        except Exception as e:
            logger.error(f"Error during Gemini detection: {e}")
            return []
    
    def check_helmet_compliance(self, detections: List[Dict]) -> Dict:
        """
        Check helmet compliance based on detections.

        Args:
            detections (List[Dict]): List of detected objects

        Returns:
            Dict: Compliance analysis results
        """
        persons = []
        helmets = []
        no_helmets = []

        for detection in detections:
            class_name = detection['class_name']
            if class_name == 'Person':
                persons.append(detection)
            elif class_name == 'Hardhat':
                helmets.append(detection)
            elif class_name == 'NO-Hardhat':
                no_helmets.append(detection)

        # Calculate compliance
        total_persons = max(len(persons), len(helmets) + len(no_helmets))
        persons_with_helmets = len(helmets)
        persons_without_helmets = len(no_helmets)

        compliance_rate = 0.0
        if total_persons > 0:
            compliance_rate = persons_with_helmets / total_persons

        result = {
            'total_persons': total_persons,
            'persons_with_helmets': persons_with_helmets,
            'persons_without_helmets': persons_without_helmets,
            'compliance_rate': compliance_rate,
            'is_compliant': compliance_rate >= 0.8,  # 80% compliance threshold
            'violations': no_helmets
        }

        return result
    
    def draw_detections(self, image: np.ndarray, detections: List[Dict]) -> np.ndarray:
        """
        Draw detection boxes and labels on the image.
        
        Args:
            image (np.ndarray): Input image
            detections (List[Dict]): List of detected objects
            
        Returns:
            np.ndarray: Image with drawn detections
        """
        result_image = image.copy()
        
        # Define colors for different classes
        colors = {
            'Hardhat': (0, 255, 0),      # Green
            'NO-Hardhat': (0, 0, 255),   # Red
            'Person': (255, 0, 0),       # Blue
            'Mask': (255, 255, 0),       # Cyan
            'NO-Mask': (0, 255, 255),    # Yellow
            'Safety Vest': (255, 0, 255), # Magenta
            'NO-Safety Vest': (128, 0, 128), # Purple
            'Safety Cone': (255, 165, 0), # Orange
            'machinery': (128, 128, 128), # Gray
            'vehicle': (0, 128, 255)     # Light Blue
        }
        
        for detection in detections:
            bbox = detection['bbox']
            confidence = detection['confidence']
            class_name = detection['class_name']
            
            x1, y1, x2, y2 = bbox
            color = colors.get(class_name, (255, 255, 255))
            
            # Draw bounding box
            cv2.rectangle(result_image, (x1, y1), (x2, y2), color, 2)
            
            # Draw label
            label = f"{class_name}: {confidence:.2f}"
            label_size = cv2.getTextSize(label, cv2.FONT_HERSHEY_SIMPLEX, 0.5, 2)[0]
            cv2.rectangle(result_image, (x1, y1 - label_size[1] - 10), 
                         (x1 + label_size[0], y1), color, -1)
            cv2.putText(result_image, label, (x1, y1 - 5), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 2)
        
        return result_image
    
    def process_image(self, image_path: str, output_path: Optional[str] = None) -> Dict:
        """
        Process a single image for helmet detection using Gemini AI.

        Args:
            image_path (str): Path to input image
            output_path (Optional[str]): Path to save output image

        Returns:
            Dict: Detection and compliance results
        """
        try:
            # Load image
            image = cv2.imread(image_path)
            if image is None:
                raise ValueError(f"Could not load image from {image_path}")

            # Use Gemini for detection
            pil_image = Image.open(image_path)

            prompt = """
            Analyze this image for safety helmet detection. Please provide a detailed JSON response:
            {
                "people_detected": number_of_people,
                "helmets_detected": number_of_people_with_helmets,
                "no_helmet_detected": number_of_people_without_helmets,
                "confidence": "high/medium/low",
                "description": "description of what you see"
            }

            Look carefully for people and whether they are wearing safety helmets/hard hats.
            Return ONLY the JSON response, no additional text.
            """

            response = self.model.generate_content([prompt, pil_image])
            response_text = response.text.strip()

            # Parse the response
            json_match = re.search(r'\{.*\}', response_text, re.DOTALL)
            if json_match:
                json_str = json_match.group()
                gemini_result = json.loads(json_str)

                # Convert to our detection format
                detections = []

                # Create mock detections based on Gemini analysis
                people_count = gemini_result.get('people_detected', 0)
                helmets_count = gemini_result.get('helmets_detected', 0)
                no_helmet_count = gemini_result.get('no_helmet_detected', 0)

                # Add person detections
                for i in range(people_count):
                    detections.append({
                        'bbox': [50 + i*100, 50, 150 + i*100, 200],
                        'confidence': 0.9,
                        'class_name': 'Person'
                    })

                # Add helmet detections
                for i in range(helmets_count):
                    detections.append({
                        'bbox': [50 + i*100, 50, 150 + i*100, 100],
                        'confidence': 0.9,
                        'class_name': 'Hardhat'
                    })

                # Add no-helmet detections
                for i in range(no_helmet_count):
                    detections.append({
                        'bbox': [50 + (helmets_count + i)*100, 50, 150 + (helmets_count + i)*100, 100],
                        'confidence': 0.9,
                        'class_name': 'NO-Hardhat'
                    })
            else:
                detections = []

            # Check compliance
            compliance = self.check_helmet_compliance(detections)

            # Draw detections
            result_image = self.draw_detections(image, detections)

            # Save output image if path provided
            if output_path:
                cv2.imwrite(output_path, result_image)
                logger.info(f"Output image saved to {output_path}")

            return {
                'detections': detections,
                'compliance': compliance,
                'image_shape': image.shape,
                'processed_image': result_image,
                'gemini_analysis': gemini_result if 'gemini_result' in locals() else None
            }

        except Exception as e:
            logger.error(f"Error processing image {image_path}: {e}")
            return {'error': str(e)}

if __name__ == "__main__":
    # Test the helmet detector
    detector = HelmetDetector()
    
    # Test with sample images from the model repository
    test_images = [
        "helmet_detection_model/source_files/construction-safety.jpg",
        "helmet_detection_model/source_files/portrait-of-woman-with-mask-and-man-with-safety-glasses-on-a-construction-HX01FH.jpg"
    ]
    
    for image_path in test_images:
        if os.path.exists(image_path):
            print(f"\nProcessing: {image_path}")
            result = detector.process_image(image_path, f"output_{os.path.basename(image_path)}")
            
            if 'error' not in result:
                compliance = result['compliance']
                print(f"Total persons: {compliance['total_persons']}")
                print(f"Persons with helmets: {compliance['persons_with_helmets']}")
                print(f"Persons without helmets: {compliance['persons_without_helmets']}")
                print(f"Compliance rate: {compliance['compliance_rate']:.2%}")
                print(f"Is compliant: {compliance['is_compliant']}")
            else:
                print(f"Error: {result['error']}")
