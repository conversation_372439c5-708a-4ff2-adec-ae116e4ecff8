{"auth": {"oauth2": {"scopes": {"https://www.googleapis.com/auth/cloud-platform": {"description": "See, edit, configure, and delete your Google Cloud data and see the email address for your Google Account."}}}}, "basePath": "", "baseUrl": "https://backupdr.googleapis.com/", "batchPath": "batch", "canonicalName": "Backupdr", "description": "", "discoveryVersion": "v1", "documentationLink": "https://cloud.google.com/backup-disaster-recovery", "fullyEncodeReservedExpansion": true, "icons": {"x16": "http://www.google.com/images/icons/product/search-16.gif", "x32": "http://www.google.com/images/icons/product/search-32.gif"}, "id": "backupdr:v1", "kind": "discovery#restDescription", "mtlsRootUrl": "https://backupdr.mtls.googleapis.com/", "name": "backupdr", "ownerDomain": "google.com", "ownerName": "Google", "parameters": {"$.xgafv": {"description": "V1 error format.", "enum": ["1", "2"], "enumDescriptions": ["v1 error format", "v2 error format"], "location": "query", "type": "string"}, "access_token": {"description": "OAuth access token.", "location": "query", "type": "string"}, "alt": {"default": "json", "description": "Data format for response.", "enum": ["json", "media", "proto"], "enumDescriptions": ["Responses with Content-Type of application/json", "Media download with context-dependent Content-Type", "Responses with Content-Type of application/x-protobuf"], "location": "query", "type": "string"}, "callback": {"description": "JSONP", "location": "query", "type": "string"}, "fields": {"description": "Selector specifying which fields to include in a partial response.", "location": "query", "type": "string"}, "key": {"description": "API key. Your API key identifies your project and provides you with API access, quota, and reports. Required unless you provide an OAuth 2.0 token.", "location": "query", "type": "string"}, "oauth_token": {"description": "OAuth 2.0 token for the current user.", "location": "query", "type": "string"}, "prettyPrint": {"default": "true", "description": "Returns response with indentations and line breaks.", "location": "query", "type": "boolean"}, "quotaUser": {"description": "Available to use for quota purposes for server-side applications. Can be any arbitrary string assigned to a user, but should not exceed 40 characters.", "location": "query", "type": "string"}, "uploadType": {"description": "Legacy upload protocol for media (e.g. \"media\", \"multipart\").", "location": "query", "type": "string"}, "upload_protocol": {"description": "Upload protocol for media (e.g. \"raw\", \"multipart\").", "location": "query", "type": "string"}}, "protocol": "rest", "resources": {"projects": {"resources": {"locations": {"methods": {"get": {"description": "Gets information about a location.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}", "httpMethod": "GET", "id": "backupdr.projects.locations.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Resource name for the location.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "Location"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists information about the supported locations for this service.", "flatPath": "v1/projects/{projectsId}/locations", "httpMethod": "GET", "id": "backupdr.projects.locations.list", "parameterOrder": ["name"], "parameters": {"extraLocationTypes": {"description": "Optional. A list of extra location types that should be used as conditions for controlling the visibility of the locations.", "location": "query", "repeated": true, "type": "string"}, "filter": {"description": "A filter to narrow down results to a preferred subset. The filtering language accepts strings like `\"displayName=tokyo\"`, and is documented in more detail in [AIP-160](https://google.aip.dev/160).", "location": "query", "type": "string"}, "name": {"description": "The resource that owns the locations collection, if applicable.", "location": "path", "pattern": "^projects/[^/]+$", "required": true, "type": "string"}, "pageSize": {"description": "The maximum number of results to return. If not set, the service selects a default.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "A page token received from the `next_page_token` field in the response. Send that page token to receive the subsequent page.", "location": "query", "type": "string"}}, "path": "v1/{+name}/locations", "response": {"$ref": "ListLocationsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}, "resources": {"backupPlanAssociations": {"methods": {"create": {"description": "Create a BackupPlanAssociation", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/backupPlanAssociations", "httpMethod": "POST", "id": "backupdr.projects.locations.backupPlanAssociations.create", "parameterOrder": ["parent"], "parameters": {"backupPlanAssociationId": {"description": "Required. The name of the backup plan association to create. The name must be unique for the specified project and location.", "location": "query", "type": "string"}, "parent": {"description": "Required. The backup plan association project and location in the format `projects/{project_id}/locations/{location}`. In Cloud BackupDR locations map to GCP regions, for example **us-central1**.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "Optional. An optional request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server will know to ignore the request if it has already been completed. The server will guarantee that for at least 60 minutes since the first request. For example, consider a situation where you make an initial request and t he request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).", "location": "query", "type": "string"}}, "path": "v1/{+parent}/backupPlanAssociations", "request": {"$ref": "BackupPlanAssociation"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Deletes a single BackupPlanAssociation.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/backupPlanAssociations/{backupPlanAssociationsId}", "httpMethod": "DELETE", "id": "backupdr.projects.locations.backupPlanAssociations.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. Name of the backup plan association resource, in the format `projects/{project}/locations/{location}/backupPlanAssociations/{backupPlanAssociationId}`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/backupPlanAssociations/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "Optional. An optional request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server will know to ignore the request if it has already been completed. The server will guarantee that for at least 60 minutes after the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).", "location": "query", "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "fetchForResourceType": {"description": "List BackupPlanAssociations for a given resource type.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/backupPlanAssociations:fetchForResourceType", "httpMethod": "GET", "id": "backupdr.projects.locations.backupPlanAssociations.fetchForResourceType", "parameterOrder": ["parent"], "parameters": {"filter": {"description": "Optional. A filter expression that filters the results fetched in the response. The expression must specify the field name, a comparison operator, and the value that you want to use for filtering. Supported fields: * resource * backup_plan * state * data_source * cloud_sql_instance_backup_plan_association_properties.instance_create_time", "location": "query", "type": "string"}, "orderBy": {"description": "Optional. A comma-separated list of fields to order by, sorted in ascending order. Use \"desc\" after a field name for descending. Supported fields: * name", "location": "query", "type": "string"}, "pageSize": {"description": "Optional. The maximum number of BackupPlanAssociations to return. The service may return fewer than this value. If unspecified, at most 50 BackupPlanAssociations will be returned. The maximum value is 100; values above 100 will be coerced to 100.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Optional. A page token, received from a previous call of `FetchBackupPlanAssociationsForResourceType`. Provide this to retrieve the subsequent page. When paginating, all other parameters provided to `FetchBackupPlanAssociationsForResourceType` must match the call that provided the page token.", "location": "query", "type": "string"}, "parent": {"description": "Required. The parent resource name. Format: projects/{project}/locations/{location}", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}, "resourceType": {"description": "Required. The type of the GCP resource. Ex: sql.googleapis.com/Instance", "location": "query", "type": "string"}}, "path": "v1/{+parent}/backupPlanAssociations:fetchForResourceType", "response": {"$ref": "FetchBackupPlanAssociationsForResourceTypeResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Gets details of a single BackupPlanAssociation.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/backupPlanAssociations/{backupPlanAssociationsId}", "httpMethod": "GET", "id": "backupdr.projects.locations.backupPlanAssociations.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. Name of the backup plan association resource, in the format `projects/{project}/locations/{location}/backupPlanAssociations/{backupPlanAssociationId}`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/backupPlanAssociations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "BackupPlanAssociation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists BackupPlanAssociations in a given project and location.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/backupPlanAssociations", "httpMethod": "GET", "id": "backupdr.projects.locations.backupPlanAssociations.list", "parameterOrder": ["parent"], "parameters": {"filter": {"description": "Optional. Filtering results", "location": "query", "type": "string"}, "pageSize": {"description": "Optional. Requested page size. Server may return fewer items than requested. If unspecified, server will pick an appropriate default.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Optional. A token identifying a page of results the server should return.", "location": "query", "type": "string"}, "parent": {"description": "Required. The project and location for which to retrieve backup Plan Associations information, in the format `projects/{project_id}/locations/{location}`. In Cloud BackupDR, locations map to GCP regions, for example **us-central1**. To retrieve backup plan associations for all locations, use \"-\" for the `{location}` value.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/backupPlanAssociations", "response": {"$ref": "ListBackupPlanAssociationsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "patch": {"description": "Update a BackupPlanAssociation.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/backupPlanAssociations/{backupPlanAssociationsId}", "httpMethod": "PATCH", "id": "backupdr.projects.locations.backupPlanAssociations.patch", "parameterOrder": ["name"], "parameters": {"name": {"description": "Output only. Identifier. The resource name of BackupPlanAssociation in below format Format : projects/{project}/locations/{location}/backupPlanAssociations/{backupPlanAssociationId}", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/backupPlanAssociations/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "Optional. An optional request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server will know to ignore the request if it has already been completed. The server will guarantee that for at least 60 minutes since the first request. For example, consider a situation where you make an initial request and t he request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).", "location": "query", "type": "string"}, "updateMask": {"description": "Required. The list of fields to update. Field mask is used to specify the fields to be overwritten in the BackupPlanAssociation resource by the update. The fields specified in the update_mask are relative to the resource, not the full request. A field will be overwritten if it is in the mask. If the user does not provide a mask then the request will fail. Currently backup_plan_association.backup_plan is the only supported field.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1/{+name}", "request": {"$ref": "BackupPlanAssociation"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "triggerBackup": {"description": "Triggers a new Backup.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/backupPlanAssociations/{backupPlanAssociationsId}:triggerBackup", "httpMethod": "POST", "id": "backupdr.projects.locations.backupPlanAssociations.triggerBackup", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. Name of the backup plan association resource, in the format `projects/{project}/locations/{location}/backupPlanAssociations/{backupPlanAssociationId}`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/backupPlanAssociations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}:triggerBackup", "request": {"$ref": "TriggerBackupRequest"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}, "backupPlans": {"methods": {"create": {"description": "Create a BackupPlan", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/backupPlans", "httpMethod": "POST", "id": "backupdr.projects.locations.backupPlans.create", "parameterOrder": ["parent"], "parameters": {"backupPlanId": {"description": "Required. The name of the `BackupPlan` to create. The name must be unique for the specified project and location.The name must start with a lowercase letter followed by up to 62 lowercase letters, numbers, or hyphens. Pattern, /a-z{,62}/.", "location": "query", "type": "string"}, "parent": {"description": "Required. The `BackupPlan` project and location in the format `projects/{project}/locations/{location}`. In Cloud BackupDR locations map to GCP regions, for example **us-central1**.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "Optional. An optional request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server will know to ignore the request if it has already been completed. The server will guarantee that for at least 60 minutes since the first request. For example, consider a situation where you make an initial request and t he request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).", "location": "query", "type": "string"}}, "path": "v1/{+parent}/backupPlans", "request": {"$ref": "BackupPlan"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Deletes a single BackupPlan.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/backupPlans/{backupPlansId}", "httpMethod": "DELETE", "id": "backupdr.projects.locations.backupPlans.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The resource name of the `BackupPlan` to delete. Format: `projects/{project}/locations/{location}/backupPlans/{backup_plan}`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/backupPlans/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "Optional. An optional request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server will know to ignore the request if it has already been completed. The server will guarantee that for at least 60 minutes after the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).", "location": "query", "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Gets details of a single BackupPlan.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/backupPlans/{backupPlansId}", "httpMethod": "GET", "id": "backupdr.projects.locations.backupPlans.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The resource name of the `BackupPlan` to retrieve. Format: `projects/{project}/locations/{location}/backupPlans/{backup_plan}`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/backupPlans/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "BackupPlan"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists BackupPlans in a given project and location.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/backupPlans", "httpMethod": "GET", "id": "backupdr.projects.locations.backupPlans.list", "parameterOrder": ["parent"], "parameters": {"filter": {"description": "Optional. Field match expression used to filter the results.", "location": "query", "type": "string"}, "orderBy": {"description": "Optional. Field by which to sort the results.", "location": "query", "type": "string"}, "pageSize": {"description": "Optional. The maximum number of `BackupPlans` to return in a single response. If not specified, a default value will be chosen by the service. Note that the response may include a partial list and a caller should only rely on the response's next_page_token to determine if there are more instances left to be queried.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Optional. The value of next_page_token received from a previous `ListBackupPlans` call. Provide this to retrieve the subsequent page in a multi-page list of results. When paginating, all other parameters provided to `ListBackupPlans` must match the call that provided the page token.", "location": "query", "type": "string"}, "parent": {"description": "Required. The project and location for which to retrieve `BackupPlans` information. Format: `projects/{project}/locations/{location}`. In Cloud BackupDR, locations map to GCP regions, for e.g. **us-central1**. To retrieve backup plans for all locations, use \"-\" for the `{location}` value.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/backupPlans", "response": {"$ref": "ListBackupPlansResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "patch": {"description": "Update a BackupPlan.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/backupPlans/{backupPlansId}", "httpMethod": "PATCH", "id": "backupdr.projects.locations.backupPlans.patch", "parameterOrder": ["name"], "parameters": {"name": {"description": "Output only. Identifier. The resource name of the `BackupPlan`. Format: `projects/{project}/locations/{location}/backupPlans/{backup_plan}`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/backupPlans/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "Optional. An optional request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server will know to ignore the request if it has already been completed. The server will guarantee that for at least 60 minutes since the first request. For example, consider a situation where you make an initial request and t he request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).", "location": "query", "type": "string"}, "updateMask": {"description": "Required. The list of fields to update. Field mask is used to specify the fields to be overwritten in the BackupPlan resource by the update. The fields specified in the update_mask are relative to the resource, not the full request. A field will be overwritten if it is in the mask. If the user does not provide a mask then the request will fail. Currently, these fields are supported in update: description, schedules, retention period, adding and removing Backup Rules.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1/{+name}", "request": {"$ref": "BackupPlan"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}, "resources": {"revisions": {"methods": {"get": {"description": "Gets details of a single BackupPlanRevision.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/backupPlans/{backupPlansId}/revisions/{revisionsId}", "httpMethod": "GET", "id": "backupdr.projects.locations.backupPlans.revisions.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The resource name of the `BackupPlanRevision` to retrieve. Format: `projects/{project}/locations/{location}/backupPlans/{backup_plan}/revisions/{revision}`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/backupPlans/[^/]+/revisions/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "BackupPlanRevision"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists BackupPlanRevisions in a given project and location.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/backupPlans/{backupPlansId}/revisions", "httpMethod": "GET", "id": "backupdr.projects.locations.backupPlans.revisions.list", "parameterOrder": ["parent"], "parameters": {"pageSize": {"description": "Optional. The maximum number of `BackupPlans` to return in a single response. If not specified, a default value will be chosen by the service. Note that the response may include a partial list and a caller should only rely on the response's next_page_token to determine if there are more instances left to be queried.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Optional. The value of next_page_token received from a previous `ListBackupPlans` call. Provide this to retrieve the subsequent page in a multi-page list of results. When paginating, all other parameters provided to `ListBackupPlans` must match the call that provided the page token.", "location": "query", "type": "string"}, "parent": {"description": "Required. The project and location for which to retrieve `BackupPlanRevisions` information. Format: `projects/{project}/locations/{location}/backupPlans/{backup_plan}`. In Cloud BackupDR, locations map to GCP regions, for e.g. **us-central1**.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/backupPlans/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/revisions", "response": {"$ref": "ListBackupPlanRevisionsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}}}, "backupVaults": {"methods": {"create": {"description": "Creates a new BackupVault in a given project and location.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/backupVaults", "httpMethod": "POST", "id": "backupdr.projects.locations.backupVaults.create", "parameterOrder": ["parent"], "parameters": {"backupVaultId": {"description": "Required. ID of the requesting object If auto-generating ID server-side, remove this field and backup_vault_id from the method_signature of Create RPC", "location": "query", "type": "string"}, "parent": {"description": "Required. Value for parent.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "Optional. An optional request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server will know to ignore the request if it has already been completed. The server will guarantee that for at least 60 minutes since the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).", "location": "query", "type": "string"}, "validateOnly": {"description": "Optional. Only validate the request, but do not perform mutations. The default is 'false'.", "location": "query", "type": "boolean"}}, "path": "v1/{+parent}/backupVaults", "request": {"$ref": "<PERSON><PERSON><PERSON><PERSON>"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Deletes a BackupVault.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/backupVaults/{backupVaultsId}", "httpMethod": "DELETE", "id": "backupdr.projects.locations.backupVaults.delete", "parameterOrder": ["name"], "parameters": {"allowMissing": {"description": "Optional. If true and the BackupVault is not found, the request will succeed but no action will be taken.", "location": "query", "type": "boolean"}, "etag": {"description": "The current etag of the backup vault. If an etag is provided and does not match the current etag of the connection, deletion will be blocked.", "location": "query", "type": "string"}, "force": {"description": "Optional. If set to true, any data source from this backup vault will also be deleted.", "location": "query", "type": "boolean"}, "ignoreBackupPlanReferences": {"description": "Optional. If set to true, backupvault deletion will proceed even if there are backup plans referencing the backupvault. The default is 'false'.", "location": "query", "type": "boolean"}, "name": {"description": "Required. Name of the resource.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/backupVaults/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "Optional. An optional request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server will know to ignore the request if it has already been completed. The server will guarantee that for at least 60 minutes after the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).", "location": "query", "type": "string"}, "validateOnly": {"description": "Optional. Only validate the request, but do not perform mutations. The default is 'false'.", "location": "query", "type": "boolean"}}, "path": "v1/{+name}", "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "fetchUsable": {"description": "FetchUsableBackupVaults lists usable BackupVaults in a given project and location. Usable BackupVault are the ones that user has backupdr.backupVaults.get permission.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/backupVaults:fetchUsable", "httpMethod": "GET", "id": "backupdr.projects.locations.backupVaults.fetchUsable", "parameterOrder": ["parent"], "parameters": {"filter": {"description": "Optional. Filtering results.", "location": "query", "type": "string"}, "orderBy": {"description": "Optional. Hint for how to order the results.", "location": "query", "type": "string"}, "pageSize": {"description": "Optional. Requested page size. Server may return fewer items than requested. If unspecified, server will pick an appropriate default.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Optional. A token identifying a page of results the server should return.", "location": "query", "type": "string"}, "parent": {"description": "Required. The project and location for which to retrieve backupvault stores information, in the format 'projects/{project_id}/locations/{location}'. In Cloud Backup and DR, locations map to Google Cloud regions, for example **us-central1**. To retrieve backupvault stores for all locations, use \"-\" for the '{location}' value.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/backupVaults:fetchUsable", "response": {"$ref": "FetchUsableBackupVaultsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Gets details of a BackupVault.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/backupVaults/{backupVaultsId}", "httpMethod": "GET", "id": "backupdr.projects.locations.backupVaults.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. Name of the backupvault store resource name, in the format 'projects/{project_id}/locations/{location}/backupVaults/{resource_name}'", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/backupVaults/[^/]+$", "required": true, "type": "string"}, "view": {"description": "Optional. Reserved for future use to provide a BASIC & FULL view of Backup Vault", "enum": ["BACKUP_VAULT_VIEW_UNSPECIFIED", "BACKUP_VAULT_VIEW_BASIC", "BACKUP_VAULT_VIEW_FULL"], "enumDescriptions": ["If the value is not set, the default 'FULL' view is used.", "Includes basic data about the Backup Vault, but not the full contents.", "Includes all data about the Backup Vault. This is the default value (for both ListBackupVaults and GetBackupVault)."], "location": "query", "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "<PERSON><PERSON><PERSON><PERSON>"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists BackupVaults in a given project and location.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/backupVaults", "httpMethod": "GET", "id": "backupdr.projects.locations.backupVaults.list", "parameterOrder": ["parent"], "parameters": {"filter": {"description": "Optional. Filtering results.", "location": "query", "type": "string"}, "orderBy": {"description": "Optional. Hint for how to order the results.", "location": "query", "type": "string"}, "pageSize": {"description": "Optional. Requested page size. Server may return fewer items than requested. If unspecified, server will pick an appropriate default.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Optional. A token identifying a page of results the server should return.", "location": "query", "type": "string"}, "parent": {"description": "Required. The project and location for which to retrieve backupvault stores information, in the format 'projects/{project_id}/locations/{location}'. In Cloud Backup and DR, locations map to Google Cloud regions, for example **us-central1**. To retrieve backupvault stores for all locations, use \"-\" for the '{location}' value.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}, "view": {"description": "Optional. Reserved for future use to provide a BASIC & FULL view of Backup Vault.", "enum": ["BACKUP_VAULT_VIEW_UNSPECIFIED", "BACKUP_VAULT_VIEW_BASIC", "BACKUP_VAULT_VIEW_FULL"], "enumDescriptions": ["If the value is not set, the default 'FULL' view is used.", "Includes basic data about the Backup Vault, but not the full contents.", "Includes all data about the Backup Vault. This is the default value (for both ListBackupVaults and GetBackupVault)."], "location": "query", "type": "string"}}, "path": "v1/{+parent}/backupVaults", "response": {"$ref": "ListBackupVaultsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "patch": {"description": "Updates the settings of a BackupVault.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/backupVaults/{backupVaultsId}", "httpMethod": "PATCH", "id": "backupdr.projects.locations.backupVaults.patch", "parameterOrder": ["name"], "parameters": {"force": {"description": "Optional. If set to true, will not check plan duration against backup vault enforcement duration.", "location": "query", "type": "boolean"}, "forceUpdateAccessRestriction": {"description": "Optional. If set to true, we will force update access restriction even if some non compliant data sources are present. The default is 'false'.", "location": "query", "type": "boolean"}, "name": {"description": "Output only. Identifier. Name of the backup vault to create. It must have the format`\"projects/{project}/locations/{location}/backupVaults/{backupvault}\"`. `{backupvault}` cannot be changed after creation. It must be between 3-63 characters long and must be unique within the project and location.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/backupVaults/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "Optional. An optional request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server will know to ignore the request if it has already been completed. The server will guarantee that for at least 60 minutes since the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).", "location": "query", "type": "string"}, "updateMask": {"description": "Required. Field mask is used to specify the fields to be overwritten in the BackupVault resource by the update. The fields specified in the update_mask are relative to the resource, not the full request. A field will be overwritten if it is in the mask. If the user does not provide a mask then the request will fail.", "format": "google-fieldmask", "location": "query", "type": "string"}, "validateOnly": {"description": "Optional. Only validate the request, but do not perform mutations. The default is 'false'.", "location": "query", "type": "boolean"}}, "path": "v1/{+name}", "request": {"$ref": "<PERSON><PERSON><PERSON><PERSON>"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "testIamPermissions": {"description": "Returns the caller's permissions on a BackupVault resource. A caller is not required to have Google IAM permission to make this request.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/backupVaults/{backupVaultsId}:testIamPermissions", "httpMethod": "POST", "id": "backupdr.projects.locations.backupVaults.testIamPermissions", "parameterOrder": ["resource"], "parameters": {"resource": {"description": "REQUIRED: The resource for which the policy detail is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/backupVaults/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+resource}:testIamPermissions", "request": {"$ref": "TestIamPermissionsRequest"}, "response": {"$ref": "TestIamPermissionsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}, "resources": {"dataSources": {"methods": {"abandonBackup": {"description": "Internal only. Abandons a backup.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/backupVaults/{backupVaultsId}/dataSources/{dataSourcesId}:abandonBackup", "httpMethod": "POST", "id": "backupdr.projects.locations.backupVaults.dataSources.abandonBackup", "parameterOrder": ["dataSource"], "parameters": {"dataSource": {"description": "Required. The resource name of the instance, in the format 'projects/*/locations/*/backupVaults/*/dataSources/'.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/backupVaults/[^/]+/dataSources/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+dataSource}:abandonBackup", "request": {"$ref": "AbandonBackupRequest"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "fetchAccessToken": {"description": "Internal only. Fetch access token for a given data source.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/backupVaults/{backupVaultsId}/dataSources/{dataSourcesId}:fetchAccessToken", "httpMethod": "POST", "id": "backupdr.projects.locations.backupVaults.dataSources.fetchAccessToken", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The resource name for the location for which static IPs should be returned. Must be in the format 'projects/*/locations/*/backupVaults/*/dataSources'.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/backupVaults/[^/]+/dataSources/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}:fetchAccessToken", "request": {"$ref": "FetchAccessTokenRequest"}, "response": {"$ref": "FetchAccessTokenResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "finalizeBackup": {"description": "Internal only. Finalize a backup that was started by a call to InitiateBackup.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/backupVaults/{backupVaultsId}/dataSources/{dataSourcesId}:finalizeBackup", "httpMethod": "POST", "id": "backupdr.projects.locations.backupVaults.dataSources.finalizeBackup", "parameterOrder": ["dataSource"], "parameters": {"dataSource": {"description": "Required. The resource name of the instance, in the format 'projects/*/locations/*/backupVaults/*/dataSources/'.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/backupVaults/[^/]+/dataSources/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+dataSource}:finalizeBackup", "request": {"$ref": "FinalizeBackupRequest"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Gets details of a DataSource.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/backupVaults/{backupVaultsId}/dataSources/{dataSourcesId}", "httpMethod": "GET", "id": "backupdr.projects.locations.backupVaults.dataSources.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. Name of the data source resource name, in the format 'projects/{project_id}/locations/{location}/backupVaults/{resource_name}/dataSource/{resource_name}'", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/backupVaults/[^/]+/dataSources/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "DataSource"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "initiateBackup": {"description": "Internal only. Initiates a backup.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/backupVaults/{backupVaultsId}/dataSources/{dataSourcesId}:initiateBackup", "httpMethod": "POST", "id": "backupdr.projects.locations.backupVaults.dataSources.initiateBackup", "parameterOrder": ["dataSource"], "parameters": {"dataSource": {"description": "Required. The resource name of the instance, in the format 'projects/*/locations/*/backupVaults/*/dataSources/'.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/backupVaults/[^/]+/dataSources/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+dataSource}:initiateBackup", "request": {"$ref": "InitiateBackupRequest"}, "response": {"$ref": "InitiateBackupResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists DataSources in a given project and location.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/backupVaults/{backupVaultsId}/dataSources", "httpMethod": "GET", "id": "backupdr.projects.locations.backupVaults.dataSources.list", "parameterOrder": ["parent"], "parameters": {"filter": {"description": "Optional. Filtering results.", "location": "query", "type": "string"}, "orderBy": {"description": "Optional. Hint for how to order the results.", "location": "query", "type": "string"}, "pageSize": {"description": "Optional. Requested page size. Server may return fewer items than requested. If unspecified, server will pick an appropriate default.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Optional. A token identifying a page of results the server should return.", "location": "query", "type": "string"}, "parent": {"description": "Required. The project and location for which to retrieve data sources information, in the format 'projects/{project_id}/locations/{location}'. In Cloud Backup and DR, locations map to Google Cloud regions, for example **us-central1**. To retrieve data sources for all locations, use \"-\" for the '{location}' value.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/backupVaults/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/dataSources", "response": {"$ref": "ListDataSourcesResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "patch": {"description": "Updates the settings of a DataSource.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/backupVaults/{backupVaultsId}/dataSources/{dataSourcesId}", "httpMethod": "PATCH", "id": "backupdr.projects.locations.backupVaults.dataSources.patch", "parameterOrder": ["name"], "parameters": {"allowMissing": {"description": "Optional. Enable upsert.", "location": "query", "type": "boolean"}, "name": {"description": "Output only. Identifier. Name of the datasource to create. It must have the format`\"projects/{project}/locations/{location}/backupVaults/{backupvault}/dataSources/{datasource}\"`. `{datasource}` cannot be changed after creation. It must be between 3-63 characters long and must be unique within the backup vault.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/backupVaults/[^/]+/dataSources/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "Optional. An optional request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server will know to ignore the request if it has already been completed. The server will guarantee that for at least 60 minutes since the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).", "location": "query", "type": "string"}, "updateMask": {"description": "Required. Field mask is used to specify the fields to be overwritten in the DataSource resource by the update. The fields specified in the update_mask are relative to the resource, not the full request. A field will be overwritten if it is in the mask. If the user does not provide a mask then the request will fail.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1/{+name}", "request": {"$ref": "DataSource"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "remove": {"description": "Deletes a DataSource. This is a custom method instead of a standard delete method because external clients will not delete DataSources except for BackupDR backup appliances.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/backupVaults/{backupVaultsId}/dataSources/{dataSourcesId}:remove", "httpMethod": "POST", "id": "backupdr.projects.locations.backupVaults.dataSources.remove", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. Name of the resource.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/backupVaults/[^/]+/dataSources/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}:remove", "request": {"$ref": "RemoveDataSourceRequest"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "setInternalStatus": {"description": "Sets the internal status of a DataSource.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/backupVaults/{backupVaultsId}/dataSources/{dataSourcesId}:setInternalStatus", "httpMethod": "POST", "id": "backupdr.projects.locations.backupVaults.dataSources.setInternalStatus", "parameterOrder": ["dataSource"], "parameters": {"dataSource": {"description": "Required. The resource name of the instance, in the format 'projects/*/locations/*/backupVaults/*/dataSources/'.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/backupVaults/[^/]+/dataSources/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+dataSource}:setInternalStatus", "request": {"$ref": "SetInternalStatusRequest"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}, "resources": {"backups": {"methods": {"delete": {"description": "Deletes a Backup.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/backupVaults/{backupVaultsId}/dataSources/{dataSourcesId}/backups/{backupsId}", "httpMethod": "DELETE", "id": "backupdr.projects.locations.backupVaults.dataSources.backups.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. Name of the resource.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/backupVaults/[^/]+/dataSources/[^/]+/backups/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "Optional. An optional request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server will know to ignore the request if it has already been completed. The server will guarantee that for at least 60 minutes after the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).", "location": "query", "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Gets details of a Backup.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/backupVaults/{backupVaultsId}/dataSources/{dataSourcesId}/backups/{backupsId}", "httpMethod": "GET", "id": "backupdr.projects.locations.backupVaults.dataSources.backups.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. Name of the data source resource name, in the format 'projects/{project_id}/locations/{location}/backupVaults/{backupVault}/dataSources/{datasource}/backups/{backup}'", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/backupVaults/[^/]+/dataSources/[^/]+/backups/[^/]+$", "required": true, "type": "string"}, "view": {"description": "Optional. Reserved for future use to provide a BASIC & FULL view of Backup resource.", "enum": ["BACKUP_VIEW_UNSPECIFIED", "BACKUP_VIEW_BASIC", "BACKUP_VIEW_FULL"], "enumDescriptions": ["If the value is not set, the default 'FULL' view is used.", "Includes basic data about the Backup, but not the full contents.", "Includes all data about the Backup. This is the default value (for both ListBackups and GetBackup)."], "location": "query", "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "Backup"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists Backups in a given project and location.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/backupVaults/{backupVaultsId}/dataSources/{dataSourcesId}/backups", "httpMethod": "GET", "id": "backupdr.projects.locations.backupVaults.dataSources.backups.list", "parameterOrder": ["parent"], "parameters": {"filter": {"description": "Optional. Filtering results.", "location": "query", "type": "string"}, "orderBy": {"description": "Optional. Hint for how to order the results.", "location": "query", "type": "string"}, "pageSize": {"description": "Optional. Requested page size. Server may return fewer items than requested. If unspecified, server will pick an appropriate default.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Optional. A token identifying a page of results the server should return.", "location": "query", "type": "string"}, "parent": {"description": "Required. The project and location for which to retrieve backup information, in the format 'projects/{project_id}/locations/{location}'. In Cloud Backup and DR, locations map to Google Cloud regions, for example **us-central1**. To retrieve data sources for all locations, use \"-\" for the '{location}' value.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/backupVaults/[^/]+/dataSources/[^/]+$", "required": true, "type": "string"}, "view": {"description": "Optional. Reserved for future use to provide a BASIC & FULL view of Backup resource.", "enum": ["BACKUP_VIEW_UNSPECIFIED", "BACKUP_VIEW_BASIC", "BACKUP_VIEW_FULL"], "enumDescriptions": ["If the value is not set, the default 'FULL' view is used.", "Includes basic data about the Backup, but not the full contents.", "Includes all data about the Backup. This is the default value (for both ListBackups and GetBackup)."], "location": "query", "type": "string"}}, "path": "v1/{+parent}/backups", "response": {"$ref": "ListBackupsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "patch": {"description": "Updates the settings of a Backup.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/backupVaults/{backupVaultsId}/dataSources/{dataSourcesId}/backups/{backupsId}", "httpMethod": "PATCH", "id": "backupdr.projects.locations.backupVaults.dataSources.backups.patch", "parameterOrder": ["name"], "parameters": {"name": {"description": "Output only. Identifier. Name of the backup to create. It must have the format`\"projects//locations//backupVaults//dataSources/{datasource}/backups/{backup}\"`. `{backup}` cannot be changed after creation. It must be between 3-63 characters long and must be unique within the datasource.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/backupVaults/[^/]+/dataSources/[^/]+/backups/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "Optional. An optional request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server will know to ignore the request if it has already been completed. The server will guarantee that for at least 60 minutes since the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).", "location": "query", "type": "string"}, "updateMask": {"description": "Required. Field mask is used to specify the fields to be overwritten in the Backup resource by the update. The fields specified in the update_mask are relative to the resource, not the full request. A field will be overwritten if it is in the mask. If the user does not provide a mask then the request will fail.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1/{+name}", "request": {"$ref": "Backup"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "restore": {"description": "Restore from a Backup", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/backupVaults/{backupVaultsId}/dataSources/{dataSourcesId}/backups/{backupsId}:restore", "httpMethod": "POST", "id": "backupdr.projects.locations.backupVaults.dataSources.backups.restore", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The resource name of the Backup instance, in the format 'projects/*/locations/*/backupVaults/*/dataSources/*/backups/'.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/backupVaults/[^/]+/dataSources/[^/]+/backups/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}:restore", "request": {"$ref": "RestoreBackupRequest"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}}}}}, "dataSourceReferences": {"methods": {"fetchForResourceType": {"description": "Fetch DataSourceReferences for a given project, location and resource type.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/dataSourceReferences:fetchForResourceType", "httpMethod": "GET", "id": "backupdr.projects.locations.dataSourceReferences.fetchForResourceType", "parameterOrder": ["parent"], "parameters": {"filter": {"description": "Optional. A filter expression that filters the results fetched in the response. The expression must specify the field name, a comparison operator, and the value that you want to use for filtering. Supported fields: * data_source * data_source_gcp_resource_info.gcp_resourcename * data_source_backup_config_state * data_source_backup_count * data_source_backup_config_info.last_backup_state * data_source_gcp_resource_info.gcp_resourcename * data_source_gcp_resource_info.type * data_source_gcp_resource_info.location * data_source_gcp_resource_info.cloud_sql_instance_properties.instance_create_time", "location": "query", "type": "string"}, "orderBy": {"description": "Optional. A comma-separated list of fields to order by, sorted in ascending order. Use \"desc\" after a field name for descending. Supported fields: * name", "location": "query", "type": "string"}, "pageSize": {"description": "Optional. The maximum number of DataSourceReferences to return. The service may return fewer than this value. If unspecified, at most 50 DataSourceReferences will be returned. The maximum value is 100; values above 100 will be coerced to 100.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Optional. A page token, received from a previous call of `FetchDataSourceReferencesForResourceType`. Provide this to retrieve the subsequent page. When paginating, all other parameters provided to `FetchDataSourceReferencesForResourceType` must match the call that provided the page token.", "location": "query", "type": "string"}, "parent": {"description": "Required. The parent resource name. Format: projects/{project}/locations/{location}", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}, "resourceType": {"description": "Required. The type of the GCP resource. Ex: sql.googleapis.com/Instance", "location": "query", "type": "string"}}, "path": "v1/{+parent}/dataSourceReferences:fetchForResourceType", "response": {"$ref": "FetchDataSourceReferencesForResourceTypeResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Gets details of a single DataSourceReference.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/dataSourceReferences/{dataSourceReferencesId}", "httpMethod": "GET", "id": "backupdr.projects.locations.dataSourceReferences.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the DataSourceReference to retrieve. Format: projects/{project}/locations/{location}/dataSourceReferences/{data_source_reference}", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/dataSourceReferences/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "DataSourceReference"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}, "managementServers": {"methods": {"create": {"description": "Creates a new ManagementServer in a given project and location.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/managementServers", "httpMethod": "POST", "id": "backupdr.projects.locations.managementServers.create", "parameterOrder": ["parent"], "parameters": {"managementServerId": {"description": "Required. The name of the management server to create. The name must be unique for the specified project and location.", "location": "query", "type": "string"}, "parent": {"description": "Required. The management server project and location in the format 'projects/{project_id}/locations/{location}'. In Cloud Backup and DR locations map to Google Cloud regions, for example **us-central1**.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "Optional. An optional request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server will know to ignore the request if it has already been completed. The server will guarantee that for at least 60 minutes since the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).", "location": "query", "type": "string"}}, "path": "v1/{+parent}/managementServers", "request": {"$ref": "ManagementServer"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Deletes a single ManagementServer.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/managementServers/{managementServersId}", "httpMethod": "DELETE", "id": "backupdr.projects.locations.managementServers.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. Name of the resource", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/managementServers/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "Optional. An optional request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server will know to ignore the request if it has already been completed. The server will guarantee that for at least 60 minutes after the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).", "location": "query", "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Gets details of a single ManagementServer.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/managementServers/{managementServersId}", "httpMethod": "GET", "id": "backupdr.projects.locations.managementServers.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. Name of the management server resource name, in the format 'projects/{project_id}/locations/{location}/managementServers/{resource_name}'", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/managementServers/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "ManagementServer"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "getIamPolicy": {"description": "Gets the access control policy for a resource. Returns an empty policy if the resource exists and does not have a policy set.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/managementServers/{managementServersId}:getIamPolicy", "httpMethod": "GET", "id": "backupdr.projects.locations.managementServers.getIamPolicy", "parameterOrder": ["resource"], "parameters": {"options.requestedPolicyVersion": {"description": "Optional. The maximum policy version that will be used to format the policy. Valid values are 0, 1, and 3. Requests specifying an invalid value will be rejected. Requests for policies with any conditional role bindings must specify version 3. Policies with no conditional role bindings may specify any valid value or leave the field unset. The policy in the response might use the policy version that you specified, or it might use a lower policy version. For example, if you specify version 3, but the policy has no conditional role bindings, the response uses version 1. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies).", "format": "int32", "location": "query", "type": "integer"}, "resource": {"description": "REQUIRED: The resource for which the policy is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/managementServers/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+resource}:getIamPolicy", "response": {"$ref": "Policy"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists ManagementServers in a given project and location.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/managementServers", "httpMethod": "GET", "id": "backupdr.projects.locations.managementServers.list", "parameterOrder": ["parent"], "parameters": {"filter": {"description": "Optional. Filtering results.", "location": "query", "type": "string"}, "orderBy": {"description": "Optional. Hint for how to order the results.", "location": "query", "type": "string"}, "pageSize": {"description": "Optional. Requested page size. Server may return fewer items than requested. If unspecified, server will pick an appropriate default.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Optional. A token identifying a page of results the server should return.", "location": "query", "type": "string"}, "parent": {"description": "Required. The project and location for which to retrieve management servers information, in the format 'projects/{project_id}/locations/{location}'. In Cloud BackupDR, locations map to Google Cloud regions, for example **us-central1**. To retrieve management servers for all locations, use \"-\" for the '{location}' value.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/managementServers", "response": {"$ref": "ListManagementServersResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "msComplianceMetadata": {"description": "Returns the Assured Workloads compliance metadata for a given project.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/managementServers:msComplianceMetadata", "httpMethod": "POST", "id": "backupdr.projects.locations.managementServers.msComplianceMetadata", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. The project and location to be used to check CSS metadata for target project information, in the format 'projects/{project_id}/locations/{location}'. In Cloud BackupDR, locations map to Google Cloud regions, for example **us-central1**.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/managementServers$", "required": true, "type": "string"}}, "path": "v1/{+parent}:msComplianceMetadata", "request": {"$ref": "FetchMsComplianceMetadataRequest"}, "response": {"$ref": "FetchMsComplianceMetadataResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "setIamPolicy": {"description": "Sets the access control policy on the specified resource. Replaces any existing policy. Can return `NOT_FOUND`, `INVALID_ARGUMENT`, and `PERMISSION_DENIED` errors.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/managementServers/{managementServersId}:setIamPolicy", "httpMethod": "POST", "id": "backupdr.projects.locations.managementServers.setIamPolicy", "parameterOrder": ["resource"], "parameters": {"resource": {"description": "REQUIRED: The resource for which the policy is being specified. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/managementServers/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+resource}:setIamPolicy", "request": {"$ref": "SetIamPolicyRequest"}, "response": {"$ref": "Policy"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "testIamPermissions": {"description": "Returns permissions that a caller has on the specified resource. If the resource does not exist, this will return an empty set of permissions, not a `NOT_FOUND` error. Note: This operation is designed to be used for building permission-aware UIs and command-line tools, not for authorization checking. This operation may \"fail open\" without warning.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/managementServers/{managementServersId}:testIamPermissions", "httpMethod": "POST", "id": "backupdr.projects.locations.managementServers.testIamPermissions", "parameterOrder": ["resource"], "parameters": {"resource": {"description": "REQUIRED: The resource for which the policy detail is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/managementServers/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+resource}:testIamPermissions", "request": {"$ref": "TestIamPermissionsRequest"}, "response": {"$ref": "TestIamPermissionsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}, "operations": {"methods": {"cancel": {"description": "Starts asynchronous cancellation on a long-running operation. The server makes a best effort to cancel the operation, but success is not guaranteed. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`. Clients can use Operations.GetOperation or other methods to check whether the cancellation succeeded or whether the operation completed despite cancellation. On successful cancellation, the operation is not deleted; instead, it becomes an operation with an Operation.error value with a google.rpc.Status.code of `1`, corresponding to `Code.CANCELLED`.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/operations/{operationsId}:cancel", "httpMethod": "POST", "id": "backupdr.projects.locations.operations.cancel", "parameterOrder": ["name"], "parameters": {"name": {"description": "The name of the operation resource to be cancelled.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/operations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}:cancel", "request": {"$ref": "CancelOperationRequest"}, "response": {"$ref": "Empty"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Deletes a long-running operation. This method indicates that the client is no longer interested in the operation result. It does not cancel the operation. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/operations/{operationsId}", "httpMethod": "DELETE", "id": "backupdr.projects.locations.operations.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "The name of the operation resource to be deleted.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/operations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "Empty"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Gets the latest state of a long-running operation. Clients can use this method to poll the operation result at intervals as recommended by the API service.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/operations/{operationsId}", "httpMethod": "GET", "id": "backupdr.projects.locations.operations.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "The name of the operation resource.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/operations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists operations that match the specified filter in the request. If the server doesn't support this method, it returns `UNIMPLEMENTED`.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/operations", "httpMethod": "GET", "id": "backupdr.projects.locations.operations.list", "parameterOrder": ["name"], "parameters": {"filter": {"description": "The standard list filter.", "location": "query", "type": "string"}, "name": {"description": "The name of the operation's parent resource.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}, "pageSize": {"description": "The standard list page size.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "The standard list page token.", "location": "query", "type": "string"}}, "path": "v1/{+name}/operations", "response": {"$ref": "ListOperationsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}, "resourceBackupConfigs": {"methods": {"list": {"description": "Lists ResourceBackupConfigs.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/resourceBackupConfigs", "httpMethod": "GET", "id": "backupdr.projects.locations.resourceBackupConfigs.list", "parameterOrder": ["parent"], "parameters": {"filter": {"description": "Optional. Filtering results.", "location": "query", "type": "string"}, "orderBy": {"description": "Optional. Hint for how to order the results.", "location": "query", "type": "string"}, "pageSize": {"description": "Optional. Requested page size. Server may return fewer items than requested. If unspecified, server will use 100 as default. Maximum value is 500 and values above 500 will be coerced to 500.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Optional. A token identifying a page of results the server should return.", "location": "query", "type": "string"}, "parent": {"description": "Required. The project and location for which to retrieve resource backup configs. Format: 'projects/{project_id}/locations/{location}'. In Cloud Backup and DR, locations map to Google Cloud regions, for example **us-central1**.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/resourceBackupConfigs", "response": {"$ref": "ListResourceBackupConfigsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}, "serviceConfig": {"methods": {"initialize": {"description": "Initializes the service related config for a project.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/serviceConfig:initialize", "httpMethod": "POST", "id": "backupdr.projects.locations.serviceConfig.initialize", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The resource name of the serviceConfig used to initialize the service. Format: `projects/{project_id}/locations/{location}/serviceConfig`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/serviceConfig$", "required": true, "type": "string"}}, "path": "v1/{+name}:initialize", "request": {"$ref": "InitializeServiceRequest"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}}}}}}, "revision": "20250723", "rootUrl": "https://backupdr.googleapis.com/", "schemas": {"AbandonBackupRequest": {"description": "request message for AbandonBackup.", "id": "AbandonBackupRequest", "properties": {"requestId": {"description": "Optional. An optional request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server will know to ignore the request if it has already been completed. The server will guarantee that for at least 60 minutes since the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).", "type": "string"}}, "type": "object"}, "AcceleratorConfig": {"description": "A specification of the type and number of accelerator cards attached to the instance.", "id": "AcceleratorConfig", "properties": {"acceleratorCount": {"description": "Optional. The number of the guest accelerator cards exposed to this instance.", "format": "int32", "type": "integer"}, "acceleratorType": {"description": "Optional. Full or partial URL of the accelerator type resource to attach to this instance.", "type": "string"}}, "type": "object"}, "AccessConfig": {"description": "An access configuration attached to an instance's network interface. Only one access config per instance is supported.", "id": "AccessConfig", "properties": {"externalIpv6": {"description": "Optional. The external IPv6 address of this access configuration.", "type": "string"}, "externalIpv6PrefixLength": {"description": "Optional. The prefix length of the external IPv6 range.", "format": "int32", "type": "integer"}, "name": {"description": "Optional. The name of this access configuration.", "type": "string"}, "natIP": {"description": "Optional. The external IP address of this access configuration.", "type": "string"}, "networkTier": {"description": "Optional. This signifies the networking tier used for configuring this access", "enum": ["NETWORK_TIER_UNSPECIFIED", "PREMIUM", "STANDARD"], "enumDescriptions": ["Default value. This value is unused.", "High quality, Google-grade network tier, support for all networking products.", "Public internet quality, only limited support for other networking products."], "type": "string"}, "publicPtrDomainName": {"description": "Optional. The DNS domain name for the public PTR record.", "type": "string"}, "setPublicPtr": {"description": "Optional. Specifies whether a public DNS 'PTR' record should be created to map the external IP address of the instance to a DNS domain name.", "type": "boolean"}, "type": {"description": "Optional. In accessConfigs (IPv4), the default and only option is ONE_TO_ONE_NAT. In ipv6AccessConfigs, the default and only option is DIRECT_IPV6.", "enum": ["ACCESS_TYPE_UNSPECIFIED", "ONE_TO_ONE_NAT", "DIRECT_IPV6"], "enumDescriptions": ["Default value. This value is unused.", "ONE_TO_ONE_NAT", "Direct IPv6 access."], "type": "string"}}, "type": "object"}, "AdvancedMachineFeatures": {"description": "Specifies options for controlling advanced machine features.", "id": "AdvancedMachineFeatures", "properties": {"enableNestedVirtualization": {"description": "Optional. Whether to enable nested virtualization or not (default is false).", "type": "boolean"}, "enableUefiNetworking": {"description": "Optional. Whether to enable UEFI networking for instance creation.", "type": "boolean"}, "threadsPerCore": {"description": "Optional. The number of threads per physical core. To disable simultaneous multithreading (SMT) set this to 1. If unset, the maximum number of threads supported per core by the underlying processor is assumed.", "format": "int32", "type": "integer"}, "visibleCoreCount": {"description": "Optional. The number of physical cores to expose to an instance. Multiply by the number of threads per core to compute the total number of virtual CPUs to expose to the instance. If unset, the number of cores is inferred from the instance's nominal CPU count and the underlying platform's SMT width.", "format": "int32", "type": "integer"}}, "type": "object"}, "AliasIpRange": {"description": "An alias IP range attached to an instance's network interface.", "id": "AliasIpRange", "properties": {"ipCidrRange": {"description": "Optional. The IP alias ranges to allocate for this interface.", "type": "string"}, "subnetworkRangeName": {"description": "Optional. The name of a subnetwork secondary IP range from which to allocate an IP alias range. If not specified, the primary range of the subnetwork is used.", "type": "string"}}, "type": "object"}, "AllocationAffinity": {"description": "Specifies the reservations that this instance can consume from.", "id": "AllocationAffinity", "properties": {"consumeReservationType": {"description": "Optional. Specifies the type of reservation from which this instance can consume", "enum": ["TYPE_UNSPECIFIED", "NO_RESERVATION", "ANY_RESERVATION", "SPECIFIC_RESERVATION"], "enumDescriptions": ["Default value. This value is unused.", "Do not consume from any allocated capacity.", "Consume any allocation available.", "Must consume from a specific reservation. Must specify key value fields for specifying the reservations."], "type": "string"}, "key": {"description": "Optional. Corresponds to the label key of a reservation resource.", "type": "string"}, "values": {"description": "Optional. Corresponds to the label values of a reservation resource.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "AttachedDisk": {"description": "An instance-attached disk resource.", "id": "AttachedDisk", "properties": {"autoDelete": {"description": "Optional. Specifies whether the disk will be auto-deleted when the instance is deleted (but not when the disk is detached from the instance).", "type": "boolean"}, "boot": {"description": "Optional. Indicates that this is a boot disk. The virtual machine will use the first partition of the disk for its root filesystem.", "type": "boolean"}, "deviceName": {"description": "Optional. This is used as an identifier for the disks. This is the unique name has to provided to modify disk parameters like disk_name and replica_zones (in case of RePDs)", "type": "string"}, "diskEncryptionKey": {"$ref": "CustomerEncryptionKey", "description": "Optional. Encrypts or decrypts a disk using a customer-supplied encryption key."}, "diskInterface": {"description": "Optional. Specifies the disk interface to use for attaching this disk.", "enum": ["DISK_INTERFACE_UNSPECIFIED", "SCSI", "NVME", "NVDIMM", "ISCSI"], "enumDescriptions": ["Default value, which is unused.", "SCSI Disk Interface.", "NVME Disk Interface.", "NVDIMM Disk Interface.", "ISCSI Disk Interface."], "type": "string"}, "diskSizeGb": {"description": "Optional. The size of the disk in GB.", "format": "int64", "type": "string"}, "diskType": {"description": "Optional. Output only. The URI of the disk type resource. For example: projects/project/zones/zone/diskTypes/pd-standard or pd-ssd", "readOnly": true, "type": "string"}, "diskTypeDeprecated": {"deprecated": true, "description": "Specifies the type of the disk.", "enum": ["DISK_TYPE_UNSPECIFIED", "SCRATCH", "PERSISTENT"], "enumDescriptions": ["Default value, which is unused.", "A scratch disk type.", "A persistent disk type."], "type": "string"}, "guestOsFeature": {"description": "Optional. A list of features to enable on the guest operating system. Applicable only for bootable images.", "items": {"$ref": "GuestOsFeature"}, "type": "array"}, "index": {"description": "Optional. A zero-based index to this disk, where 0 is reserved for the boot disk.", "format": "int64", "type": "string"}, "initializeParams": {"$ref": "InitializeParams", "description": "Optional. Specifies the parameters to initialize this disk."}, "kind": {"description": "Optional. Type of the resource.", "type": "string"}, "license": {"description": "Optional. Any valid publicly visible licenses.", "items": {"type": "string"}, "type": "array"}, "mode": {"description": "Optional. The mode in which to attach this disk.", "enum": ["DISK_MODE_UNSPECIFIED", "READ_WRITE", "READ_ONLY", "LOCKED"], "enumDescriptions": ["Default value, which is unused.", "Attaches this disk in read-write mode. Only one virtual machine at a time can be attached to a disk in read-write mode.", "Attaches this disk in read-only mode. Multiple virtual machines can use a disk in read-only mode at a time.", "The disk is locked for administrative reasons. Nobody else can use the disk. This mode is used (for example) when taking a snapshot of a disk to prevent mounting the disk while it is being snapshotted."], "type": "string"}, "savedState": {"description": "Optional. Output only. The state of the disk.", "enum": ["DISK_SAVED_STATE_UNSPECIFIED", "PRESERVED"], "enumDescriptions": ["Default Disk state has not been preserved.", "Disk state has been preserved."], "readOnly": true, "type": "string"}, "source": {"description": "Optional. Specifies a valid partial or full URL to an existing Persistent Disk resource.", "type": "string"}, "type": {"description": "Optional. Specifies the type of the disk.", "enum": ["DISK_TYPE_UNSPECIFIED", "SCRATCH", "PERSISTENT"], "enumDescriptions": ["Default value, which is unused.", "A scratch disk type.", "A persistent disk type."], "type": "string"}}, "type": "object"}, "AuditConfig": {"description": "Specifies the audit configuration for a service. The configuration determines which permission types are logged, and what identities, if any, are exempted from logging. An AuditConfig must have one or more AuditLogConfigs. If there are AuditConfigs for both `allServices` and a specific service, the union of the two AuditConfigs is used for that service: the log_types specified in each AuditConfig are enabled, and the exempted_members in each AuditLogConfig are exempted. Example Policy with multiple AuditConfigs: { \"audit_configs\": [ { \"service\": \"allServices\", \"audit_log_configs\": [ { \"log_type\": \"DATA_READ\", \"exempted_members\": [ \"user:<EMAIL>\" ] }, { \"log_type\": \"DATA_WRITE\" }, { \"log_type\": \"ADMIN_READ\" } ] }, { \"service\": \"sampleservice.googleapis.com\", \"audit_log_configs\": [ { \"log_type\": \"DATA_READ\" }, { \"log_type\": \"DATA_WRITE\", \"exempted_members\": [ \"user:<EMAIL>\" ] } ] } ] } For sampleservice, this policy enables DATA_READ, DATA_WRITE and ADMIN_READ logging. It also exempts `<EMAIL>` from DATA_READ logging, and `<EMAIL>` from DATA_WRITE logging.", "id": "AuditConfig", "properties": {"auditLogConfigs": {"description": "The configuration for logging of each type of permission.", "items": {"$ref": "AuditLogConfig"}, "type": "array"}, "service": {"description": "Specifies a service that will be enabled for audit logging. For example, `storage.googleapis.com`, `cloudsql.googleapis.com`. `allServices` is a special value that covers all services.", "type": "string"}}, "type": "object"}, "AuditLogConfig": {"description": "Provides the configuration for logging a type of permissions. Example: { \"audit_log_configs\": [ { \"log_type\": \"DATA_READ\", \"exempted_members\": [ \"user:<EMAIL>\" ] }, { \"log_type\": \"DATA_WRITE\" } ] } This enables 'DATA_READ' and 'DATA_WRITE' logging, <NAME_EMAIL> from DATA_READ logging.", "id": "AuditLogConfig", "properties": {"exemptedMembers": {"description": "Specifies the identities that do not cause logging for this type of permission. Follows the same format of Binding.members.", "items": {"type": "string"}, "type": "array"}, "logType": {"description": "The log type that this config enables.", "enum": ["LOG_TYPE_UNSPECIFIED", "ADMIN_READ", "DATA_WRITE", "DATA_READ"], "enumDescriptions": ["Default case. Should never be this.", "Admin reads. Example: CloudIAM getIamPolicy", "Data writes. Example: CloudSQL Users create", "Data reads. Example: CloudSQL Users list"], "type": "string"}}, "type": "object"}, "Backup": {"description": "Message describing a Backup object.", "id": "Backup", "properties": {"backupApplianceBackupProperties": {"$ref": "BackupApplianceBackupProperties", "description": "Output only. Backup Appliance specific backup properties.", "readOnly": true}, "backupApplianceLocks": {"description": "Optional. The list of BackupLocks taken by the accessor Backup Appliance.", "items": {"$ref": "BackupLock"}, "type": "array"}, "backupType": {"description": "Output only. Type of the backup, unspecified, scheduled or ondemand.", "enum": ["BACKUP_TYPE_UNSPECIFIED", "SCHEDULED", "ON_DEMAND", "ON_DEMAND_OPERATIONAL"], "enumDescriptions": ["Backup type is unspecified.", "Scheduled backup.", "On demand backup.", "Operational backup."], "readOnly": true, "type": "string"}, "cloudSqlInstanceBackupProperties": {"$ref": "CloudSqlInstanceBackupProperties", "description": "Output only. Cloud SQL specific backup properties.", "readOnly": true}, "computeInstanceBackupProperties": {"$ref": "ComputeInstanceBackupProperties", "description": "Output only. Compute Engine specific backup properties.", "readOnly": true}, "consistencyTime": {"description": "Output only. The point in time when this backup was captured from the source.", "format": "google-datetime", "readOnly": true, "type": "string"}, "createTime": {"description": "Output only. The time when the instance was created.", "format": "google-datetime", "readOnly": true, "type": "string"}, "description": {"description": "Output only. The description of the Backup instance (2048 characters or less).", "readOnly": true, "type": "string"}, "diskBackupProperties": {"$ref": "DiskBackupProperties", "description": "Output only. Disk specific backup properties.", "readOnly": true}, "enforcedRetentionEndTime": {"description": "Optional. The backup can not be deleted before this time.", "format": "google-datetime", "type": "string"}, "etag": {"description": "Optional. Server specified ETag to prevent updates from overwriting each other.", "type": "string"}, "expireTime": {"description": "Optional. When this backup is automatically expired.", "format": "google-datetime", "type": "string"}, "gcpBackupPlanInfo": {"$ref": "GCPBackupPlanInfo", "description": "Output only. Configuration for a Google Cloud resource.", "readOnly": true}, "labels": {"additionalProperties": {"type": "string"}, "description": "Optional. Resource labels to represent user provided metadata. No labels currently defined.", "type": "object"}, "name": {"description": "Output only. Identifier. Name of the backup to create. It must have the format`\"projects//locations//backupVaults//dataSources/{datasource}/backups/{backup}\"`. `{backup}` cannot be changed after creation. It must be between 3-63 characters long and must be unique within the datasource.", "readOnly": true, "type": "string"}, "resourceSizeBytes": {"description": "Output only. source resource size in bytes at the time of the backup.", "format": "int64", "readOnly": true, "type": "string"}, "satisfiesPzi": {"description": "Optional. Output only. Reserved for future use.", "readOnly": true, "type": "boolean"}, "satisfiesPzs": {"description": "Optional. Output only. Reserved for future use.", "readOnly": true, "type": "boolean"}, "serviceLocks": {"description": "Output only. The list of BackupLocks taken by the service to prevent the deletion of the backup.", "items": {"$ref": "BackupLock"}, "readOnly": true, "type": "array"}, "state": {"description": "Output only. The Backup resource instance state.", "enum": ["STATE_UNSPECIFIED", "CREATING", "ACTIVE", "DELETING", "ERROR", "UPLOADING"], "enumDescriptions": ["State not set.", "The backup is being created.", "The backup has been created and is fully usable.", "The backup is being deleted.", "The backup is experiencing an issue and might be unusable.", "The backup is being uploaded."], "readOnly": true, "type": "string"}, "updateTime": {"description": "Output only. The time when the instance was updated.", "format": "google-datetime", "readOnly": true, "type": "string"}}, "type": "object"}, "BackupApplianceBackupConfig": {"description": "BackupApplianceBackupConfig captures the backup configuration for applications that are protected by Backup Appliances.", "id": "BackupApplianceBackupConfig", "properties": {"applicationName": {"description": "The name of the application.", "type": "string"}, "backupApplianceId": {"description": "The ID of the backup appliance.", "format": "int64", "type": "string"}, "backupApplianceName": {"description": "The name of the backup appliance.", "type": "string"}, "hostName": {"description": "The name of the host where the application is running.", "type": "string"}, "slaId": {"description": "The ID of the SLA of this application.", "format": "int64", "type": "string"}, "slpName": {"description": "The name of the SLP associated with the application.", "type": "string"}, "sltName": {"description": "The name of the SLT associated with the application.", "type": "string"}}, "type": "object"}, "BackupApplianceBackupProperties": {"description": "BackupApplianceBackupProperties represents BackupDR backup appliance's properties.", "id": "BackupApplianceBackupProperties", "properties": {"finalizeTime": {"description": "Output only. The time when this backup object was finalized (if none, backup is not finalized).", "format": "google-datetime", "readOnly": true, "type": "string"}, "generationId": {"description": "Output only. The numeric generation ID of the backup (monotonically increasing).", "format": "int32", "readOnly": true, "type": "integer"}, "recoveryRangeEndTime": {"description": "Optional. The latest timestamp of data available in this Backup.", "format": "google-datetime", "type": "string"}, "recoveryRangeStartTime": {"description": "Optional. The earliest timestamp of data available in this Backup.", "format": "google-datetime", "type": "string"}}, "type": "object"}, "BackupApplianceLockInfo": {"description": "BackupApplianceLockInfo contains metadata about the backupappliance that created the lock.", "id": "BackupApplianceLockInfo", "properties": {"backupApplianceId": {"description": "Required. The ID of the backup/recovery appliance that created this lock.", "format": "int64", "type": "string"}, "backupApplianceName": {"description": "Required. The name of the backup/recovery appliance that created this lock.", "type": "string"}, "backupImage": {"description": "The image name that depends on this Backup.", "type": "string"}, "jobName": {"description": "The job name on the backup/recovery appliance that created this lock.", "type": "string"}, "lockReason": {"description": "Required. The reason for the lock: e.g. MOUNT/RESTORE/BACKUP/etc. The value of this string is only meaningful to the client and it is not interpreted by the BackupVault service.", "type": "string"}, "slaId": {"description": "The SLA on the backup/recovery appliance that owns the lock.", "format": "int64", "type": "string"}}, "type": "object"}, "BackupConfigDetails": {"description": "BackupConfigDetails has information about how the resource is configured for backups and about the most recent backup taken for this configuration.", "id": "BackupConfigDetails", "properties": {"applicableResource": {"description": "Output only. The [full resource name](https://cloud.google.com/asset-inventory/docs/resource-name-format) of the resource that is applicable for the backup configuration. Example: \"//compute.googleapis.com/projects/{project}/zones/{zone}/instances/{instance}\"", "readOnly": true, "type": "string"}, "backupConfigSource": {"description": "Output only. The full resource name of the backup config source resource. For example, \"//backupdr.googleapis.com/v1/projects/{project}/locations/{region}/backupPlans/{backupplanId}\" or \"//compute.googleapis.com/projects/{project}/locations/{region}/resourcePolicies/{resourcePolicyId}\".", "readOnly": true, "type": "string"}, "backupConfigSourceDisplayName": {"description": "Output only. The display name of the backup config source resource.", "readOnly": true, "type": "string"}, "backupDrPlanConfig": {"$ref": "BackupDrPlanConfig", "description": "Backup and DR's Backup Plan specific data."}, "backupDrTemplateConfig": {"$ref": "BackupDrTemplateConfig", "description": "Backup and DR's Template specific data."}, "backupLocations": {"description": "The locations where the backups are to be stored.", "items": {"$ref": "BackupLocation"}, "type": "array"}, "backupVault": {"description": "Output only. The [full resource name](https://cloud.google.com/asset-inventory/docs/resource-name-format) of the backup vault that will store the backups generated through this backup configuration. Example: \"//backupdr.googleapis.com/v1/projects/{project}/locations/{region}/backupVaults/{backupvaultId}\"", "readOnly": true, "type": "string"}, "latestSuccessfulBackupTime": {"description": "Output only. Timestamp of the latest successful backup created via this backup configuration.", "format": "google-datetime", "readOnly": true, "type": "string"}, "pitrSettings": {"$ref": "PitrSettings", "description": "Output only. Point in time recovery settings of the backup configuration resource.", "readOnly": true}, "state": {"description": "Output only. The state of the backup config resource.", "enum": ["STATE_UNSPECIFIED", "ACTIVE", "INACTIVE", "ERROR"], "enumDescriptions": ["Backup config state not set.", "The config is in an active state protecting the resource", "The config is currently not protecting the resource. Either because it is disabled or the owning project has been deleted without cleanup of the actual resource.", "The config still exists but because of some error state it is not protecting the resource. Like the source project is deleted. For eg. PlanAssociation, BackupPlan is deleted."], "readOnly": true, "type": "string"}, "type": {"description": "Output only. The type of the backup config resource.", "enum": ["TYPE_UNSPECIFIED", "CLOUD_SQL_INSTANCE_BACKUP_CONFIG", "COMPUTE_ENGINE_RESOURCE_POLICY", "BACKUPDR_BACKUP_PLAN", "BACKUPDR_TEMPLATE"], "enumDescriptions": ["Backup config type is unspecified.", "Backup config is Cloud SQL instance's automated backup config.", "Backup config is Compute Engine Resource Policy.", "Backup config is Backup and DR's Backup Plan.", "Backup config is Backup and DR's Template."], "readOnly": true, "type": "string"}}, "type": "object"}, "BackupConfigInfo": {"description": "BackupConfigInfo has information about how the resource is configured for Backup and about the most recent backup to this vault.", "id": "BackupConfigInfo", "properties": {"backupApplianceBackupConfig": {"$ref": "BackupApplianceBackupConfig", "description": "Configuration for an application backed up by a Backup Appliance."}, "gcpBackupConfig": {"$ref": "GcpBackupConfig", "description": "Configuration for a Google Cloud resource."}, "lastBackupError": {"$ref": "Status", "description": "Output only. If the last backup failed, this field has the error message.", "readOnly": true}, "lastBackupState": {"description": "Output only. The status of the last backup to this BackupVault", "enum": ["LAST_BACKUP_STATE_UNSPECIFIED", "FIRST_BACKUP_PENDING", "SUCCEEDED", "FAILED", "PERMISSION_DENIED"], "enumDescriptions": ["Status not set.", "The first backup has not yet completed", "The most recent backup was successful", "The most recent backup failed", "The most recent backup could not be run/failed because of the lack of permissions"], "readOnly": true, "type": "string"}, "lastSuccessfulBackupConsistencyTime": {"description": "Output only. If the last backup were successful, this field has the consistency date.", "format": "google-datetime", "readOnly": true, "type": "string"}}, "type": "object"}, "BackupDrPlanConfig": {"description": "BackupDrPlanConfig has additional information about Backup and DR's Plan backup configuration.", "id": "BackupDrPlanConfig", "properties": {"backupDrPlanRules": {"description": "Backup rules of the backup plan resource.", "items": {"$ref": "BackupDrPlanRule"}, "type": "array"}}, "type": "object"}, "BackupDrPlanRule": {"description": "BackupDrPlanRule has rule specific information of the backup plan resource.", "id": "BackupDrPlanRule", "properties": {"lastSuccessfulBackupTime": {"description": "Output only. Timestamp of the latest successful backup created via this backup rule.", "format": "google-datetime", "readOnly": true, "type": "string"}, "ruleId": {"description": "Output only. Unique Id of the backup rule.", "readOnly": true, "type": "string"}}, "type": "object"}, "BackupDrTemplateConfig": {"description": "BackupDrTemplateConfig has additional information about Backup and DR's Template backup configuration.", "id": "BackupDrTemplateConfig", "properties": {"firstPartyManagementUri": {"description": "Output only. The URI of the BackupDr template resource for the first party identity users.", "readOnly": true, "type": "string"}, "thirdPartyManagementUri": {"description": "Output only. The URI of the BackupDr template resource for the third party identity users.", "readOnly": true, "type": "string"}}, "type": "object"}, "BackupLocation": {"description": "BackupLocation represents a cloud location where a backup can be stored.", "id": "BackupLocation", "properties": {"locationId": {"description": "Output only. The id of the cloud location. Example: \"us-central1\"", "readOnly": true, "type": "string"}, "type": {"description": "Output only. The type of the location.", "enum": ["TYPE_UNSPECIFIED", "ZONAL", "REGIONAL", "MULTI_REGIONAL"], "enumDescriptions": ["Location type is unspecified.", "Location type is zonal.", "Location type is regional.", "Location type is multi regional."], "readOnly": true, "type": "string"}}, "type": "object"}, "BackupLock": {"description": "BackupLock represents a single lock on a Backup resource. An unexpired lock on a Backup prevents the Backup from being deleted.", "id": "BackupLock", "properties": {"backupApplianceLockInfo": {"$ref": "BackupApplianceLockInfo", "description": "If the client is a backup and recovery appliance, this contains metadata about why the lock exists."}, "lockUntilTime": {"description": "Required. The time after which this lock is not considered valid and will no longer protect the Backup from deletion.", "format": "google-datetime", "type": "string"}, "serviceLockInfo": {"$ref": "ServiceLockInfo", "description": "Output only. Contains metadata about the lock exist for Google Cloud native backups.", "readOnly": true}}, "type": "object"}, "BackupPlan": {"description": "A `BackupPlan` specifies some common fields, such as `description` as well as one or more `BackupRule` messages. Each `BackupRule` has a retention policy and defines a schedule by which the system is to perform backup workloads.", "id": "BackupPlan", "properties": {"backupRules": {"description": "Optional. The backup rules for this `BackupPlan`.", "items": {"$ref": "BackupRule"}, "type": "array"}, "backupVault": {"description": "Required. Resource name of backup vault which will be used as storage location for backups. Format: projects/{project}/locations/{location}/backupVaults/{backupvault}", "type": "string"}, "backupVaultServiceAccount": {"description": "Output only. The Google Cloud Platform Service Account to be used by the BackupVault for taking backups. Specify the email address of the Backup Vault Service Account.", "readOnly": true, "type": "string"}, "createTime": {"description": "Output only. When the `BackupPlan` was created.", "format": "google-datetime", "readOnly": true, "type": "string"}, "description": {"description": "Optional. The description of the `BackupPlan` resource. The description allows for additional details about `BackupPlan` and its use cases to be provided. An example description is the following: \"This is a backup plan that performs a daily backup at 6pm and retains data for 3 months\". The description must be at most 2048 characters.", "type": "string"}, "etag": {"description": "Optional. `etag` is returned from the service in the response. As a user of the service, you may provide an etag value in this field to prevent stale resources.", "type": "string"}, "labels": {"additionalProperties": {"type": "string"}, "description": "Optional. This collection of key/value pairs allows for custom labels to be supplied by the user. Example, {\"tag\": \"Weekly\"}.", "type": "object"}, "logRetentionDays": {"description": "Optional. Applicable only for CloudSQL resource_type. Configures how long logs will be stored. It is defined in “days”. This value should be greater than or equal to minimum enforced log retention duration of the backup vault.", "format": "int64", "type": "string"}, "name": {"description": "Output only. Identifier. The resource name of the `BackupPlan`. Format: `projects/{project}/locations/{location}/backupPlans/{backup_plan}`", "readOnly": true, "type": "string"}, "resourceType": {"description": "Required. The resource type to which the `BackupPlan` will be applied. Examples include, \"compute.googleapis.com/Instance\", \"sqladmin.googleapis.com/Instance\", \"alloydb.googleapis.com/Cluster\", \"compute.googleapis.com/Disk\".", "type": "string"}, "revisionId": {"description": "Output only. The user friendly revision ID of the `BackupPlanRevision`. Example: v0, v1, v2, etc.", "readOnly": true, "type": "string"}, "revisionName": {"description": "Output only. The resource id of the `BackupPlanRevision`. Format: `projects/{project}/locations/{location}/backupPlans/{backup_plan}/revisions/{revision_id}`", "readOnly": true, "type": "string"}, "state": {"description": "Output only. The `State` for the `BackupPlan`.", "enum": ["STATE_UNSPECIFIED", "CREATING", "ACTIVE", "DELETING", "INACTIVE", "UPDATING"], "enumDescriptions": ["State not set.", "The resource is being created.", "The resource has been created and is fully usable.", "The resource is being deleted.", "The resource has been created but is not usable.", "The resource is being updated."], "readOnly": true, "type": "string"}, "supportedResourceTypes": {"description": "Output only. All resource types to which backupPlan can be applied.", "items": {"type": "string"}, "readOnly": true, "type": "array"}, "updateTime": {"description": "Output only. When the `BackupPlan` was last updated.", "format": "google-datetime", "readOnly": true, "type": "string"}}, "type": "object"}, "BackupPlanAssociation": {"description": "A BackupPlanAssociation represents a single BackupPlanAssociation which contains details like workload, backup plan etc", "id": "BackupPlanAssociation", "properties": {"backupPlan": {"description": "Required. Resource name of backup plan which needs to be applied on workload. Format: projects/{project}/locations/{location}/backupPlans/{backupPlanId}", "type": "string"}, "backupPlanRevisionId": {"description": "Output only. The user friendly revision ID of the `BackupPlanRevision`. Example: v0, v1, v2, etc.", "readOnly": true, "type": "string"}, "backupPlanRevisionName": {"description": "Output only. The resource id of the `BackupPlanRevision`. Format: `projects/{project}/locations/{location}/backupPlans/{backup_plan}/revisions/{revision_id}`", "readOnly": true, "type": "string"}, "cloudSqlInstanceBackupPlanAssociationProperties": {"$ref": "CloudSqlInstanceBackupPlanAssociationProperties", "description": "Output only. Cloud SQL instance's backup plan association properties.", "readOnly": true}, "createTime": {"description": "Output only. The time when the instance was created.", "format": "google-datetime", "readOnly": true, "type": "string"}, "dataSource": {"description": "Output only. Resource name of data source which will be used as storage location for backups taken. Format : projects/{project}/locations/{location}/backupVaults/{backupvault}/dataSources/{datasource}", "readOnly": true, "type": "string"}, "name": {"description": "Output only. Identifier. The resource name of BackupPlanAssociation in below format Format : projects/{project}/locations/{location}/backupPlanAssociations/{backupPlanAssociationId}", "readOnly": true, "type": "string"}, "resource": {"description": "Required. Immutable. Resource name of workload on which the backup plan is applied. The format can either be the resource name (e.g., \"projects/my-project/zones/us-central1-a/instances/my-instance\") or the full resource URI (e.g., \"https://www.googleapis.com/compute/v1/projects/my-project/zones/us-central1-a/instances/my-instance\").", "type": "string"}, "resourceType": {"description": "Required. Immutable. Resource type of workload on which backupplan is applied", "type": "string"}, "rulesConfigInfo": {"description": "Output only. The config info related to backup rules.", "items": {"$ref": "RuleConfigInfo"}, "readOnly": true, "type": "array"}, "state": {"description": "Output only. The BackupPlanAssociation resource state.", "enum": ["STATE_UNSPECIFIED", "CREATING", "ACTIVE", "DELETING", "INACTIVE", "UPDATING"], "enumDescriptions": ["State not set.", "The resource is being created.", "The resource has been created and is fully usable.", "The resource is being deleted.", "The resource has been created but is not usable.", "The resource is being updated."], "readOnly": true, "type": "string"}, "updateTime": {"description": "Output only. The time when the instance was updated.", "format": "google-datetime", "readOnly": true, "type": "string"}}, "type": "object"}, "BackupPlanRevision": {"description": "`BackupPlanRevision` represents a snapshot of a `BackupPlan` at a point in time.", "id": "BackupPlanRevision", "properties": {"backupPlanSnapshot": {"$ref": "BackupPlan", "description": "The Backup Plan being encompassed by this revision."}, "createTime": {"description": "Output only. The timestamp that the revision was created.", "format": "google-datetime", "readOnly": true, "type": "string"}, "name": {"description": "Output only. Identifier. The resource name of the `BackupPlanRevision`. Format: `projects/{project}/locations/{location}/backupPlans/{backup_plan}/revisions/{revision}`", "readOnly": true, "type": "string"}, "revisionId": {"description": "Output only. The user friendly revision ID of the `BackupPlanRevision`. Example: v0, v1, v2, etc.", "readOnly": true, "type": "string"}, "state": {"description": "Output only. Resource State", "enum": ["STATE_UNSPECIFIED", "CREATING", "ACTIVE", "DELETING", "INACTIVE"], "enumDescriptions": ["State not set.", "The resource is being created.", "The resource has been created and is fully usable.", "The resource is being deleted.", "The resource has been created but is not usable."], "readOnly": true, "type": "string"}}, "type": "object"}, "BackupRule": {"description": "`BackupRule` binds the backup schedule to a retention policy.", "id": "BackupRule", "properties": {"backupRetentionDays": {"description": "Required. Configures the duration for which backup data will be kept. It is defined in “days”. The value should be greater than or equal to minimum enforced retention of the backup vault. Minimum value is 1 and maximum value is 36159 for custom retention on-demand backup. Minimum and maximum values are workload specific for all other rules.", "format": "int32", "type": "integer"}, "ruleId": {"description": "Required. Immutable. The unique id of this `BackupRule`. The `rule_id` is unique per `BackupPlan`.The `rule_id` must start with a lowercase letter followed by up to 62 lowercase letters, numbers, or hyphens. Pattern, /a-z{,62}/.", "type": "string"}, "standardSchedule": {"$ref": "StandardSchedule", "description": "Optional. Defines a schedule that runs within the confines of a defined window of time."}}, "type": "object"}, "BackupVault": {"description": "Message describing a BackupVault object.", "id": "<PERSON><PERSON><PERSON><PERSON>", "properties": {"accessRestriction": {"description": "Optional. Note: This field is added for future use case and will not be supported in the current release. Access restriction for the backup vault. Default value is WITHIN_ORGANIZATION if not provided during creation.", "enum": ["ACCESS_RESTRICTION_UNSPECIFIED", "WITHIN_PROJECT", "WITHIN_ORGANIZATION", "UNRESTRICTED", "WITHIN_ORG_BUT_UNRESTRICTED_FOR_BA"], "enumDescriptions": ["Access restriction not set. If user does not provide any value or pass this value, it will be changed to WITHIN_ORGANIZATION.", "Access to or from resources outside your current project will be denied.", "Access to or from resources outside your current organization will be denied.", "No access restriction.", "Access to or from resources outside your current organization will be denied except for backup appliance."], "type": "string"}, "annotations": {"additionalProperties": {"type": "string"}, "description": "Optional. User annotations. See https://google.aip.dev/128#annotations Stores small amounts of arbitrary data.", "type": "object"}, "backupCount": {"description": "Output only. The number of backups in this backup vault.", "format": "int64", "readOnly": true, "type": "string"}, "backupMinimumEnforcedRetentionDuration": {"description": "Required. The default and minimum enforced retention for each backup within the backup vault. The enforced retention for each backup can be extended.", "format": "google-duration", "type": "string"}, "createTime": {"description": "Output only. The time when the instance was created.", "format": "google-datetime", "readOnly": true, "type": "string"}, "deletable": {"description": "Output only. Set to true when there are no backups nested under this resource.", "readOnly": true, "type": "boolean"}, "description": {"description": "Optional. The description of the BackupVault instance (2048 characters or less).", "type": "string"}, "effectiveTime": {"description": "Optional. Time after which the BackupVault resource is locked.", "format": "google-datetime", "type": "string"}, "etag": {"description": "Optional. Server specified ETag for the backup vault resource to prevent simultaneous updates from overwiting each other.", "type": "string"}, "labels": {"additionalProperties": {"type": "string"}, "description": "Optional. Resource labels to represent user provided metadata. No labels currently defined:", "type": "object"}, "name": {"description": "Output only. Identifier. Name of the backup vault to create. It must have the format`\"projects/{project}/locations/{location}/backupVaults/{backupvault}\"`. `{backupvault}` cannot be changed after creation. It must be between 3-63 characters long and must be unique within the project and location.", "readOnly": true, "type": "string"}, "serviceAccount": {"description": "Output only. Service account used by the BackupVault Service for this BackupVault. The user should grant this account permissions in their workload project to enable the service to run backups and restores there.", "readOnly": true, "type": "string"}, "state": {"description": "Output only. The BackupVault resource instance state.", "enum": ["STATE_UNSPECIFIED", "CREATING", "ACTIVE", "DELETING", "ERROR", "UPDATING"], "enumDescriptions": ["State not set.", "The backup vault is being created.", "The backup vault has been created and is fully usable.", "The backup vault is being deleted.", "The backup vault is experiencing an issue and might be unusable.", "The backup vault is being updated."], "readOnly": true, "type": "string"}, "totalStoredBytes": {"description": "Output only. Total size of the storage used by all backup resources.", "format": "int64", "readOnly": true, "type": "string"}, "uid": {"description": "Output only. Immutable after resource creation until resource deletion.", "readOnly": true, "type": "string"}, "updateTime": {"description": "Output only. The time when the instance was updated.", "format": "google-datetime", "readOnly": true, "type": "string"}}, "type": "object"}, "BackupWindow": {"description": "`BackupWindow` defines a window of the day during which backup jobs will run.", "id": "BackupWindow", "properties": {"endHourOfDay": {"description": "Required. The hour of day (1-24) when the window end for e.g. if value of end hour of day is 10 that mean backup window end time is 10:00. End hour of day should be greater than start hour of day. 0 <= start_hour_of_day < end_hour_of_day <= 24 End hour of day is not include in backup window that mean if end_hour_of_day= 10 jobs should start before 10:00.", "format": "int32", "type": "integer"}, "startHourOfDay": {"description": "Required. The hour of day (0-23) when the window starts for e.g. if value of start hour of day is 6 that mean backup window start at 6:00.", "format": "int32", "type": "integer"}}, "type": "object"}, "Binding": {"description": "Associates `members`, or principals, with a `role`.", "id": "Binding", "properties": {"condition": {"$ref": "Expr", "description": "The condition that is associated with this binding. If the condition evaluates to `true`, then this binding applies to the current request. If the condition evaluates to `false`, then this binding does not apply to the current request. However, a different role binding might grant the same role to one or more of the principals in this binding. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies)."}, "members": {"description": "Specifies the principals requesting access for a Google Cloud resource. `members` can have the following values: * `allUsers`: A special identifier that represents anyone who is on the internet; with or without a Google account. * `allAuthenticatedUsers`: A special identifier that represents anyone who is authenticated with a Google account or a service account. Does not include identities that come from external identity providers (IdPs) through identity federation. * `user:{emailid}`: An email address that represents a specific Google account. For example, `<EMAIL>` . * `serviceAccount:{emailid}`: An email address that represents a Google service account. For example, `<EMAIL>`. * `serviceAccount:{projectid}.svc.id.goog[{namespace}/{kubernetes-sa}]`: An identifier for a [Kubernetes service account](https://cloud.google.com/kubernetes-engine/docs/how-to/kubernetes-service-accounts). For example, `my-project.svc.id.goog[my-namespace/my-kubernetes-sa]`. * `group:{emailid}`: An email address that represents a Google group. For example, `<EMAIL>`. * `domain:{domain}`: The G Suite domain (primary) that represents all the users of that domain. For example, `google.com` or `example.com`. * `principal://iam.googleapis.com/locations/global/workforcePools/{pool_id}/subject/{subject_attribute_value}`: A single identity in a workforce identity pool. * `principalSet://iam.googleapis.com/locations/global/workforcePools/{pool_id}/group/{group_id}`: All workforce identities in a group. * `principalSet://iam.googleapis.com/locations/global/workforcePools/{pool_id}/attribute.{attribute_name}/{attribute_value}`: All workforce identities with a specific attribute value. * `principalSet://iam.googleapis.com/locations/global/workforcePools/{pool_id}/*`: All identities in a workforce identity pool. * `principal://iam.googleapis.com/projects/{project_number}/locations/global/workloadIdentityPools/{pool_id}/subject/{subject_attribute_value}`: A single identity in a workload identity pool. * `principalSet://iam.googleapis.com/projects/{project_number}/locations/global/workloadIdentityPools/{pool_id}/group/{group_id}`: A workload identity pool group. * `principalSet://iam.googleapis.com/projects/{project_number}/locations/global/workloadIdentityPools/{pool_id}/attribute.{attribute_name}/{attribute_value}`: All identities in a workload identity pool with a certain attribute. * `principalSet://iam.googleapis.com/projects/{project_number}/locations/global/workloadIdentityPools/{pool_id}/*`: All identities in a workload identity pool. * `deleted:user:{emailid}?uid={uniqueid}`: An email address (plus unique identifier) representing a user that has been recently deleted. For example, `<EMAIL>?uid=123456789012345678901`. If the user is recovered, this value reverts to `user:{emailid}` and the recovered user retains the role in the binding. * `deleted:serviceAccount:{emailid}?uid={uniqueid}`: An email address (plus unique identifier) representing a service account that has been recently deleted. For example, `<EMAIL>?uid=123456789012345678901`. If the service account is undeleted, this value reverts to `serviceAccount:{emailid}` and the undeleted service account retains the role in the binding. * `deleted:group:{emailid}?uid={uniqueid}`: An email address (plus unique identifier) representing a Google group that has been recently deleted. For example, `<EMAIL>?uid=123456789012345678901`. If the group is recovered, this value reverts to `group:{emailid}` and the recovered group retains the role in the binding. * `deleted:principal://iam.googleapis.com/locations/global/workforcePools/{pool_id}/subject/{subject_attribute_value}`: Deleted single identity in a workforce identity pool. For example, `deleted:principal://iam.googleapis.com/locations/global/workforcePools/my-pool-id/subject/my-subject-attribute-value`.", "items": {"type": "string"}, "type": "array"}, "role": {"description": "Role that is assigned to the list of `members`, or principals. For example, `roles/viewer`, `roles/editor`, or `roles/owner`. For an overview of the IAM roles and permissions, see the [IAM documentation](https://cloud.google.com/iam/docs/roles-overview). For a list of the available pre-defined roles, see [here](https://cloud.google.com/iam/docs/understanding-roles).", "type": "string"}}, "type": "object"}, "CancelOperationRequest": {"description": "The request message for Operations.CancelOperation.", "id": "CancelOperationRequest", "properties": {}, "type": "object"}, "CloudSqlInstanceBackupPlanAssociationProperties": {"description": "Cloud SQL instance's BPA properties.", "id": "CloudSqlInstanceBackupPlanAssociationProperties", "properties": {"instanceCreateTime": {"description": "Output only. The time when the instance was created.", "format": "google-datetime", "readOnly": true, "type": "string"}}, "type": "object"}, "CloudSqlInstanceBackupProperties": {"description": "CloudSqlInstanceBackupProperties represents Cloud SQL Instance Backup properties.", "id": "CloudSqlInstanceBackupProperties", "properties": {"databaseInstalledVersion": {"description": "Output only. The installed database version of the Cloud SQL instance when the backup was taken.", "readOnly": true, "type": "string"}, "finalBackup": {"description": "Output only. Whether the backup is a final backup.", "readOnly": true, "type": "boolean"}, "instanceTier": {"description": "Output only. The tier (or machine type) for this instance. Example: `db-custom-1-3840`", "readOnly": true, "type": "string"}, "sourceInstance": {"description": "Output only. The source instance of the backup. Format: projects/{project}/instances/{instance}", "readOnly": true, "type": "string"}}, "type": "object"}, "CloudSqlInstanceDataSourceProperties": {"description": "CloudSqlInstanceDataSourceProperties represents the properties of a Cloud SQL resource that are stored in the DataSource.", "id": "CloudSqlInstanceDataSourceProperties", "properties": {"databaseInstalledVersion": {"description": "Output only. The installed database version of the Cloud SQL instance.", "readOnly": true, "type": "string"}, "instanceCreateTime": {"description": "Output only. The instance creation timestamp.", "format": "google-datetime", "readOnly": true, "type": "string"}, "instanceTier": {"description": "Output only. The tier (or machine type) for this instance. Example: `db-custom-1-3840`", "readOnly": true, "type": "string"}, "name": {"description": "Output only. Name of the Cloud SQL instance backed up by the datasource. Format: projects/{project}/instances/{instance}", "readOnly": true, "type": "string"}}, "type": "object"}, "CloudSqlInstanceDataSourceReferenceProperties": {"description": "CloudSqlInstanceDataSourceReferenceProperties represents the properties of a Cloud SQL resource that are stored in the DataSourceReference.", "id": "CloudSqlInstanceDataSourceReferenceProperties", "properties": {"databaseInstalledVersion": {"description": "Output only. The installed database version of the Cloud SQL instance.", "readOnly": true, "type": "string"}, "instanceCreateTime": {"description": "Output only. The instance creation timestamp.", "format": "google-datetime", "readOnly": true, "type": "string"}, "instanceTier": {"description": "Output only. The tier (or machine type) for this instance. Example: `db-custom-1-3840`", "readOnly": true, "type": "string"}, "name": {"description": "Output only. Name of the Cloud SQL instance backed up by the datasource. Format: projects/{project}/instances/{instance}", "readOnly": true, "type": "string"}}, "type": "object"}, "CloudSqlInstanceInitializationConfig": {"description": "CloudSqlInstanceInitializationConfig contains the configuration for initializing a Cloud SQL instance.", "id": "CloudSqlInstanceInitializationConfig", "properties": {"edition": {"description": "Required. The edition of the Cloud SQL instance.", "enum": ["EDITION_UNSPECIFIED", "ENTERPRISE", "ENTERPRISE_PLUS"], "enumDescriptions": ["Unspecified edition.", "Enterprise edition.", "Enterprise Plus edition."], "type": "string"}}, "type": "object"}, "ComputeInstanceBackupProperties": {"description": "ComputeInstanceBackupProperties represents Compute Engine instance backup properties.", "id": "ComputeInstanceBackupProperties", "properties": {"canIpForward": {"description": "Enables instances created based on these properties to send packets with source IP addresses other than their own and receive packets with destination IP addresses other than their own. If these instances will be used as an IP gateway or it will be set as the next-hop in a Route resource, specify `true`. If unsure, leave this set to `false`. See the https://cloud.google.com/vpc/docs/using-routes#canipforward documentation for more information.", "type": "boolean"}, "description": {"description": "An optional text description for the instances that are created from these properties.", "type": "string"}, "disk": {"description": "An array of disks that are associated with the instances that are created from these properties.", "items": {"$ref": "AttachedDisk"}, "type": "array"}, "guestAccelerator": {"description": "A list of guest accelerator cards' type and count to use for instances created from these properties.", "items": {"$ref": "AcceleratorConfig"}, "type": "array"}, "keyRevocationActionType": {"description": "KeyRevocationActionType of the instance. Supported options are \"STOP\" and \"NONE\". The default value is \"NONE\" if it is not specified.", "enum": ["KEY_REVOCATION_ACTION_TYPE_UNSPECIFIED", "NONE", "STOP"], "enumDescriptions": ["Default value. This value is unused.", "Indicates user chose no operation.", "Indicates user chose to opt for VM shutdown on key revocation."], "type": "string"}, "labels": {"additionalProperties": {"type": "string"}, "description": "Labels to apply to instances that are created from these properties.", "type": "object"}, "machineType": {"description": "The machine type to use for instances that are created from these properties.", "type": "string"}, "metadata": {"$ref": "<PERSON><PERSON><PERSON>", "description": "The metadata key/value pairs to assign to instances that are created from these properties. These pairs can consist of custom metadata or predefined keys. See https://cloud.google.com/compute/docs/metadata/overview for more information."}, "minCpuPlatform": {"description": "Minimum cpu/platform to be used by instances. The instance may be scheduled on the specified or newer cpu/platform. Applicable values are the friendly names of CPU platforms, such as `minCpuPlatform: Intel Haswell` or `minCpuPlatform: Intel Sandy Bridge`. For more information, read https://cloud.google.com/compute/docs/instances/specify-min-cpu-platform.", "type": "string"}, "networkInterface": {"description": "An array of network access configurations for this interface.", "items": {"$ref": "NetworkInterface"}, "type": "array"}, "scheduling": {"$ref": "Scheduling", "description": "Specifies the scheduling options for the instances that are created from these properties."}, "serviceAccount": {"description": "A list of service accounts with specified scopes. Access tokens for these service accounts are available to the instances that are created from these properties. Use metadata queries to obtain the access tokens for these instances.", "items": {"$ref": "ServiceAccount"}, "type": "array"}, "sourceInstance": {"description": "The source instance used to create this backup. This can be a partial or full URL to the resource. For example, the following are valid values: -https://www.googleapis.com/compute/v1/projects/project/zones/zone/instances/instance -projects/project/zones/zone/instances/instance", "type": "string"}, "tags": {"$ref": "Tags", "description": "A list of tags to apply to the instances that are created from these properties. The tags identify valid sources or targets for network firewalls. The setTags method can modify this list of tags. Each tag within the list must comply with RFC1035 (https://www.ietf.org/rfc/rfc1035.txt)."}}, "type": "object"}, "ComputeInstanceDataSourceProperties": {"description": "ComputeInstanceDataSourceProperties represents the properties of a ComputeEngine resource that are stored in the DataSource.", "id": "ComputeInstanceDataSourceProperties", "properties": {"description": {"description": "The description of the Compute Engine instance.", "type": "string"}, "machineType": {"description": "The machine type of the instance.", "type": "string"}, "name": {"description": "Name of the compute instance backed up by the datasource.", "type": "string"}, "totalDiskCount": {"description": "The total number of disks attached to the Instance.", "format": "int64", "type": "string"}, "totalDiskSizeGb": {"description": "The sum of all the disk sizes.", "format": "int64", "type": "string"}}, "type": "object"}, "ComputeInstanceRestoreProperties": {"description": "ComputeInstanceRestoreProperties represents Compute Engine instance properties to be overridden during restore.", "id": "ComputeInstanceRestoreProperties", "properties": {"advancedMachineFeatures": {"$ref": "AdvancedMachineFeatures", "description": "Optional. Controls for advanced machine-related behavior features."}, "canIpForward": {"description": "Optional. Allows this instance to send and receive packets with non-matching destination or source IPs.", "type": "boolean"}, "confidentialInstanceConfig": {"$ref": "ConfidentialInstanceConfig", "description": "Optional. Controls Confidential compute options on the instance"}, "deletionProtection": {"description": "Optional. Whether the resource should be protected against deletion.", "type": "boolean"}, "description": {"description": "Optional. An optional description of this resource. Provide this property when you create the resource.", "type": "string"}, "disks": {"description": "Optional. Array of disks associated with this instance. Persistent disks must be created before you can assign them. Source regional persistent disks will be restored with default replica zones if not specified.", "items": {"$ref": "AttachedDisk"}, "type": "array"}, "displayDevice": {"$ref": "DisplayDevice", "description": "Optional. Enables display device for the instance."}, "guestAccelerators": {"description": "Optional. A list of the type and count of accelerator cards attached to the instance.", "items": {"$ref": "AcceleratorConfig"}, "type": "array"}, "hostname": {"description": "Optional. Specifies the hostname of the instance. The specified hostname must be RFC1035 compliant. If hostname is not specified, the default hostname is [INSTANCE_NAME].c.[PROJECT_ID].internal when using the global DNS, and [INSTANCE_NAME].[ZONE].c.[PROJECT_ID].internal when using zonal DNS.", "type": "string"}, "instanceEncryptionKey": {"$ref": "CustomerEncryptionKey", "description": "Optional. Encrypts suspended data for an instance with a customer-managed encryption key."}, "keyRevocationActionType": {"description": "Optional. KeyRevocationActionType of the instance.", "enum": ["KEY_REVOCATION_ACTION_TYPE_UNSPECIFIED", "NONE", "STOP"], "enumDescriptions": ["Default value. This value is unused.", "Indicates user chose no operation.", "Indicates user chose to opt for VM shutdown on key revocation."], "type": "string"}, "labels": {"additionalProperties": {"type": "string"}, "description": "Optional. Labels to apply to this instance.", "type": "object"}, "machineType": {"description": "Optional. Full or partial URL of the machine type resource to use for this instance.", "type": "string"}, "metadata": {"$ref": "<PERSON><PERSON><PERSON>", "description": "Optional. This includes custom metadata and predefined keys."}, "minCpuPlatform": {"description": "Optional. Minimum CPU platform to use for this instance.", "type": "string"}, "name": {"description": "Required. Name of the compute instance.", "type": "string"}, "networkInterfaces": {"description": "Optional. An array of network configurations for this instance. These specify how interfaces are configured to interact with other network services, such as connecting to the internet. Multiple interfaces are supported per instance. Required to restore in different project or region.", "items": {"$ref": "NetworkInterface"}, "type": "array"}, "networkPerformanceConfig": {"$ref": "NetworkPerformanceConfig", "description": "Optional. Configure network performance such as egress bandwidth tier."}, "params": {"$ref": "InstanceParams", "description": "Input only. Additional params passed with the request, but not persisted as part of resource payload."}, "privateIpv6GoogleAccess": {"description": "Optional. The private IPv6 google access type for the VM. If not specified, use INHERIT_FROM_SUBNETWORK as default.", "enum": ["INSTANCE_PRIVATE_IPV6_GOOGLE_ACCESS_UNSPECIFIED", "INHERIT_FROM_SUBNETWORK", "ENABLE_OUTBOUND_VM_ACCESS_TO_GOOGLE", "ENABLE_BIDIRECTIONAL_ACCESS_TO_GOOGLE"], "enumDescriptions": ["Default value. This value is unused.", "Each network interface inherits PrivateIpv6GoogleAccess from its subnetwork.", "Outbound private IPv6 access from VMs in this subnet to Google services. If specified, the subnetwork who is attached to the instance's default network interface will be assigned an internal IPv6 prefix if it doesn't have before.", "Bidirectional private IPv6 access to/from Google services. If specified, the subnetwork who is attached to the instance's default network interface will be assigned an internal IPv6 prefix if it doesn't have before."], "type": "string"}, "reservationAffinity": {"$ref": "AllocationAffinity", "description": "Optional. Specifies the reservations that this instance can consume from."}, "resourcePolicies": {"description": "Optional. Resource policies applied to this instance. By default, no resource policies will be applied.", "items": {"type": "string"}, "type": "array"}, "scheduling": {"$ref": "Scheduling", "description": "Optional. Sets the scheduling options for this instance."}, "serviceAccounts": {"description": "Optional. A list of service accounts, with their specified scopes, authorized for this instance. Only one service account per VM instance is supported.", "items": {"$ref": "ServiceAccount"}, "type": "array"}, "tags": {"$ref": "Tags", "description": "Optional. Tags to apply to this instance. Tags are used to identify valid sources or targets for network firewalls and are specified by the client during instance creation."}}, "type": "object"}, "ComputeInstanceTargetEnvironment": {"description": "ComputeInstanceTargetEnvironment represents Compute Engine target environment to be used during restore.", "id": "ComputeInstanceTargetEnvironment", "properties": {"project": {"description": "Required. Target project for the Compute Engine instance.", "type": "string"}, "zone": {"description": "Required. The zone of the Compute Engine instance.", "type": "string"}}, "type": "object"}, "ConfidentialInstanceConfig": {"description": "A set of Confidential Instance options.", "id": "ConfidentialInstanceConfig", "properties": {"enableConfidentialCompute": {"description": "Optional. Defines whether the instance should have confidential compute enabled.", "type": "boolean"}}, "type": "object"}, "CustomerEncryptionKey": {"description": "A customer-supplied encryption key.", "id": "CustomerEncryptionKey", "properties": {"kmsKeyName": {"description": "Optional. The name of the encryption key that is stored in Google Cloud KMS.", "type": "string"}, "kmsKeyServiceAccount": {"description": "Optional. The service account being used for the encryption request for the given KMS key. If absent, the Compute Engine default service account is used.", "type": "string"}, "rawKey": {"description": "Optional. Specifies a 256-bit customer-supplied encryption key.", "type": "string"}, "rsaEncryptedKey": {"description": "Optional. RSA-wrapped 2048-bit customer-supplied encryption key to either encrypt or decrypt this resource.", "type": "string"}}, "type": "object"}, "DataSource": {"description": "Message describing a DataSource object. Datasource object used to represent Datasource details for both admin and basic view.", "id": "DataSource", "properties": {"backupBlockedByVaultAccessRestriction": {"description": "Output only. This field is set to true if the backup is blocked by vault access restriction.", "readOnly": true, "type": "boolean"}, "backupConfigInfo": {"$ref": "BackupConfigInfo", "description": "Output only. Details of how the resource is configured for backup.", "readOnly": true}, "backupCount": {"description": "Number of backups in the data source.", "format": "int64", "type": "string"}, "configState": {"description": "Output only. The backup configuration state.", "enum": ["BACKUP_CONFIG_STATE_UNSPECIFIED", "ACTIVE", "PASSIVE"], "enumDescriptions": ["The possible states of backup configuration. Status not set.", "The data source is actively protected (i.e. there is a BackupPlanAssociation or Appliance SLA pointing to it)", "The data source is no longer protected (but may have backups under it)"], "readOnly": true, "type": "string"}, "createTime": {"description": "Output only. The time when the instance was created.", "format": "google-datetime", "readOnly": true, "type": "string"}, "dataSourceBackupApplianceApplication": {"$ref": "DataSourceBackupApplianceApplication", "description": "The backed up resource is a backup appliance application."}, "dataSourceGcpResource": {"$ref": "DataSourceGcpResource", "description": "The backed up resource is a Google Cloud resource. The word 'DataSource' was included in the names to indicate that this is the representation of the Google Cloud resource used within the DataSource object."}, "etag": {"description": "Server specified ETag for the ManagementServer resource to prevent simultaneous updates from overwiting each other.", "type": "string"}, "labels": {"additionalProperties": {"type": "string"}, "description": "Optional. Resource labels to represent user provided metadata. No labels currently defined:", "type": "object"}, "name": {"description": "Output only. Identifier. Name of the datasource to create. It must have the format`\"projects/{project}/locations/{location}/backupVaults/{backupvault}/dataSources/{datasource}\"`. `{datasource}` cannot be changed after creation. It must be between 3-63 characters long and must be unique within the backup vault.", "readOnly": true, "type": "string"}, "state": {"description": "Output only. The DataSource resource instance state.", "enum": ["STATE_UNSPECIFIED", "CREATING", "ACTIVE", "DELETING", "ERROR"], "enumDescriptions": ["State not set.", "The data source is being created.", "The data source has been created and is fully usable.", "The data source is being deleted.", "The data source is experiencing an issue and might be unusable."], "readOnly": true, "type": "string"}, "totalStoredBytes": {"description": "The number of bytes (metadata and data) stored in this datasource.", "format": "int64", "type": "string"}, "updateTime": {"description": "Output only. The time when the instance was updated.", "format": "google-datetime", "readOnly": true, "type": "string"}}, "type": "object"}, "DataSourceBackupApplianceApplication": {"description": "BackupApplianceApplication describes a Source Resource when it is an application backed up by a BackupAppliance.", "id": "DataSourceBackupApplianceApplication", "properties": {"applianceId": {"description": "Appliance Id of the Backup Appliance.", "format": "int64", "type": "string"}, "applicationId": {"description": "The appid field of the application within the Backup Appliance.", "format": "int64", "type": "string"}, "applicationName": {"description": "The name of the Application as known to the Backup Appliance.", "type": "string"}, "backupAppliance": {"description": "Appliance name.", "type": "string"}, "hostId": {"description": "Hostid of the application host.", "format": "int64", "type": "string"}, "hostname": {"description": "Hostname of the host where the application is running.", "type": "string"}, "type": {"description": "The type of the application. e.g. VMBackup", "type": "string"}}, "type": "object"}, "DataSourceBackupConfigInfo": {"description": "Information of backup configuration on the DataSource.", "id": "DataSourceBackupConfigInfo", "properties": {"lastBackupState": {"description": "Output only. The status of the last backup in this DataSource", "enum": ["LAST_BACKUP_STATE_UNSPECIFIED", "FIRST_BACKUP_PENDING", "SUCCEEDED", "FAILED", "PERMISSION_DENIED"], "enumDescriptions": ["Status not set.", "The first backup has not yet completed", "The most recent backup was successful", "The most recent backup failed", "The most recent backup could not be run/failed because of the lack of permissions"], "readOnly": true, "type": "string"}, "lastSuccessfulBackupConsistencyTime": {"description": "Output only. Timestamp of the last successful backup to this DataSource.", "format": "google-datetime", "readOnly": true, "type": "string"}}, "type": "object"}, "DataSourceGcpResource": {"description": "DataSourceGcpResource is used for protected resources that are Google Cloud Resources. This name is easeier to understand than GcpResourceDataSource or GcpDataSourceResource", "id": "DataSourceGcpResource", "properties": {"cloudSqlInstanceDatasourceProperties": {"$ref": "CloudSqlInstanceDataSourceProperties", "description": "Output only. CloudSqlInstanceDataSourceProperties has a subset of Cloud SQL Instance properties that are useful at the Datasource level.", "readOnly": true}, "computeInstanceDatasourceProperties": {"$ref": "ComputeInstanceDataSourceProperties", "description": "ComputeInstanceDataSourceProperties has a subset of Compute Instance properties that are useful at the Datasource level."}, "diskDatasourceProperties": {"$ref": "DiskDataSourceProperties", "description": "DiskDataSourceProperties has a subset of Disk properties that are useful at the Datasource level."}, "gcpResourcename": {"description": "Output only. Full resource pathname URL of the source Google Cloud resource.", "readOnly": true, "type": "string"}, "location": {"description": "Location of the resource: //\"global\"/\"unspecified\".", "type": "string"}, "type": {"description": "The type of the Google Cloud resource. Use the Unified Resource Type, eg. compute.googleapis.com/Instance.", "type": "string"}}, "type": "object"}, "DataSourceGcpResourceInfo": {"description": "The GCP resource that the DataSource is associated with.", "id": "DataSourceGcpResourceInfo", "properties": {"cloudSqlInstanceProperties": {"$ref": "CloudSqlInstanceDataSourceReferenceProperties", "description": "Output only. The properties of the Cloud SQL instance.", "readOnly": true}, "gcpResourcename": {"description": "Output only. The resource name of the GCP resource. Ex: projects/{project}/zones/{zone}/instances/{instance}", "readOnly": true, "type": "string"}, "location": {"description": "Output only. The location of the GCP resource. Ex: //\"global\"/\"unspecified\"", "readOnly": true, "type": "string"}, "type": {"description": "Output only. The type of the GCP resource. Ex: compute.googleapis.com/Instance", "readOnly": true, "type": "string"}}, "type": "object"}, "DataSourceReference": {"description": "DataSourceReference is a reference to a DataSource resource.", "id": "DataSourceReference", "properties": {"createTime": {"description": "Output only. The time when the DataSourceReference was created.", "format": "google-datetime", "readOnly": true, "type": "string"}, "dataSource": {"description": "Output only. The resource name of the DataSource. Format: projects/{project}/locations/{location}/backupVaults/{backupVault}/dataSources/{dataSource}", "readOnly": true, "type": "string"}, "dataSourceBackupConfigInfo": {"$ref": "DataSourceBackupConfigInfo", "description": "Output only. Information of backup configuration on the DataSource.", "readOnly": true}, "dataSourceBackupConfigState": {"description": "Output only. The backup configuration state of the DataSource.", "enum": ["BACKUP_CONFIG_STATE_UNSPECIFIED", "ACTIVE", "PASSIVE"], "enumDescriptions": ["The possible states of backup configuration. Status not set.", "The data source is actively protected (i.e. there is a BackupPlanAssociation or Appliance SLA pointing to it)", "The data source is no longer protected (but may have backups under it)"], "readOnly": true, "type": "string"}, "dataSourceBackupCount": {"description": "Output only. Number of backups in the DataSource.", "format": "int64", "readOnly": true, "type": "string"}, "dataSourceGcpResourceInfo": {"$ref": "DataSourceGcpResourceInfo", "description": "Output only. The GCP resource that the DataSource is associated with.", "readOnly": true}, "name": {"description": "Identifier. The resource name of the DataSourceReference. Format: projects/{project}/locations/{location}/dataSourceReferences/{data_source_reference}", "type": "string"}}, "type": "object"}, "DiskBackupProperties": {"description": "DiskBackupProperties represents the properties of a Disk backup.", "id": "DiskBackupProperties", "properties": {"architecture": {"description": "The architecture of the source disk. Valid values are ARM64 or X86_64.", "enum": ["ARCHITECTURE_UNSPECIFIED", "X86_64", "ARM64"], "enumDescriptions": ["Default value. This value is unused.", "Disks with architecture X86_64", "Disks with architecture ARM64"], "type": "string"}, "description": {"description": "A description of the source disk.", "type": "string"}, "guestOsFeature": {"description": "A list of guest OS features that are applicable to this backup.", "items": {"$ref": "GuestOsFeature"}, "type": "array"}, "licenses": {"description": "A list of publicly available licenses that are applicable to this backup. This is applicable if the original image had licenses attached, e.g. Windows image.", "items": {"type": "string"}, "type": "array"}, "region": {"description": "Region and zone are mutually exclusive fields. The URL of the region of the source disk.", "type": "string"}, "replicaZones": {"description": "The URL of the Zones where the source disk should be replicated.", "items": {"type": "string"}, "type": "array"}, "sizeGb": {"description": "Size(in GB) of the source disk.", "format": "int64", "type": "string"}, "sourceDisk": {"description": "The source disk used to create this backup.", "type": "string"}, "type": {"description": "The URL of the type of the disk.", "type": "string"}, "zone": {"description": "The URL of the Zone where the source disk.", "type": "string"}}, "type": "object"}, "DiskDataSourceProperties": {"description": "DiskDataSourceProperties represents the properties of a Disk resource that are stored in the DataSource. .", "id": "DiskDataSourceProperties", "properties": {"description": {"description": "The description of the disk.", "type": "string"}, "name": {"description": "Name of the disk backed up by the datasource.", "type": "string"}, "sizeGb": {"description": "The size of the disk in GB.", "format": "int64", "type": "string"}, "type": {"description": "The type of the disk.", "type": "string"}}, "type": "object"}, "DiskRestoreProperties": {"description": "DiskRestoreProperties represents the properties of a Disk restore.", "id": "DiskRestoreProperties", "properties": {"accessMode": {"description": "Optional. The access mode of the disk.", "enum": ["READ_WRITE_SINGLE", "READ_WRITE_MANY", "READ_ONLY_MANY"], "enumDescriptions": ["The default AccessMode, means the disk can be attached to single instance in RW mode.", "The AccessMode means the disk can be attached to multiple instances in RW mode.", "The AccessMode means the disk can be attached to multiple instances in RO mode."], "type": "string"}, "architecture": {"description": "Optional. The architecture of the source disk. Valid values are ARM64 or X86_64.", "enum": ["ARCHITECTURE_UNSPECIFIED", "X86_64", "ARM64"], "enumDescriptions": ["Default value. This value is unused.", "Disks with architecture X86_64", "Disks with architecture ARM64"], "type": "string"}, "description": {"description": "Optional. An optional description of this resource. Provide this property when you create the resource.", "type": "string"}, "diskEncryptionKey": {"$ref": "CustomerEncryptionKey", "description": "Optional. Encrypts the disk using a customer-supplied encryption key or a customer-managed encryption key."}, "enableConfidentialCompute": {"description": "Optional. Indicates whether this disk is using confidential compute mode. Encryption with a Cloud KMS key is required to enable this option.", "type": "boolean"}, "guestOsFeature": {"description": "Optional. A list of features to enable in the guest operating system. This is applicable only for bootable images.", "items": {"$ref": "GuestOsFeature"}, "type": "array"}, "labels": {"additionalProperties": {"type": "string"}, "description": "Optional. Labels to apply to this disk. These can be modified later using setLabels method. Label values can be empty.", "type": "object"}, "licenses": {"description": "Optional. A list of publicly available licenses that are applicable to this backup. This is applicable if the original image had licenses attached, e.g. Windows image", "items": {"type": "string"}, "type": "array"}, "name": {"description": "Required. Name of the disk.", "type": "string"}, "physicalBlockSizeBytes": {"description": "Optional. Physical block size of the persistent disk, in bytes. If not present in a request, a default value is used. Currently, the supported size is 4096.", "format": "int64", "type": "string"}, "provisionedIops": {"description": "Optional. Indicates how many IOPS to provision for the disk. This sets the number of I/O operations per second that the disk can handle.", "format": "int64", "type": "string"}, "provisionedThroughput": {"description": "Optional. Indicates how much throughput to provision for the disk. This sets the number of throughput MB per second that the disk can handle.", "format": "int64", "type": "string"}, "resourceManagerTags": {"additionalProperties": {"type": "string"}, "description": "Optional. Resource manager tags to be bound to the disk.", "type": "object"}, "resourcePolicy": {"description": "Optional. Resource policies applied to this disk.", "items": {"type": "string"}, "type": "array"}, "sizeGb": {"description": "Required. The size of the disk in GB.", "format": "int64", "type": "string"}, "storagePool": {"description": "Optional. The storage pool in which the new disk is created. You can provide this as a partial or full URL to the resource.", "type": "string"}, "type": {"description": "Required. URL of the disk type resource describing which disk type to use to create the disk.", "type": "string"}}, "type": "object"}, "DiskTargetEnvironment": {"description": "DiskTargetEnvironment represents the target environment for the disk.", "id": "DiskTargetEnvironment", "properties": {"project": {"description": "Required. Target project for the disk.", "type": "string"}, "zone": {"description": "Required. Target zone for the disk.", "type": "string"}}, "type": "object"}, "DisplayDevice": {"description": "A set of Display Device options", "id": "DisplayDevice", "properties": {"enableDisplay": {"description": "Optional. Enables display for the Compute Engine VM", "type": "boolean"}}, "type": "object"}, "Empty": {"description": "A generic empty message that you can re-use to avoid defining duplicated empty messages in your APIs. A typical example is to use it as the request or the response type of an API method. For instance: service Foo { rpc Bar(google.protobuf.Empty) returns (google.protobuf.Empty); }", "id": "Empty", "properties": {}, "type": "object"}, "Entry": {"description": "A key/value pair to be used for storing metadata.", "id": "Entry", "properties": {"key": {"description": "Optional. Key for the metadata entry.", "type": "string"}, "value": {"description": "Optional. Value for the metadata entry. These are free-form strings, and only have meaning as interpreted by the image running in the instance. The only restriction placed on values is that their size must be less than or equal to 262144 bytes (256 KiB).", "type": "string"}}, "type": "object"}, "Expr": {"description": "Represents a textual expression in the Common Expression Language (CEL) syntax. CEL is a C-like expression language. The syntax and semantics of CEL are documented at https://github.com/google/cel-spec. Example (Comparison): title: \"Summary size limit\" description: \"Determines if a summary is less than 100 chars\" expression: \"document.summary.size() < 100\" Example (Equality): title: \"Requestor is owner\" description: \"Determines if requestor is the document owner\" expression: \"document.owner == request.auth.claims.email\" Example (Logic): title: \"Public documents\" description: \"Determine whether the document should be publicly visible\" expression: \"document.type != 'private' && document.type != 'internal'\" Example (Data Manipulation): title: \"Notification string\" description: \"Create a notification string with a timestamp.\" expression: \"'New message received at ' + string(document.create_time)\" The exact variables and functions that may be referenced within an expression are determined by the service that evaluates it. See the service documentation for additional information.", "id": "Expr", "properties": {"description": {"description": "Optional. Description of the expression. This is a longer text which describes the expression, e.g. when hovered over it in a UI.", "type": "string"}, "expression": {"description": "Textual representation of an expression in Common Expression Language syntax.", "type": "string"}, "location": {"description": "Optional. String indicating the location of the expression for error reporting, e.g. a file name and a position in the file.", "type": "string"}, "title": {"description": "Optional. Title for the expression, i.e. a short string describing its purpose. This can be used e.g. in UIs which allow to enter the expression.", "type": "string"}}, "type": "object"}, "FetchAccessTokenRequest": {"description": "Request message for FetchAccessToken.", "id": "FetchAccessTokenRequest", "properties": {"generationId": {"description": "Required. The generation of the backup to update.", "format": "int32", "type": "integer"}}, "type": "object"}, "FetchAccessTokenResponse": {"description": "Response message for FetchAccessToken.", "id": "FetchAccessTokenResponse", "properties": {"expireTime": {"description": "The token is valid until this time.", "format": "google-datetime", "type": "string"}, "readLocation": {"description": "The location in bucket that can be used for reading.", "type": "string"}, "token": {"description": "The downscoped token that was created.", "type": "string"}, "writeLocation": {"description": "The location in bucket that can be used for writing.", "type": "string"}}, "type": "object"}, "FetchBackupPlanAssociationsForResourceTypeResponse": {"description": "Response for the FetchBackupPlanAssociationsForResourceType method.", "id": "FetchBackupPlanAssociationsForResourceTypeResponse", "properties": {"backupPlanAssociations": {"description": "Output only. The BackupPlanAssociations from the specified parent.", "items": {"$ref": "BackupPlanAssociation"}, "readOnly": true, "type": "array"}, "nextPageToken": {"description": "Output only. A token, which can be sent as `page_token` to retrieve the next page. If this field is omitted, there are no subsequent pages.", "readOnly": true, "type": "string"}}, "type": "object"}, "FetchDataSourceReferencesForResourceTypeResponse": {"description": "Response for the FetchDataSourceReferencesForResourceType method.", "id": "FetchDataSourceReferencesForResourceTypeResponse", "properties": {"dataSourceReferences": {"description": "The DataSourceReferences from the specified parent.", "items": {"$ref": "DataSourceReference"}, "type": "array"}, "nextPageToken": {"description": "A token, which can be sent as `page_token` to retrieve the next page. If this field is omitted, there are no subsequent pages.", "type": "string"}}, "type": "object"}, "FetchMsComplianceMetadataRequest": {"description": "Request message for GetMsComplianceMetadata", "id": "FetchMsComplianceMetadataRequest", "properties": {"projectId": {"description": "Required. The project id of the target project", "type": "string"}}, "type": "object"}, "FetchMsComplianceMetadataResponse": {"description": "Response message for GetMsComplianceMetadata", "id": "FetchMsComplianceMetadataResponse", "properties": {"isAssuredWorkload": {"description": "The ms compliance metadata of the target project, if the project is an assured workloads project, values will be true, otherwise false.", "type": "boolean"}}, "type": "object"}, "FetchUsableBackupVaultsResponse": {"description": "Response message for fetching usable BackupVaults.", "id": "FetchUsableBackupVaultsResponse", "properties": {"backupVaults": {"description": "The list of BackupVault instances in the project for the specified location. If the '{location}' value in the request is \"-\", the response contains a list of instances from all locations. In case any location is unreachable, the response will only return backup vaults in reachable locations and the 'unreachable' field will be populated with a list of unreachable locations.", "items": {"$ref": "<PERSON><PERSON><PERSON><PERSON>"}, "type": "array"}, "nextPageToken": {"description": "A token identifying a page of results the server should return.", "type": "string"}, "unreachable": {"description": "Locations that could not be reached.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "FinalizeBackupRequest": {"description": "Message for finalizing a Backup.", "id": "FinalizeBackupRequest", "properties": {"backupId": {"description": "Required. Resource ID of the Backup resource to be finalized. This must be the same backup_id that was used in the InitiateBackupRequest.", "type": "string"}, "consistencyTime": {"description": "The point in time when this backup was captured from the source. This will be assigned to the consistency_time field of the newly created Backup.", "format": "google-datetime", "type": "string"}, "description": {"description": "This will be assigned to the description field of the newly created Backup.", "type": "string"}, "recoveryRangeEndTime": {"description": "The latest timestamp of data available in this Backup. This will be set on the newly created Backup.", "format": "google-datetime", "type": "string"}, "recoveryRangeStartTime": {"description": "The earliest timestamp of data available in this Backup. This will set on the newly created Backup.", "format": "google-datetime", "type": "string"}, "requestId": {"description": "Optional. An optional request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server will know to ignore the request if it has already been completed. The server will guarantee that for at least 60 minutes after the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).", "type": "string"}, "retentionDuration": {"description": "The ExpireTime on the backup will be set to FinalizeTime plus this duration. If the resulting ExpireTime is less than EnforcedRetentionEndTime, then ExpireTime is set to EnforcedRetentionEndTime.", "format": "google-duration", "type": "string"}}, "type": "object"}, "GCPBackupPlanInfo": {"description": "GCPBackupPlanInfo captures the plan configuration details of Google Cloud resources at the time of backup.", "id": "GCPBackupPlanInfo", "properties": {"backupPlan": {"description": "Resource name of backup plan by which workload is protected at the time of the backup. Format: projects/{project}/locations/{location}/backupPlans/{backupPlanId}", "type": "string"}, "backupPlanRevisionId": {"description": "The user friendly id of the backup plan revision which triggered this backup in case of scheduled backup or used for on demand backup.", "type": "string"}, "backupPlanRevisionName": {"description": "Resource name of the backup plan revision which triggered this backup in case of scheduled backup or used for on demand backup. Format: projects/{project}/locations/{location}/backupPlans/{backupPlanId}/revisions/{revisionId}", "type": "string"}, "backupPlanRuleId": {"description": "The rule id of the backup plan which triggered this backup in case of scheduled backup or used for", "type": "string"}}, "type": "object"}, "GcpBackupConfig": {"description": "GcpBackupConfig captures the Backup configuration details for Google Cloud resources. All Google Cloud resources regardless of type are protected with backup plan associations.", "id": "GcpBackupConfig", "properties": {"backupPlan": {"description": "The name of the backup plan.", "type": "string"}, "backupPlanAssociation": {"description": "The name of the backup plan association.", "type": "string"}, "backupPlanDescription": {"description": "The description of the backup plan.", "type": "string"}, "backupPlanRevisionId": {"description": "The user friendly id of the backup plan revision. E.g. v0, v1 etc.", "type": "string"}, "backupPlanRevisionName": {"description": "The name of the backup plan revision.", "type": "string"}, "backupPlanRules": {"description": "The names of the backup plan rules which point to this backupvault", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "GcpResource": {"description": "Minimum details to identify a Google Cloud resource", "id": "GcpResource", "properties": {"gcpResourcename": {"description": "Name of the Google Cloud resource.", "type": "string"}, "location": {"description": "Location of the resource: //\"global\"/\"unspecified\".", "type": "string"}, "type": {"description": "Type of the resource. Use the Unified Resource Type, eg. compute.googleapis.com/Instance.", "type": "string"}}, "type": "object"}, "GuestOsFeature": {"description": "Feature type of the Guest OS.", "id": "GuestOsFeature", "properties": {"type": {"description": "The ID of a supported feature.", "enum": ["FEATURE_TYPE_UNSPECIFIED", "VIRTIO_SCSI_MULTIQUEUE", "WINDOWS", "MULTI_IP_SUBNET", "UEFI_COMPATIBLE", "SECURE_BOOT", "GVNIC", "SEV_CAPABLE", "BARE_METAL_LINUX_COMPATIBLE", "SUSPEND_RESUME_COMPATIBLE", "SEV_LIVE_MIGRATABLE", "SEV_SNP_CAPABLE", "TDX_CAPABLE", "IDPF", "SEV_LIVE_MIGRATABLE_V2"], "enumDescriptions": ["Default value, which is unused.", "VIRTIO_SCSI_MULTIQUEUE feature type.", "WINDOWS feature type.", "MULTI_IP_SUBNET feature type.", "UEFI_COMPATIBLE feature type.", "SECURE_BOOT feature type.", "GVNIC feature type.", "SEV_CAPABLE feature type.", "BARE_METAL_LINUX_COMPATIBLE feature type.", "SUSPEND_RESUME_COMPATIBLE feature type.", "SEV_LIVE_MIGRATABLE feature type.", "SEV_SNP_CAPABLE feature type.", "TDX_CAPABLE feature type.", "IDPF feature type.", "SEV_LIVE_MIGRATABLE_V2 feature type."], "type": "string"}}, "type": "object"}, "InitializeParams": {"description": "Specifies the parameters to initialize this disk.", "id": "InitializeParams", "properties": {"diskName": {"description": "Optional. Specifies the disk name. If not specified, the default is to use the name of the instance.", "type": "string"}, "replicaZones": {"description": "Optional. URL of the zone where the disk should be created. Required for each regional disk associated with the instance.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "InitializeServiceRequest": {"description": "Request message for initializing the service.", "id": "InitializeServiceRequest", "properties": {"cloudSqlInstanceInitializationConfig": {"$ref": "CloudSqlInstanceInitializationConfig", "description": "Optional. The configuration for initializing a Cloud SQL instance."}, "requestId": {"description": "Optional. An optional request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server will know to ignore the request if it has already been completed. The server will guarantee that for at least 60 minutes since the first request. For example, consider a situation where you make an initial request and t he request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).", "type": "string"}, "resourceType": {"description": "Required. The resource type to which the default service config will be applied. Examples include, \"compute.googleapis.com/Instance\" and \"storage.googleapis.com/Bucket\".", "type": "string"}}, "type": "object"}, "InitiateBackupRequest": {"description": "request message for InitiateBackup.", "id": "InitiateBackupRequest", "properties": {"backupId": {"description": "Required. Resource ID of the Backup resource.", "type": "string"}, "requestId": {"description": "Optional. An optional request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server will know to ignore the request if it has already been completed. The server will guarantee that for at least 60 minutes since the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).", "type": "string"}}, "type": "object"}, "InitiateBackupResponse": {"description": "Response message for InitiateBackup.", "id": "InitiateBackupResponse", "properties": {"backup": {"description": "The name of the backup that was created.", "type": "string"}, "baseBackupGenerationId": {"description": "The generation id of the base backup. It is needed for the incremental backups.", "format": "int32", "type": "integer"}, "newBackupGenerationId": {"description": "The generation id of the new backup.", "format": "int32", "type": "integer"}}, "type": "object"}, "InstanceParams": {"description": "Additional instance params.", "id": "InstanceParams", "properties": {"resourceManagerTags": {"additionalProperties": {"type": "string"}, "description": "Optional. Resource manager tags to be bound to the instance.", "type": "object"}}, "type": "object"}, "ListBackupPlanAssociationsResponse": {"description": "Response message for List BackupPlanAssociation", "id": "ListBackupPlanAssociationsResponse", "properties": {"backupPlanAssociations": {"description": "The list of Backup Plan Associations in the project for the specified location. If the `{location}` value in the request is \"-\", the response contains a list of instances from all locations. In case any location is unreachable, the response will only return backup plan associations in reachable locations and the 'unreachable' field will be populated with a list of unreachable locations.", "items": {"$ref": "BackupPlanAssociation"}, "type": "array"}, "nextPageToken": {"description": "A token identifying a page of results the server should return.", "type": "string"}, "unreachable": {"description": "Locations that could not be reached.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "ListBackupPlanRevisionsResponse": {"description": "The response message for getting a list of `BackupPlanRevision`.", "id": "ListBackupPlanRevisionsResponse", "properties": {"backupPlanRevisions": {"description": "The list of `BackupPlanRevisions` in the project for the specified location. If the `{location}` value in the request is \"-\", the response contains a list of resources from all locations. In case any location is unreachable, the response will only return backup plans in reachable locations and the 'unreachable' field will be populated with a list of unreachable locations.", "items": {"$ref": "BackupPlanRevision"}, "type": "array"}, "nextPageToken": {"description": "A token which may be sent as page_token in a subsequent `ListBackupPlanRevisions` call to retrieve the next page of results. If this field is omitted or empty, then there are no more results to return.", "type": "string"}, "unreachable": {"description": "Locations that could not be reached.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "ListBackupPlansResponse": {"description": "The response message for getting a list of `BackupPlan`.", "id": "ListBackupPlansResponse", "properties": {"backupPlans": {"description": "The list of `BackupPlans` in the project for the specified location. If the `{location}` value in the request is \"-\", the response contains a list of resources from all locations. In case any location is unreachable, the response will only return backup plans in reachable locations and the 'unreachable' field will be populated with a list of unreachable locations. BackupPlan", "items": {"$ref": "BackupPlan"}, "type": "array"}, "nextPageToken": {"description": "A token which may be sent as page_token in a subsequent `ListBackupPlans` call to retrieve the next page of results. If this field is omitted or empty, then there are no more results to return.", "type": "string"}, "unreachable": {"description": "Locations that could not be reached.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "ListBackupVaultsResponse": {"description": "Response message for listing BackupVaults.", "id": "ListBackupVaultsResponse", "properties": {"backupVaults": {"description": "The list of BackupVault instances in the project for the specified location. If the '{location}' value in the request is \"-\", the response contains a list of instances from all locations. In case any location is unreachable, the response will only return backup vaults in reachable locations and the 'unreachable' field will be populated with a list of unreachable locations.", "items": {"$ref": "<PERSON><PERSON><PERSON><PERSON>"}, "type": "array"}, "nextPageToken": {"description": "A token identifying a page of results the server should return.", "type": "string"}, "unreachable": {"description": "Locations that could not be reached.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "ListBackupsResponse": {"description": "Response message for listing Backups.", "id": "ListBackupsResponse", "properties": {"backups": {"description": "The list of Backup instances in the project for the specified location. If the '{location}' value in the request is \"-\", the response contains a list of instances from all locations. In case any location is unreachable, the response will only return data sources in reachable locations and the 'unreachable' field will be populated with a list of unreachable locations.", "items": {"$ref": "Backup"}, "type": "array"}, "nextPageToken": {"description": "A token identifying a page of results the server should return.", "type": "string"}, "unreachable": {"description": "Locations that could not be reached.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "ListDataSourcesResponse": {"description": "Response message for listing DataSources.", "id": "ListDataSourcesResponse", "properties": {"dataSources": {"description": "The list of DataSource instances in the project for the specified location. If the '{location}' value in the request is \"-\", the response contains a list of instances from all locations. In case any location is unreachable, the response will only return data sources in reachable locations and the 'unreachable' field will be populated with a list of unreachable locations.", "items": {"$ref": "DataSource"}, "type": "array"}, "nextPageToken": {"description": "A token identifying a page of results the server should return.", "type": "string"}, "unreachable": {"description": "Locations that could not be reached.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "ListLocationsResponse": {"description": "The response message for Locations.ListLocations.", "id": "ListLocationsResponse", "properties": {"locations": {"description": "A list of locations that matches the specified filter in the request.", "items": {"$ref": "Location"}, "type": "array"}, "nextPageToken": {"description": "The standard List next-page token.", "type": "string"}}, "type": "object"}, "ListManagementServersResponse": {"description": "Response message for listing management servers.", "id": "ListManagementServersResponse", "properties": {"managementServers": {"description": "The list of ManagementServer instances in the project for the specified location. If the '{location}' value in the request is \"-\", the response contains a list of instances from all locations. In case any location is unreachable, the response will only return management servers in reachable locations and the 'unreachable' field will be populated with a list of unreachable locations.", "items": {"$ref": "ManagementServer"}, "type": "array"}, "nextPageToken": {"description": "A token identifying a page of results the server should return.", "type": "string"}, "unreachable": {"description": "Locations that could not be reached.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "ListOperationsResponse": {"description": "The response message for Operations.ListOperations.", "id": "ListOperationsResponse", "properties": {"nextPageToken": {"description": "The standard List next-page token.", "type": "string"}, "operations": {"description": "A list of operations that matches the specified filter in the request.", "items": {"$ref": "Operation"}, "type": "array"}}, "type": "object"}, "ListResourceBackupConfigsResponse": {"description": "Response for ListResourceBackupConfigs.", "id": "ListResourceBackupConfigsResponse", "properties": {"nextPageToken": {"description": "A token identifying a page of results the server should return.", "type": "string"}, "resourceBackupConfigs": {"description": "The list of ResourceBackupConfigs for the specified scope.", "items": {"$ref": "ResourceBackupConfig"}, "type": "array"}}, "type": "object"}, "Location": {"description": "A resource that represents a Google Cloud location.", "id": "Location", "properties": {"displayName": {"description": "The friendly name for this location, typically a nearby city name. For example, \"Tokyo\".", "type": "string"}, "labels": {"additionalProperties": {"type": "string"}, "description": "Cross-service attributes for the location. For example {\"cloud.googleapis.com/region\": \"us-east1\"}", "type": "object"}, "locationId": {"description": "The canonical id for this location. For example: `\"us-east1\"`.", "type": "string"}, "metadata": {"additionalProperties": {"description": "Properties of the object. Contains field @type with type URL.", "type": "any"}, "description": "Service-specific metadata. For example the available capacity at the given location.", "type": "object"}, "name": {"description": "Resource name for the location, which may vary between implementations. For example: `\"projects/example-project/locations/us-east1\"`", "type": "string"}}, "type": "object"}, "ManagementServer": {"description": "ManagementServer describes a single BackupDR ManagementServer instance.", "id": "ManagementServer", "properties": {"baProxyUri": {"description": "Output only. The hostname or ip address of the exposed AGM endpoints, used by BAs to connect to BA proxy.", "items": {"type": "string"}, "readOnly": true, "type": "array"}, "createTime": {"description": "Output only. The time when the instance was created.", "format": "google-datetime", "readOnly": true, "type": "string"}, "description": {"description": "Optional. The description of the ManagementServer instance (2048 characters or less).", "type": "string"}, "etag": {"description": "Optional. Server specified ETag for the ManagementServer resource to prevent simultaneous updates from overwiting each other.", "type": "string"}, "labels": {"additionalProperties": {"type": "string"}, "description": "Optional. Resource labels to represent user provided metadata. Labels currently defined: 1. migrate_from_go= If set to true, the MS is created in migration ready mode.", "type": "object"}, "managementUri": {"$ref": "ManagementURI", "description": "Output only. The hostname or ip address of the exposed AGM endpoints, used by clients to connect to AGM/RD graphical user interface and APIs.", "readOnly": true}, "name": {"description": "Output only. Identifier. The resource name.", "readOnly": true, "type": "string"}, "networks": {"description": "Optional. VPC networks to which the ManagementServer instance is connected. For this version, only a single network is supported. This field is optional if MS is created without PSA", "items": {"$ref": "NetworkConfig"}, "type": "array"}, "oauth2ClientId": {"description": "Output only. The OAuth 2.0 client id is required to make API calls to the BackupDR instance API of this ManagementServer. This is the value that should be provided in the 'aud' field of the OIDC ID Token (see openid specification https://openid.net/specs/openid-connect-core-1_0.html#IDToken).", "readOnly": true, "type": "string"}, "satisfiesPzi": {"description": "Output only. Reserved for future use.", "readOnly": true, "type": "boolean"}, "satisfiesPzs": {"description": "Output only. Reserved for future use.", "readOnly": true, "type": "boolean"}, "state": {"description": "Output only. The ManagementServer state.", "enum": ["INSTANCE_STATE_UNSPECIFIED", "CREATING", "READY", "UPDATING", "DELETING", "REPAIRING", "MAINTENANCE", "ERROR"], "enumDescriptions": ["State not set.", "The instance is being created.", "The instance has been created and is fully usable.", "The instance configuration is being updated. Certain kinds of updates may cause the instance to become unusable while the update is in progress.", "The instance is being deleted.", "The instance is being repaired and may be unstable.", "Maintenance is being performed on this instance.", "The instance is experiencing an issue and might be unusable. You can get further details from the statusMessage field of Instance resource."], "readOnly": true, "type": "string"}, "type": {"description": "Optional. The type of the ManagementServer resource.", "enum": ["INSTANCE_TYPE_UNSPECIFIED", "BACKUP_RESTORE"], "enumDescriptions": ["Instance type is not mentioned.", "Instance for backup and restore management (i.e., AGM)."], "type": "string"}, "updateTime": {"description": "Output only. The time when the instance was updated.", "format": "google-datetime", "readOnly": true, "type": "string"}, "workforceIdentityBasedManagementUri": {"$ref": "WorkforceIdentityBasedManagementURI", "description": "Output only. The hostnames of the exposed AGM endpoints for both types of user i.e. 1p and 3p, used to connect AGM/RM UI.", "readOnly": true}, "workforceIdentityBasedOauth2ClientId": {"$ref": "WorkforceIdentityBasedOAuth2ClientID", "description": "Output only. The OAuth client IDs for both types of user i.e. 1p and 3p.", "readOnly": true}}, "type": "object"}, "ManagementURI": {"description": "ManagementURI for the Management Server resource.", "id": "ManagementURI", "properties": {"api": {"description": "Output only. The ManagementServer AGM/RD API URL.", "readOnly": true, "type": "string"}, "webUi": {"description": "Output only. The ManagementServer AGM/RD WebUI URL.", "readOnly": true, "type": "string"}}, "type": "object"}, "Metadata": {"description": "A metadata key/value entry.", "id": "<PERSON><PERSON><PERSON>", "properties": {"items": {"description": "Optional. Array of key/value pairs. The total size of all keys and values must be less than 512 KB.", "items": {"$ref": "Entry"}, "type": "array"}}, "type": "object"}, "NetworkConfig": {"description": "Network configuration for ManagementServer instance.", "id": "NetworkConfig", "properties": {"network": {"description": "Optional. The resource name of the Google Compute Engine VPC network to which the ManagementServer instance is connected.", "type": "string"}, "peeringMode": {"description": "Optional. The network connect mode of the ManagementServer instance. For this version, only PRIVATE_SERVICE_ACCESS is supported.", "enum": ["PEERING_MODE_UNSPECIFIED", "PRIVATE_SERVICE_ACCESS"], "enumDescriptions": ["Peering mode not set.", "Connect using Private Service Access to the Management Server. Private services access provides an IP address range for multiple Google Cloud services, including Cloud BackupDR."], "type": "string"}}, "type": "object"}, "NetworkInterface": {"description": "A network interface resource attached to an instance. s", "id": "NetworkInterface", "properties": {"accessConfigs": {"description": "Optional. An array of configurations for this interface. Currently, only one access config,ONE_TO_ONE_NAT is supported. If there are no accessConfigs specified, then this instance will have no external internet access.", "items": {"$ref": "AccessConfig"}, "type": "array"}, "aliasIpRanges": {"description": "Optional. An array of alias IP ranges for this network interface. You can only specify this field for network interfaces in VPC networks.", "items": {"$ref": "AliasIpRange"}, "type": "array"}, "internalIpv6PrefixLength": {"description": "Optional. The prefix length of the primary internal IPv6 range.", "format": "int32", "type": "integer"}, "ipv6AccessConfigs": {"description": "Optional. An array of IPv6 access configurations for this interface. Currently, only one IPv6 access config, DIRECT_IPV6, is supported. If there is no ipv6AccessConfig specified, then this instance will have no external IPv6 Internet access.", "items": {"$ref": "AccessConfig"}, "type": "array"}, "ipv6AccessType": {"description": "Optional. [Output Only] One of EXTERNAL, INTERNAL to indicate whether the IP can be accessed from the Internet. This field is always inherited from its subnetwork.", "enum": ["UNSPECIFIED_IPV6_ACCESS_TYPE", "INTERNAL", "EXTERNAL"], "enumDescriptions": ["IPv6 access type not set. Means this network interface hasn't been turned on IPv6 yet.", "This network interface can have internal IPv6.", "This network interface can have external IPv6."], "type": "string"}, "ipv6Address": {"description": "Optional. An IPv6 internal network address for this network interface. To use a static internal IP address, it must be unused and in the same region as the instance's zone. If not specified, Google Cloud will automatically assign an internal IPv6 address from the instance's subnetwork.", "type": "string"}, "name": {"description": "Output only. [Output Only] The name of the network interface, which is generated by the server.", "readOnly": true, "type": "string"}, "network": {"description": "Optional. URL of the VPC network resource for this instance.", "type": "string"}, "networkAttachment": {"description": "Optional. The URL of the network attachment that this interface should connect to in the following format: projects/{project_number}/regions/{region_name}/networkAttachments/{network_attachment_name}.", "type": "string"}, "networkIP": {"description": "Optional. An IPv4 internal IP address to assign to the instance for this network interface. If not specified by the user, an unused internal IP is assigned by the system.", "type": "string"}, "nicType": {"description": "Optional. The type of vNIC to be used on this interface. This may be gVNIC or VirtioNet.", "enum": ["NIC_TYPE_UNSPECIFIED", "VIRTIO_NET", "GVNIC"], "enumDescriptions": ["Default should be NIC_TYPE_UNSPECIFIED.", "VIRTIO", "GVNIC"], "type": "string"}, "queueCount": {"description": "Optional. The networking queue count that's specified by users for the network interface. Both Rx and Tx queues will be set to this number. It'll be empty if not specified by the users.", "format": "int32", "type": "integer"}, "stackType": {"description": "The stack type for this network interface.", "enum": ["STACK_TYPE_UNSPECIFIED", "IPV4_ONLY", "IPV4_IPV6"], "enumDescriptions": ["Default should be STACK_TYPE_UNSPECIFIED.", "The network interface will be assigned IPv4 address.", "The network interface can have both IPv4 and IPv6 addresses."], "type": "string"}, "subnetwork": {"description": "Optional. The URL of the Subnetwork resource for this instance.", "type": "string"}}, "type": "object"}, "NetworkPerformanceConfig": {"description": "Network performance configuration.", "id": "NetworkPerformanceConfig", "properties": {"totalEgressBandwidthTier": {"description": "Optional. The tier of the total egress bandwidth.", "enum": ["TIER_UNSPECIFIED", "DEFAULT", "TIER_1"], "enumDescriptions": ["This value is unused.", "Default network performance config.", "Tier 1 network performance config."], "type": "string"}}, "type": "object"}, "NodeAffinity": {"description": "Node Affinity: the configuration of desired nodes onto which this Instance could be scheduled.", "id": "NodeAffinity", "properties": {"key": {"description": "Optional. Corresponds to the label key of Node resource.", "type": "string"}, "operator": {"description": "Optional. Defines the operation of node selection.", "enum": ["OPERATOR_UNSPECIFIED", "IN", "NOT_IN"], "enumDescriptions": ["Default value. This value is unused.", "Requires Compute Engine to seek for matched nodes.", "Requires Compute Engine to avoid certain nodes."], "type": "string"}, "values": {"description": "Optional. Corresponds to the label values of Node resource.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "Operation": {"description": "This resource represents a long-running operation that is the result of a network API call.", "id": "Operation", "properties": {"done": {"description": "If the value is `false`, it means the operation is still in progress. If `true`, the operation is completed, and either `error` or `response` is available.", "type": "boolean"}, "error": {"$ref": "Status", "description": "The error result of the operation in case of failure or cancellation."}, "metadata": {"additionalProperties": {"description": "Properties of the object. Contains field @type with type URL.", "type": "any"}, "description": "Service-specific metadata associated with the operation. It typically contains progress information and common metadata such as create time. Some services might not provide such metadata. Any method that returns a long-running operation should document the metadata type, if any.", "type": "object"}, "name": {"description": "The server-assigned name, which is only unique within the same service that originally returns it. If you use the default HTTP mapping, the `name` should be a resource name ending with `operations/{unique_id}`.", "type": "string"}, "response": {"additionalProperties": {"description": "Properties of the object. Contains field @type with type URL.", "type": "any"}, "description": "The normal, successful response of the operation. If the original method returns no data on success, such as `Delete`, the response is `google.protobuf.Empty`. If the original method is standard `Get`/`Create`/`Update`, the response should be the resource. For other methods, the response should have the type `XxxResponse`, where `Xxx` is the original method name. For example, if the original method name is `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.", "type": "object"}}, "type": "object"}, "OperationMetadata": {"description": "Represents the metadata of the long-running operation.", "id": "OperationMetadata", "properties": {"additionalInfo": {"additionalProperties": {"type": "string"}, "description": "Output only. AdditionalInfo contains additional Info related to backup plan association resource.", "readOnly": true, "type": "object"}, "apiVersion": {"description": "Output only. API version used to start the operation.", "readOnly": true, "type": "string"}, "createTime": {"description": "Output only. The time the operation was created.", "format": "google-datetime", "readOnly": true, "type": "string"}, "endTime": {"description": "Output only. The time the operation finished running.", "format": "google-datetime", "readOnly": true, "type": "string"}, "requestedCancellation": {"description": "Output only. Identifies whether the user has requested cancellation of the operation. Operations that have successfully been cancelled have google.longrunning.Operation.error value with a google.rpc.Status.code of 1, corresponding to 'Code.CANCELLED'.", "readOnly": true, "type": "boolean"}, "statusMessage": {"description": "Output only. Human-readable status of the operation, if any.", "readOnly": true, "type": "string"}, "target": {"description": "Output only. Server-defined resource path for the target of the operation.", "readOnly": true, "type": "string"}, "verb": {"description": "Output only. Name of the verb executed by the operation.", "readOnly": true, "type": "string"}}, "type": "object"}, "PitrSettings": {"description": "Point in time recovery settings of the backup configuration resource.", "id": "PitrSettings", "properties": {"retentionDays": {"description": "Output only. Number of days to retain the backup.", "format": "int32", "readOnly": true, "type": "integer"}}, "type": "object"}, "Policy": {"description": "An Identity and Access Management (IAM) policy, which specifies access controls for Google Cloud resources. A `Policy` is a collection of `bindings`. A `binding` binds one or more `members`, or principals, to a single `role`. Principals can be user accounts, service accounts, Google groups, and domains (such as G Suite). A `role` is a named list of permissions; each `role` can be an IAM predefined role or a user-created custom role. For some types of Google Cloud resources, a `binding` can also specify a `condition`, which is a logical expression that allows access to a resource only if the expression evaluates to `true`. A condition can add constraints based on attributes of the request, the resource, or both. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies). **JSON example:** ``` { \"bindings\": [ { \"role\": \"roles/resourcemanager.organizationAdmin\", \"members\": [ \"user:<EMAIL>\", \"group:<EMAIL>\", \"domain:google.com\", \"serviceAccount:<EMAIL>\" ] }, { \"role\": \"roles/resourcemanager.organizationViewer\", \"members\": [ \"user:<EMAIL>\" ], \"condition\": { \"title\": \"expirable access\", \"description\": \"Does not grant access after Sep 2020\", \"expression\": \"request.time < timestamp('2020-10-01T00:00:00.000Z')\", } } ], \"etag\": \"BwWWja0YfJA=\", \"version\": 3 } ``` **YAML example:** ``` bindings: - members: - user:<EMAIL> - group:<EMAIL> - domain:google.com - serviceAccount:<EMAIL> role: roles/resourcemanager.organizationAdmin - members: - user:<EMAIL> role: roles/resourcemanager.organizationViewer condition: title: expirable access description: Does not grant access after Sep 2020 expression: request.time < timestamp('2020-10-01T00:00:00.000Z') etag: BwWWja0YfJA= version: 3 ``` For a description of IAM and its features, see the [IAM documentation](https://cloud.google.com/iam/docs/).", "id": "Policy", "properties": {"auditConfigs": {"description": "Specifies cloud audit logging configuration for this policy.", "items": {"$ref": "AuditConfig"}, "type": "array"}, "bindings": {"description": "Associates a list of `members`, or principals, with a `role`. Optionally, may specify a `condition` that determines how and when the `bindings` are applied. Each of the `bindings` must contain at least one principal. The `bindings` in a `Policy` can refer to up to 1,500 principals; up to 250 of these principals can be Google groups. Each occurrence of a principal counts towards these limits. For example, if the `bindings` grant 50 different roles to `user:<EMAIL>`, and not to any other principal, then you can add another 1,450 principals to the `bindings` in the `Policy`.", "items": {"$ref": "Binding"}, "type": "array"}, "etag": {"description": "`etag` is used for optimistic concurrency control as a way to help prevent simultaneous updates of a policy from overwriting each other. It is strongly suggested that systems make use of the `etag` in the read-modify-write cycle to perform policy updates in order to avoid race conditions: An `etag` is returned in the response to `getIamPolicy`, and systems are expected to put that etag in the request to `setIamPolicy` to ensure that their change will be applied to the same version of the policy. **Important:** If you use IAM Conditions, you must include the `etag` field whenever you call `setIamPolicy`. If you omit this field, then IAM allows you to overwrite a version `3` policy with a version `1` policy, and all of the conditions in the version `3` policy are lost.", "format": "byte", "type": "string"}, "version": {"description": "Specifies the format of the policy. Valid values are `0`, `1`, and `3`. Requests that specify an invalid value are rejected. Any operation that affects conditional role bindings must specify version `3`. This requirement applies to the following operations: * Getting a policy that includes a conditional role binding * Adding a conditional role binding to a policy * Changing a conditional role binding in a policy * Removing any role binding, with or without a condition, from a policy that includes conditions **Important:** If you use IAM Conditions, you must include the `etag` field whenever you call `setIamPolicy`. If you omit this field, then IAM allows you to overwrite a version `3` policy with a version `1` policy, and all of the conditions in the version `3` policy are lost. If a policy does not include any conditions, operations on that policy may specify any valid version or leave the field unset. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies).", "format": "int32", "type": "integer"}}, "type": "object"}, "RegionDiskTargetEnvironment": {"description": "RegionDiskTargetEnvironment represents the target environment for the disk.", "id": "RegionDiskTargetEnvironment", "properties": {"project": {"description": "Required. Target project for the disk.", "type": "string"}, "region": {"description": "Required. Target region for the disk.", "type": "string"}, "replicaZones": {"description": "Required. Target URLs of the replica zones for the disk.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "RemoveDataSourceRequest": {"description": "Message for deleting a DataSource.", "id": "RemoveDataSourceRequest", "properties": {"requestId": {"description": "Optional. An optional request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server will know to ignore the request if it has already been completed. The server will guarantee that for at least 60 minutes after the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).", "type": "string"}}, "type": "object"}, "ResourceBackupConfig": {"description": "ResourceBackupConfig represents a resource along with its backup configurations.", "id": "ResourceBackupConfig", "properties": {"backupConfigsDetails": {"description": "Backup configurations applying to the target resource, including those targeting its related/child resources. For example, backup configuration applicable to Compute Engine disks will be populated in this field for a Compute Engine VM which has the disk associated.", "items": {"$ref": "BackupConfigDetails"}, "type": "array"}, "backupConfigured": {"description": "Output only. Whether the target resource is configured for backup. This is true if the backup_configs_details is not empty.", "readOnly": true, "type": "boolean"}, "name": {"description": "Identifier. The resource name of the ResourceBackupConfig. Format: projects/{project}/locations/{location}/resourceBackupConfigs/{uid}", "type": "string"}, "targetResource": {"description": "Output only. The [full resource name](https://cloud.google.com/asset-inventory/docs/resource-name-format) of the cloud resource that this configuration applies to. Supported resource types are ResourceBackupConfig.ResourceType.", "readOnly": true, "type": "string"}, "targetResourceDisplayName": {"description": "Output only. The human friendly name of the target resource.", "readOnly": true, "type": "string"}, "targetResourceLabels": {"additionalProperties": {"type": "string"}, "description": "Labels associated with the target resource.", "type": "object"}, "targetResourceType": {"description": "Output only. The type of the target resource.", "enum": ["RESOURCE_TYPE_UNSPECIFIED", "CLOUD_SQL_INSTANCE", "COMPUTE_ENGINE_VM"], "enumDescriptions": ["Resource type not set.", "Cloud SQL instance.", "Compute Engine VM."], "readOnly": true, "type": "string"}, "uid": {"description": "Output only. The unique identifier of the resource backup config.", "readOnly": true, "type": "string"}, "vaulted": {"description": "Output only. Whether the target resource is protected by a backup vault. This is true if the backup_configs_details is not empty and any of the ResourceBackupConfig.backup_configs_details has a backup configuration with BackupConfigDetails.backup_vault set. set.", "readOnly": true, "type": "boolean"}}, "type": "object"}, "RestoreBackupRequest": {"description": "Request message for restoring from a Backup.", "id": "RestoreBackupRequest", "properties": {"computeInstanceRestoreProperties": {"$ref": "ComputeInstanceRestoreProperties", "description": "Compute Engine instance properties to be overridden during restore."}, "computeInstanceTargetEnvironment": {"$ref": "ComputeInstanceTargetEnvironment", "description": "Compute Engine target environment to be used during restore."}, "diskRestoreProperties": {"$ref": "DiskRestoreProperties", "description": "Disk properties to be overridden during restore."}, "diskTargetEnvironment": {"$ref": "DiskTargetEnvironment", "description": "Disk target environment to be used during restore."}, "regionDiskTargetEnvironment": {"$ref": "RegionDiskTargetEnvironment", "description": "Region disk target environment to be used during restore."}, "requestId": {"description": "Optional. An optional request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server will know to ignore the request if it has already been completed. The server will guarantee that for at least 60 minutes after the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).", "type": "string"}}, "type": "object"}, "RestoreBackupResponse": {"description": "Response message for restoring from a Backup.", "id": "RestoreBackupResponse", "properties": {"targetResource": {"$ref": "TargetResource", "description": "Details of the target resource created/modified as part of restore."}}, "type": "object"}, "RuleConfigInfo": {"description": "Message for rules config info.", "id": "RuleConfigInfo", "properties": {"lastBackupError": {"$ref": "Status", "description": "Output only. google.rpc.Status object to store the last backup error.", "readOnly": true}, "lastBackupState": {"description": "Output only. The last backup state for rule.", "enum": ["LAST_BACKUP_STATE_UNSPECIFIED", "FIRST_BACKUP_PENDING", "PERMISSION_DENIED", "SUCCEEDED", "FAILED"], "enumDescriptions": ["State not set.", "The first backup is pending.", "The most recent backup could not be run/failed because of the lack of permissions.", "The last backup operation succeeded.", "The last backup operation failed."], "readOnly": true, "type": "string"}, "lastSuccessfulBackupConsistencyTime": {"description": "Output only. The point in time when the last successful backup was captured from the source.", "format": "google-datetime", "readOnly": true, "type": "string"}, "ruleId": {"description": "Output only. Backup Rule id fetched from backup plan.", "readOnly": true, "type": "string"}}, "type": "object"}, "Scheduling": {"description": "Sets the scheduling options for an Instance.", "id": "Scheduling", "properties": {"automaticRestart": {"description": "Optional. Specifies whether the instance should be automatically restarted if it is terminated by Compute Engine (not terminated by a user).", "type": "boolean"}, "instanceTerminationAction": {"description": "Optional. Specifies the termination action for the instance.", "enum": ["INSTANCE_TERMINATION_ACTION_UNSPECIFIED", "DELETE", "STOP"], "enumDescriptions": ["Default value. This value is unused.", "Delete the VM.", "Stop the VM without storing in-memory content. default action."], "type": "string"}, "localSsdRecoveryTimeout": {"$ref": "SchedulingDuration", "description": "Optional. Specifies the maximum amount of time a Local Ssd Vm should wait while recovery of the Local Ssd state is attempted. Its value should be in between 0 and 168 hours with hour granularity and the default value being 1 hour."}, "minNodeCpus": {"description": "Optional. The minimum number of virtual CPUs this instance will consume when running on a sole-tenant node.", "format": "int32", "type": "integer"}, "nodeAffinities": {"description": "Optional. A set of node affinity and anti-affinity configurations. Overrides reservationAffinity.", "items": {"$ref": "NodeAffinity"}, "type": "array"}, "onHostMaintenance": {"description": "Optional. Defines the maintenance behavior for this instance.", "enum": ["ON_HOST_MAINTENANCE_UNSPECIFIED", "TERMINATE", "MIGRATE"], "enumDescriptions": ["Default value. This value is unused.", "Tells Compute Engine to terminate and (optionally) restart the instance away from the maintenance activity.", "Default, Allows Compute Engine to automatically migrate instances out of the way of maintenance events."], "type": "string"}, "preemptible": {"description": "Optional. Defines whether the instance is preemptible.", "type": "boolean"}, "provisioningModel": {"description": "Optional. Specifies the provisioning model of the instance.", "enum": ["PROVISIONING_MODEL_UNSPECIFIED", "STANDARD", "SPOT"], "enumDescriptions": ["Default value. This value is not used.", "Standard provisioning with user controlled runtime, no discounts.", "Heavily discounted, no guaranteed runtime."], "type": "string"}}, "type": "object"}, "SchedulingDuration": {"description": "A SchedulingDuration represents a fixed-length span of time represented as a count of seconds and fractions of seconds at nanosecond resolution. It is independent of any calendar and concepts like \"day\" or \"month\". Range is approximately 10,000 years.", "id": "SchedulingDuration", "properties": {"nanos": {"description": "Optional. Span of time that's a fraction of a second at nanosecond resolution.", "format": "int32", "type": "integer"}, "seconds": {"description": "Optional. Span of time at a resolution of a second.", "format": "int64", "type": "string"}}, "type": "object"}, "ServiceAccount": {"description": "A service account.", "id": "ServiceAccount", "properties": {"email": {"description": "Optional. Email address of the service account.", "type": "string"}, "scopes": {"description": "Optional. The list of scopes to be made available for this service account.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "ServiceLockInfo": {"description": "ServiceLockInfo represents the details of a lock taken by the service on a Backup resource.", "id": "ServiceLockInfo", "properties": {"operation": {"description": "Output only. The name of the operation that created this lock. The lock will automatically be released when the operation completes.", "readOnly": true, "type": "string"}}, "type": "object"}, "SetIamPolicyRequest": {"description": "Request message for `SetIamPolicy` method.", "id": "SetIamPolicyRequest", "properties": {"policy": {"$ref": "Policy", "description": "REQUIRED: The complete policy to be applied to the `resource`. The size of the policy is limited to a few 10s of KB. An empty policy is a valid policy but certain Google Cloud services (such as Projects) might reject them."}, "updateMask": {"description": "OPTIONAL: A FieldMask specifying which fields of the policy to modify. Only the fields in the mask will be modified. If no mask is provided, the following default mask is used: `paths: \"bindings, etag\"`", "format": "google-fieldmask", "type": "string"}}, "type": "object"}, "SetInternalStatusRequest": {"description": "Request message for SetStatusInternal method.", "id": "SetInternalStatusRequest", "properties": {"backupConfigState": {"description": "Required. Output only. The new BackupConfigState to set for the DataSource.", "enum": ["BACKUP_CONFIG_STATE_UNSPECIFIED", "ACTIVE", "PASSIVE"], "enumDescriptions": ["The possible states of backup configuration. Status not set.", "The data source is actively protected (i.e. there is a BackupPlanAssociation or Appliance SLA pointing to it)", "The data source is no longer protected (but may have backups under it)"], "readOnly": true, "type": "string"}, "requestId": {"description": "Optional. An optional request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server will know to ignore the request if it has already been completed. The server will guarantee that for at least 60 minutes after the first request. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).", "type": "string"}, "value": {"description": "Required. The value required for this method to work. This field must be the 32-byte SHA256 hash of the DataSourceID. The DataSourceID used here is only the final piece of the fully qualified resource path for this DataSource (i.e. the part after '.../dataSources/'). This field exists to make this method difficult to call since it is intended for use only by Backup Appliances.", "format": "byte", "type": "string"}}, "type": "object"}, "SetInternalStatusResponse": {"description": "Response message from SetStatusInternal method.", "id": "SetInternalStatusResponse", "properties": {}, "type": "object"}, "StandardSchedule": {"description": "`StandardSchedule` defines a schedule that run within the confines of a defined window of days. We can define recurrence type for schedule as HOURLY, DAILY, WEEKLY, MONTHLY or YEARLY.", "id": "StandardSchedule", "properties": {"backupWindow": {"$ref": "BackupWindow", "description": "Required. A BackupWindow defines the window of day during which backup jobs will run. Jobs are queued at the beginning of the window and will be marked as `NOT_RUN` if they do not start by the end of the window. Note: running jobs will not be cancelled at the end of the window."}, "daysOfMonth": {"description": "Optional. Specifies days of months like 1, 5, or 14 on which jobs will run. Values for `days_of_month` are only applicable for `recurrence_type`, `MONTHLY` and `YEARLY`. A validation error will occur if other values are supplied.", "items": {"format": "int32", "type": "integer"}, "type": "array"}, "daysOfWeek": {"description": "Optional. Specifies days of week like, MONDAY or TUESDAY, on which jobs will run. This is required for `recurrence_type`, `WEEKLY` and is not applicable otherwise. A validation error will occur if a value is supplied and `recurrence_type` is not `WEEKLY`.", "items": {"enum": ["DAY_OF_WEEK_UNSPECIFIED", "MONDAY", "TUESDAY", "WEDNESDAY", "THURSDAY", "FRIDAY", "SATURDAY", "SUNDAY"], "enumDescriptions": ["The day of the week is unspecified.", "Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday", "Sunday"], "type": "string"}, "type": "array"}, "hourlyFrequency": {"description": "Optional. Specifies frequency for hourly backups. A hourly frequency of 2 means jobs will run every 2 hours from start time till end time defined. This is required for `recurrence_type`, `HOURLY` and is not applicable otherwise. A validation error will occur if a value is supplied and `recurrence_type` is not `HOURLY`. Value of hourly frequency should be between 4 and 23. Reason for limit : We found that there is bandwidth limitation of 3GB/S for GMI while taking a backup and 5GB/S while doing a restore. Given the amount of parallel backups and restore we are targeting, this will potentially take the backup time to mins and hours (in worst case scenario).", "format": "int32", "type": "integer"}, "months": {"description": "Optional. Specifies the months of year, like `FEBRUARY` and/or `MAY`, on which jobs will run. This field is only applicable when `recurrence_type` is `YEARLY`. A validation error will occur if other values are supplied.", "items": {"enum": ["MONTH_UNSPECIFIED", "JANUARY", "FEBRUARY", "MARCH", "APRIL", "MAY", "JUNE", "JULY", "AUGUST", "SEPTEMBER", "OCTOBER", "NOVEMBER", "DECEMBER"], "enumDescriptions": ["The unspecified month.", "The month of January.", "The month of February.", "The month of March.", "The month of April.", "The month of May.", "The month of June.", "The month of July.", "The month of August.", "The month of September.", "The month of October.", "The month of November.", "The month of December."], "type": "string"}, "type": "array"}, "recurrenceType": {"description": "Required. Specifies the `RecurrenceType` for the schedule.", "enum": ["RECURRENCE_TYPE_UNSPECIFIED", "HOURLY", "DAILY", "WEEKLY", "MONTHLY", "YEARLY"], "enumDescriptions": ["recurrence type not set", "The `BackupRule` is to be applied hourly.", "The `BackupRule` is to be applied daily.", "The `BackupRule` is to be applied weekly.", "The `BackupRule` is to be applied monthly.", "The `BackupRule` is to be applied yearly."], "type": "string"}, "timeZone": {"description": "Required. The time zone to be used when interpreting the schedule. The value of this field must be a time zone name from the IANA tz database. See https://en.wikipedia.org/wiki/List_of_tz_database_time_zones for the list of valid timezone names. For e.g., Europe/Paris.", "type": "string"}, "weekDayOfMonth": {"$ref": "WeekDayOfMonth", "description": "Optional. Specifies a week day of the month like, FIRST SUNDAY or LAST MONDAY, on which jobs will run. This will be specified by two fields in `WeekDayOfMonth`, one for the day, e.g. `MONDAY`, and one for the week, e.g. `LAST`. This field is only applicable for `recurrence_type`, `MONTHLY` and `YEARLY`. A validation error will occur if other values are supplied."}}, "type": "object"}, "Status": {"description": "The `Status` type defines a logical error model that is suitable for different programming environments, including REST APIs and RPC APIs. It is used by [gRPC](https://github.com/grpc). Each `Status` message contains three pieces of data: error code, error message, and error details. You can find out more about this error model and how to work with it in the [API Design Guide](https://cloud.google.com/apis/design/errors).", "id": "Status", "properties": {"code": {"description": "The status code, which should be an enum value of google.rpc.Code.", "format": "int32", "type": "integer"}, "details": {"description": "A list of messages that carry the error details. There is a common set of message types for APIs to use.", "items": {"additionalProperties": {"description": "Properties of the object. Contains field @type with type URL.", "type": "any"}, "type": "object"}, "type": "array"}, "message": {"description": "A developer-facing error message, which should be in English. Any user-facing error message should be localized and sent in the google.rpc.Status.details field, or localized by the client.", "type": "string"}}, "type": "object"}, "Tags": {"description": "A set of instance tags.", "id": "Tags", "properties": {"items": {"description": "Optional. An array of tags. Each tag must be 1-63 characters long, and comply with RFC1035.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "TargetResource": {"description": "Details of the target resource created/modified as part of restore.", "id": "TargetResource", "properties": {"gcpResource": {"$ref": "GcpResource", "description": "Details of the native Google Cloud resource created as part of restore."}}, "type": "object"}, "TestIamPermissionsRequest": {"description": "Request message for `TestIamPermissions` method.", "id": "TestIamPermissionsRequest", "properties": {"permissions": {"description": "The set of permissions to check for the `resource`. Permissions with wildcards (such as `*` or `storage.*`) are not allowed. For more information see [IAM Overview](https://cloud.google.com/iam/docs/overview#permissions).", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "TestIamPermissionsResponse": {"description": "Response message for `TestIamPermissions` method.", "id": "TestIamPermissionsResponse", "properties": {"permissions": {"description": "A subset of `TestPermissionsRequest.permissions` that the caller is allowed.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "TriggerBackupRequest": {"description": "Request message for triggering a backup.", "id": "TriggerBackupRequest", "properties": {"requestId": {"description": "Optional. An optional request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server will know to ignore the request if it has already been completed. The server will guarantee that for at least 60 minutes after the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).", "type": "string"}, "ruleId": {"description": "Optional. backup rule_id for which a backup needs to be triggered. If not specified, on-demand backup with custom retention will be triggered.", "type": "string"}}, "type": "object"}, "WeekDayOfMonth": {"description": "`WeekDayOfMonth` defines the week day of the month on which the backups will run. The message combines a `WeekOfMonth` and `DayOfWeek` to produce values like `FIRST`/`MONDAY` or `LAST`/`FRIDAY`.", "id": "WeekDayOfMonth", "properties": {"dayOfWeek": {"description": "Required. Specifies the day of the week.", "enum": ["DAY_OF_WEEK_UNSPECIFIED", "MONDAY", "TUESDAY", "WEDNESDAY", "THURSDAY", "FRIDAY", "SATURDAY", "SUNDAY"], "enumDescriptions": ["The day of the week is unspecified.", "Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday", "Sunday"], "type": "string"}, "weekOfMonth": {"description": "Required. Specifies the week of the month.", "enum": ["WEEK_OF_MONTH_UNSPECIFIED", "FIRST", "SECOND", "THIRD", "FOURTH", "LAST"], "enumDescriptions": ["The zero value. Do not use.", "The first week of the month.", "The second week of the month.", "The third week of the month.", "The fourth week of the month.", "The last week of the month."], "type": "string"}}, "type": "object"}, "WorkforceIdentityBasedManagementURI": {"description": "ManagementURI depending on the Workforce Identity i.e. either 1p or 3p.", "id": "WorkforceIdentityBasedManagementURI", "properties": {"firstPartyManagementUri": {"description": "Output only. First party Management URI for Google Identities.", "readOnly": true, "type": "string"}, "thirdPartyManagementUri": {"description": "Output only. Third party Management URI for External Identity Providers.", "readOnly": true, "type": "string"}}, "type": "object"}, "WorkforceIdentityBasedOAuth2ClientID": {"description": "OAuth Client ID depending on the Workforce Identity i.e. either 1p or 3p,", "id": "WorkforceIdentityBasedOAuth2ClientID", "properties": {"firstPartyOauth2ClientId": {"description": "Output only. First party OAuth Client ID for Google Identities.", "readOnly": true, "type": "string"}, "thirdPartyOauth2ClientId": {"description": "Output only. Third party OAuth Client ID for External Identity Providers.", "readOnly": true, "type": "string"}}, "type": "object"}}, "servicePath": "", "title": "Backup and DR Service API", "version": "v1", "version_module": true}