{"auth": {"oauth2": {"scopes": {"https://www.googleapis.com/auth/androidmanagement": {"description": "Manage Android devices and apps for your customers"}}}}, "basePath": "", "baseUrl": "https://androidmanagement.googleapis.com/", "batchPath": "batch", "canonicalName": "Android Management", "description": "The Android Management API provides remote enterprise management of Android devices and apps.", "discoveryVersion": "v1", "documentationLink": "https://developers.google.com/android/management", "fullyEncodeReservedExpansion": true, "icons": {"x16": "http://www.google.com/images/icons/product/search-16.gif", "x32": "http://www.google.com/images/icons/product/search-32.gif"}, "id": "androidmanagement:v1", "kind": "discovery#restDescription", "mtlsRootUrl": "https://androidmanagement.mtls.googleapis.com/", "name": "androidmanagement", "ownerDomain": "google.com", "ownerName": "Google", "parameters": {"$.xgafv": {"description": "V1 error format.", "enum": ["1", "2"], "enumDescriptions": ["v1 error format", "v2 error format"], "location": "query", "type": "string"}, "access_token": {"description": "OAuth access token.", "location": "query", "type": "string"}, "alt": {"default": "json", "description": "Data format for response.", "enum": ["json", "media", "proto"], "enumDescriptions": ["Responses with Content-Type of application/json", "Media download with context-dependent Content-Type", "Responses with Content-Type of application/x-protobuf"], "location": "query", "type": "string"}, "callback": {"description": "JSONP", "location": "query", "type": "string"}, "fields": {"description": "Selector specifying which fields to include in a partial response.", "location": "query", "type": "string"}, "key": {"description": "API key. Your API key identifies your project and provides you with API access, quota, and reports. Required unless you provide an OAuth 2.0 token.", "location": "query", "type": "string"}, "oauth_token": {"description": "OAuth 2.0 token for the current user.", "location": "query", "type": "string"}, "prettyPrint": {"default": "true", "description": "Returns response with indentations and line breaks.", "location": "query", "type": "boolean"}, "quotaUser": {"description": "Available to use for quota purposes for server-side applications. Can be any arbitrary string assigned to a user, but should not exceed 40 characters.", "location": "query", "type": "string"}, "uploadType": {"description": "Legacy upload protocol for media (e.g. \"media\", \"multipart\").", "location": "query", "type": "string"}, "upload_protocol": {"description": "Upload protocol for media (e.g. \"raw\", \"multipart\").", "location": "query", "type": "string"}}, "protocol": "rest", "resources": {"enterprises": {"methods": {"create": {"description": "Creates an enterprise. This is the last step in the enterprise signup flow. See also: SigninDetail", "flatPath": "v1/enterprises", "httpMethod": "POST", "id": "androidmanagement.enterprises.create", "parameterOrder": [], "parameters": {"agreementAccepted": {"deprecated": true, "description": "Whether the enterprise admin has seen and agreed to the managed Google Play Agreement (https://www.android.com/enterprise/terms/). Do not set this field for any customer-managed enterprise (https://developers.google.com/android/management/create-enterprise#customer-managed_enterprises). Set this to field to true for all EMM-managed enterprises (https://developers.google.com/android/management/create-enterprise#emm-managed_enterprises).", "location": "query", "type": "boolean"}, "enterpriseToken": {"description": "The enterprise token appended to the callback URL. Set this when creating a customer-managed enterprise (https://developers.google.com/android/management/create-enterprise#customer-managed_enterprises) and not when creating a deprecated EMM-managed enterprise (https://developers.google.com/android/management/create-enterprise#emm-managed_enterprises).", "location": "query", "type": "string"}, "projectId": {"description": "The ID of the Google Cloud Platform project which will own the enterprise.", "location": "query", "type": "string"}, "signupUrlName": {"description": "The name of the SignupUrl used to sign up for the enterprise. Set this when creating a customer-managed enterprise (https://developers.google.com/android/management/create-enterprise#customer-managed_enterprises) and not when creating a deprecated EMM-managed enterprise (https://developers.google.com/android/management/create-enterprise#emm-managed_enterprises).", "location": "query", "type": "string"}}, "path": "v1/enterprises", "request": {"$ref": "Enterprise"}, "response": {"$ref": "Enterprise"}, "scopes": ["https://www.googleapis.com/auth/androidmanagement"]}, "delete": {"description": "Permanently deletes an enterprise and all accounts and data associated with it. Warning: this will result in a cascaded deletion of all AM API devices associated with the deleted enterprise. Only available for EMM-managed enterprises.", "flatPath": "v1/enterprises/{enterprisesId}", "httpMethod": "DELETE", "id": "androidmanagement.enterprises.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "The name of the enterprise in the form enterprises/{enterpriseId}.", "location": "path", "pattern": "^enterprises/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "Empty"}, "scopes": ["https://www.googleapis.com/auth/androidmanagement"]}, "generateEnterpriseUpgradeUrl": {"description": "Generates an enterprise upgrade URL to upgrade an existing managed Google Play Accounts enterprise to a managed Google domain. See the guide (https://developers.google.com/android/management/upgrade-an-enterprise) for more details.", "flatPath": "v1/enterprises/{enterprisesId}:generateEnterpriseUpgradeUrl", "httpMethod": "POST", "id": "androidmanagement.enterprises.generateEnterpriseUpgradeUrl", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the enterprise to be upgraded in the form enterprises/{enterpriseId}.", "location": "path", "pattern": "^enterprises/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}:generateEnterpriseUpgradeUrl", "request": {"$ref": "GenerateEnterpriseUpgradeUrlRequest"}, "response": {"$ref": "GenerateEnterpriseUpgradeUrlResponse"}, "scopes": ["https://www.googleapis.com/auth/androidmanagement"]}, "get": {"description": "Gets an enterprise.", "flatPath": "v1/enterprises/{enterprisesId}", "httpMethod": "GET", "id": "androidmanagement.enterprises.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "The name of the enterprise in the form enterprises/{enterpriseId}.", "location": "path", "pattern": "^enterprises/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "Enterprise"}, "scopes": ["https://www.googleapis.com/auth/androidmanagement"]}, "list": {"description": "Lists EMM-managed enterprises. Only BASIC fields are returned.", "flatPath": "v1/enterprises", "httpMethod": "GET", "id": "androidmanagement.enterprises.list", "parameterOrder": [], "parameters": {"pageSize": {"description": "The requested page size. The actual page size may be fixed to a min or max value.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "A token identifying a page of results returned by the server.", "location": "query", "type": "string"}, "projectId": {"description": "Required. The Cloud project ID of the EMM managing the enterprises.", "location": "query", "type": "string"}, "view": {"description": "Specifies which Enterprise fields to return. This method only supports BASIC.", "enum": ["ENTERPRISE_VIEW_UNSPECIFIED", "BASIC"], "enumDescriptions": ["The API will default to the BASIC view for the List method.", "Includes name and enterprise_display_name fields."], "location": "query", "type": "string"}}, "path": "v1/enterprises", "response": {"$ref": "ListEnterprisesResponse"}, "scopes": ["https://www.googleapis.com/auth/androidmanagement"]}, "patch": {"description": "Updates an enterprise. See also: SigninDetail", "flatPath": "v1/enterprises/{enterprisesId}", "httpMethod": "PATCH", "id": "androidmanagement.enterprises.patch", "parameterOrder": ["name"], "parameters": {"name": {"description": "The name of the enterprise in the form enterprises/{enterpriseId}.", "location": "path", "pattern": "^enterprises/[^/]+$", "required": true, "type": "string"}, "updateMask": {"description": "The field mask indicating the fields to update. If not set, all modifiable fields will be modified.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1/{+name}", "request": {"$ref": "Enterprise"}, "response": {"$ref": "Enterprise"}, "scopes": ["https://www.googleapis.com/auth/androidmanagement"]}}, "resources": {"applications": {"methods": {"get": {"description": "Gets info about an application.", "flatPath": "v1/enterprises/{enterprisesId}/applications/{applicationsId}", "httpMethod": "GET", "id": "androidmanagement.enterprises.applications.get", "parameterOrder": ["name"], "parameters": {"languageCode": {"description": "The preferred language for localized application info, as a BCP47 tag (e.g. \"en-US\", \"de\"). If not specified the default language of the application will be used.", "location": "query", "type": "string"}, "name": {"description": "The name of the application in the form enterprises/{enterpriseId}/applications/{package_name}.", "location": "path", "pattern": "^enterprises/[^/]+/applications/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "Application"}, "scopes": ["https://www.googleapis.com/auth/androidmanagement"]}}}, "devices": {"methods": {"delete": {"description": "Deletes a device. This operation attempts to wipe the device but this is not guaranteed to succeed if the device is offline for an extended period. Deleted devices do not show up in enterprises.devices.list calls and a 404 is returned from enterprises.devices.get.", "flatPath": "v1/enterprises/{enterprisesId}/devices/{devicesId}", "httpMethod": "DELETE", "id": "androidmanagement.enterprises.devices.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "The name of the device in the form enterprises/{enterpriseId}/devices/{deviceId}.", "location": "path", "pattern": "^enterprises/[^/]+/devices/[^/]+$", "required": true, "type": "string"}, "wipeDataFlags": {"description": "Optional flags that control the device wiping behavior.", "enum": ["WIPE_DATA_FLAG_UNSPECIFIED", "PRESERVE_RESET_PROTECTION_DATA", "WIPE_EXTERNAL_STORAGE", "WIPE_ESIMS"], "enumDescriptions": ["This value is ignored.", "Preserve the factory reset protection data on the device.", "Additionally wipe the device's external storage (such as SD cards).", "For company-owned devices, this removes all eSIMs from the device when the device is wiped. In personally-owned devices, this will remove managed eSIMs (eSIMs which are added via the ADD_ESIM command) on the devices and no personally owned eSIMs will be removed."], "location": "query", "repeated": true, "type": "string"}, "wipeReasonMessage": {"description": "Optional. A short message displayed to the user before wiping the work profile on personal devices. This has no effect on company owned devices. The maximum message length is 200 characters.", "location": "query", "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "Empty"}, "scopes": ["https://www.googleapis.com/auth/androidmanagement"]}, "get": {"description": "Gets a device. Deleted devices will respond with a 404 error.", "flatPath": "v1/enterprises/{enterprisesId}/devices/{devicesId}", "httpMethod": "GET", "id": "androidmanagement.enterprises.devices.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "The name of the device in the form enterprises/{enterpriseId}/devices/{deviceId}.", "location": "path", "pattern": "^enterprises/[^/]+/devices/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "<PERSON><PERSON>"}, "scopes": ["https://www.googleapis.com/auth/androidmanagement"]}, "issueCommand": {"description": "Issues a command to a device. The Operation resource returned contains a Command in its metadata field. Use the get operation method to get the status of the command.", "flatPath": "v1/enterprises/{enterprisesId}/devices/{devicesId}:issueCommand", "httpMethod": "POST", "id": "androidmanagement.enterprises.devices.issueCommand", "parameterOrder": ["name"], "parameters": {"name": {"description": "The name of the device in the form enterprises/{enterpriseId}/devices/{deviceId}.", "location": "path", "pattern": "^enterprises/[^/]+/devices/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}:issueCommand", "request": {"$ref": "Command"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/androidmanagement"]}, "list": {"description": "Lists devices for a given enterprise. Deleted devices are not returned in the response.", "flatPath": "v1/enterprises/{enterprisesId}/devices", "httpMethod": "GET", "id": "androidmanagement.enterprises.devices.list", "parameterOrder": ["parent"], "parameters": {"pageSize": {"description": "The requested page size. The actual page size may be fixed to a min or max value.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "A token identifying a page of results returned by the server.", "location": "query", "type": "string"}, "parent": {"description": "The name of the enterprise in the form enterprises/{enterpriseId}.", "location": "path", "pattern": "^enterprises/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/devices", "response": {"$ref": "ListDevicesResponse"}, "scopes": ["https://www.googleapis.com/auth/androidmanagement"]}, "patch": {"description": "Updates a device.", "flatPath": "v1/enterprises/{enterprisesId}/devices/{devicesId}", "httpMethod": "PATCH", "id": "androidmanagement.enterprises.devices.patch", "parameterOrder": ["name"], "parameters": {"name": {"description": "The name of the device in the form enterprises/{enterpriseId}/devices/{deviceId}.", "location": "path", "pattern": "^enterprises/[^/]+/devices/[^/]+$", "required": true, "type": "string"}, "updateMask": {"description": "The field mask indicating the fields to update. If not set, all modifiable fields will be modified.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1/{+name}", "request": {"$ref": "<PERSON><PERSON>"}, "response": {"$ref": "<PERSON><PERSON>"}, "scopes": ["https://www.googleapis.com/auth/androidmanagement"]}}, "resources": {"operations": {"methods": {"cancel": {"description": "Starts asynchronous cancellation on a long-running operation. The server makes a best effort to cancel the operation, but success is not guaranteed. If the server doesn't support this method, it returns google.rpc.Code.UNIMPLEMENTED. Clients can use Operations.GetOperation or other methods to check whether the cancellation succeeded or whether the operation completed despite cancellation. On successful cancellation, the operation is not deleted; instead, it becomes an operation with an Operation.error value with a google.rpc.Status.code of 1, corresponding to Code.CANCELLED.", "flatPath": "v1/enterprises/{enterprisesId}/devices/{devicesId}/operations/{operationsId}:cancel", "httpMethod": "POST", "id": "androidmanagement.enterprises.devices.operations.cancel", "parameterOrder": ["name"], "parameters": {"name": {"description": "The name of the operation resource to be cancelled.", "location": "path", "pattern": "^enterprises/[^/]+/devices/[^/]+/operations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}:cancel", "response": {"$ref": "Empty"}, "scopes": ["https://www.googleapis.com/auth/androidmanagement"]}, "get": {"description": "Gets the latest state of a long-running operation. Clients can use this method to poll the operation result at intervals as recommended by the API service.", "flatPath": "v1/enterprises/{enterprisesId}/devices/{devicesId}/operations/{operationsId}", "httpMethod": "GET", "id": "androidmanagement.enterprises.devices.operations.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "The name of the operation resource.", "location": "path", "pattern": "^enterprises/[^/]+/devices/[^/]+/operations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/androidmanagement"]}, "list": {"description": "Lists operations that match the specified filter in the request. If the server doesn't support this method, it returns UNIMPLEMENTED.", "flatPath": "v1/enterprises/{enterprisesId}/devices/{devicesId}/operations", "httpMethod": "GET", "id": "androidmanagement.enterprises.devices.operations.list", "parameterOrder": ["name"], "parameters": {"filter": {"description": "The standard list filter.", "location": "query", "type": "string"}, "name": {"description": "The name of the operation's parent resource.", "location": "path", "pattern": "^enterprises/[^/]+/devices/[^/]+/operations$", "required": true, "type": "string"}, "pageSize": {"description": "The standard list page size.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "The standard list page token.", "location": "query", "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "ListOperationsResponse"}, "scopes": ["https://www.googleapis.com/auth/androidmanagement"]}}}}}, "enrollmentTokens": {"methods": {"create": {"description": "Creates an enrollment token for a given enterprise. It's up to the caller's responsibility to manage the lifecycle of newly created tokens and deleting them when they're not intended to be used anymore.", "flatPath": "v1/enterprises/{enterprisesId}/enrollmentTokens", "httpMethod": "POST", "id": "androidmanagement.enterprises.enrollmentTokens.create", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "The name of the enterprise in the form enterprises/{enterpriseId}.", "location": "path", "pattern": "^enterprises/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/enrollmentTokens", "request": {"$ref": "EnrollmentToken"}, "response": {"$ref": "EnrollmentToken"}, "scopes": ["https://www.googleapis.com/auth/androidmanagement"]}, "delete": {"description": "Deletes an enrollment token. This operation invalidates the token, preventing its future use.", "flatPath": "v1/enterprises/{enterprisesId}/enrollmentTokens/{enrollmentTokensId}", "httpMethod": "DELETE", "id": "androidmanagement.enterprises.enrollmentTokens.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "The name of the enrollment token in the form enterprises/{enterpriseId}/enrollmentTokens/{enrollmentTokenId}.", "location": "path", "pattern": "^enterprises/[^/]+/enrollmentTokens/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "Empty"}, "scopes": ["https://www.googleapis.com/auth/androidmanagement"]}, "get": {"description": "Gets an active, unexpired enrollment token. A partial view of the enrollment token is returned. Only the following fields are populated: name, expirationTimestamp, allowPersonalUsage, value, qrCode. This method is meant to help manage active enrollment tokens lifecycle. For security reasons, it's recommended to delete active enrollment tokens as soon as they're not intended to be used anymore.", "flatPath": "v1/enterprises/{enterprisesId}/enrollmentTokens/{enrollmentTokensId}", "httpMethod": "GET", "id": "androidmanagement.enterprises.enrollmentTokens.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the enrollment token in the form enterprises/{enterpriseId}/enrollmentTokens/{enrollmentTokenId}.", "location": "path", "pattern": "^enterprises/[^/]+/enrollmentTokens/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "EnrollmentToken"}, "scopes": ["https://www.googleapis.com/auth/androidmanagement"]}, "list": {"description": "Lists active, unexpired enrollment tokens for a given enterprise. The list items contain only a partial view of EnrollmentToken object. Only the following fields are populated: name, expirationTimestamp, allowPersonalUsage, value, qrCode. This method is meant to help manage active enrollment tokens lifecycle. For security reasons, it's recommended to delete active enrollment tokens as soon as they're not intended to be used anymore.", "flatPath": "v1/enterprises/{enterprisesId}/enrollmentTokens", "httpMethod": "GET", "id": "androidmanagement.enterprises.enrollmentTokens.list", "parameterOrder": ["parent"], "parameters": {"pageSize": {"description": "The requested page size. The service may return fewer than this value. If unspecified, at most 10 items will be returned. The maximum value is 100; values above 100 will be coerced to 100.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "A token identifying a page of results returned by the server.", "location": "query", "type": "string"}, "parent": {"description": "Required. The name of the enterprise in the form enterprises/{enterpriseId}.", "location": "path", "pattern": "^enterprises/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/enrollmentTokens", "response": {"$ref": "ListEnrollmentTokensResponse"}, "scopes": ["https://www.googleapis.com/auth/androidmanagement"]}}}, "migrationTokens": {"methods": {"create": {"description": "Creates a migration token, to migrate an existing device from being managed by the EMM's Device Policy Controller (DPC) to being managed by the Android Management API. See the guide (https://developers.google.com/android/management/dpc-migration) for more details.", "flatPath": "v1/enterprises/{enterprisesId}/migrationTokens", "httpMethod": "POST", "id": "androidmanagement.enterprises.migrationTokens.create", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. The enterprise in which this migration token is created. This must be the same enterprise which already manages the device in the Play EMM API. Format: enterprises/{enterprise}", "location": "path", "pattern": "^enterprises/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/migrationTokens", "request": {"$ref": "MigrationToken"}, "response": {"$ref": "MigrationToken"}, "scopes": ["https://www.googleapis.com/auth/androidmanagement"]}, "get": {"description": "Gets a migration token.", "flatPath": "v1/enterprises/{enterprisesId}/migrationTokens/{migrationTokensId}", "httpMethod": "GET", "id": "androidmanagement.enterprises.migrationTokens.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the migration token to retrieve. Format: enterprises/{enterprise}/migrationTokens/{migration_token}", "location": "path", "pattern": "^enterprises/[^/]+/migrationTokens/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "MigrationToken"}, "scopes": ["https://www.googleapis.com/auth/androidmanagement"]}, "list": {"description": "Lists migration tokens.", "flatPath": "v1/enterprises/{enterprisesId}/migrationTokens", "httpMethod": "GET", "id": "androidmanagement.enterprises.migrationTokens.list", "parameterOrder": ["parent"], "parameters": {"pageSize": {"description": "The maximum number of migration tokens to return. Fewer migration tokens may be returned. If unspecified, at most 100 migration tokens will be returned. The maximum value is 100; values above 100 will be coerced to 100.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "A page token, received from a previous ListMigrationTokens call. Provide this to retrieve the subsequent page.When paginating, all other parameters provided to ListMigrationTokens must match the call that provided the page token.", "location": "query", "type": "string"}, "parent": {"description": "Required. The enterprise which the migration tokens belong to. Format: enterprises/{enterprise}", "location": "path", "pattern": "^enterprises/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/migrationTokens", "response": {"$ref": "ListMigrationTokensResponse"}, "scopes": ["https://www.googleapis.com/auth/androidmanagement"]}}}, "policies": {"methods": {"delete": {"description": "Deletes a policy. This operation is only permitted if no devices are currently referencing the policy.", "flatPath": "v1/enterprises/{enterprisesId}/policies/{policiesId}", "httpMethod": "DELETE", "id": "androidmanagement.enterprises.policies.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "The name of the policy in the form enterprises/{enterpriseId}/policies/{policyId}.", "location": "path", "pattern": "^enterprises/[^/]+/policies/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "Empty"}, "scopes": ["https://www.googleapis.com/auth/androidmanagement"]}, "get": {"description": "Gets a policy.", "flatPath": "v1/enterprises/{enterprisesId}/policies/{policiesId}", "httpMethod": "GET", "id": "androidmanagement.enterprises.policies.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "The name of the policy in the form enterprises/{enterpriseId}/policies/{policyId}.", "location": "path", "pattern": "^enterprises/[^/]+/policies/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "Policy"}, "scopes": ["https://www.googleapis.com/auth/androidmanagement"]}, "list": {"description": "Lists policies for a given enterprise.", "flatPath": "v1/enterprises/{enterprisesId}/policies", "httpMethod": "GET", "id": "androidmanagement.enterprises.policies.list", "parameterOrder": ["parent"], "parameters": {"pageSize": {"description": "The requested page size. The actual page size may be fixed to a min or max value.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "A token identifying a page of results returned by the server.", "location": "query", "type": "string"}, "parent": {"description": "The name of the enterprise in the form enterprises/{enterpriseId}.", "location": "path", "pattern": "^enterprises/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/policies", "response": {"$ref": "ListPoliciesResponse"}, "scopes": ["https://www.googleapis.com/auth/androidmanagement"]}, "modifyPolicyApplications": {"description": "Updates or creates applications in a policy.", "flatPath": "v1/enterprises/{enterprisesId}/policies/{policiesId}:modifyPolicyApplications", "httpMethod": "POST", "id": "androidmanagement.enterprises.policies.modifyPolicyApplications", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the Policy containing the ApplicationPolicy objects to be updated, in the form enterprises/{enterpriseId}/policies/{policyId}.", "location": "path", "pattern": "^enterprises/[^/]+/policies/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}:modifyPolicyApplications", "request": {"$ref": "ModifyPolicyApplicationsRequest"}, "response": {"$ref": "ModifyPolicyApplicationsResponse"}, "scopes": ["https://www.googleapis.com/auth/androidmanagement"]}, "patch": {"description": "Updates or creates a policy.", "flatPath": "v1/enterprises/{enterprisesId}/policies/{policiesId}", "httpMethod": "PATCH", "id": "androidmanagement.enterprises.policies.patch", "parameterOrder": ["name"], "parameters": {"name": {"description": "The name of the policy in the form enterprises/{enterpriseId}/policies/{policyId}.", "location": "path", "pattern": "^enterprises/[^/]+/policies/[^/]+$", "required": true, "type": "string"}, "updateMask": {"description": "The field mask indicating the fields to update. If not set, all modifiable fields will be modified.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1/{+name}", "request": {"$ref": "Policy"}, "response": {"$ref": "Policy"}, "scopes": ["https://www.googleapis.com/auth/androidmanagement"]}, "removePolicyApplications": {"description": "Removes applications in a policy.", "flatPath": "v1/enterprises/{enterprisesId}/policies/{policiesId}:removePolicyApplications", "httpMethod": "POST", "id": "androidmanagement.enterprises.policies.removePolicyApplications", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the policy containing the ApplicationPolicy objects to be removed, in the form enterprises/{enterpriseId}/policies/{policyId}.", "location": "path", "pattern": "^enterprises/[^/]+/policies/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}:removePolicyApplications", "request": {"$ref": "RemovePolicyApplicationsRequest"}, "response": {"$ref": "RemovePolicyApplicationsResponse"}, "scopes": ["https://www.googleapis.com/auth/androidmanagement"]}}}, "webApps": {"methods": {"create": {"description": "Creates a web app.", "flatPath": "v1/enterprises/{enterprisesId}/webApps", "httpMethod": "POST", "id": "androidmanagement.enterprises.webApps.create", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "The name of the enterprise in the form enterprises/{enterpriseId}.", "location": "path", "pattern": "^enterprises/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/webApps", "request": {"$ref": "WebApp"}, "response": {"$ref": "WebApp"}, "scopes": ["https://www.googleapis.com/auth/androidmanagement"]}, "delete": {"description": "Deletes a web app.", "flatPath": "v1/enterprises/{enterprisesId}/webApps/{webAppsId}", "httpMethod": "DELETE", "id": "androidmanagement.enterprises.webApps.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "The name of the web app in the form enterprises/{enterpriseId}/webApps/{packageName}.", "location": "path", "pattern": "^enterprises/[^/]+/webApps/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "Empty"}, "scopes": ["https://www.googleapis.com/auth/androidmanagement"]}, "get": {"description": "Gets a web app.", "flatPath": "v1/enterprises/{enterprisesId}/webApps/{webAppsId}", "httpMethod": "GET", "id": "androidmanagement.enterprises.webApps.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "The name of the web app in the form enterprises/{enterpriseId}/webApps/{packageName}.", "location": "path", "pattern": "^enterprises/[^/]+/webApps/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "WebApp"}, "scopes": ["https://www.googleapis.com/auth/androidmanagement"]}, "list": {"description": "Lists web apps for a given enterprise.", "flatPath": "v1/enterprises/{enterprisesId}/webApps", "httpMethod": "GET", "id": "androidmanagement.enterprises.webApps.list", "parameterOrder": ["parent"], "parameters": {"pageSize": {"description": "The requested page size. This is a hint and the actual page size in the response may be different.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "A token identifying a page of results returned by the server.", "location": "query", "type": "string"}, "parent": {"description": "The name of the enterprise in the form enterprises/{enterpriseId}.", "location": "path", "pattern": "^enterprises/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/webApps", "response": {"$ref": "ListWebAppsResponse"}, "scopes": ["https://www.googleapis.com/auth/androidmanagement"]}, "patch": {"description": "Updates a web app.", "flatPath": "v1/enterprises/{enterprisesId}/webApps/{webAppsId}", "httpMethod": "PATCH", "id": "androidmanagement.enterprises.webApps.patch", "parameterOrder": ["name"], "parameters": {"name": {"description": "The name of the web app in the form enterprises/{enterpriseId}/webApps/{packageName}.", "location": "path", "pattern": "^enterprises/[^/]+/webApps/[^/]+$", "required": true, "type": "string"}, "updateMask": {"description": "The field mask indicating the fields to update. If not set, all modifiable fields will be modified.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1/{+name}", "request": {"$ref": "WebApp"}, "response": {"$ref": "WebApp"}, "scopes": ["https://www.googleapis.com/auth/androidmanagement"]}}}, "webTokens": {"methods": {"create": {"description": "Creates a web token to access an embeddable managed Google Play web UI for a given enterprise.", "flatPath": "v1/enterprises/{enterprisesId}/webTokens", "httpMethod": "POST", "id": "androidmanagement.enterprises.webTokens.create", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "The name of the enterprise in the form enterprises/{enterpriseId}.", "location": "path", "pattern": "^enterprises/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/webTokens", "request": {"$ref": "WebToken"}, "response": {"$ref": "WebToken"}, "scopes": ["https://www.googleapis.com/auth/androidmanagement"]}}}}}, "provisioningInfo": {"methods": {"get": {"description": "Get the device provisioning information by the identifier provided in the sign-in url.", "flatPath": "v1/provisioningInfo/{provisioningInfoId}", "httpMethod": "GET", "id": "androidmanagement.provisioningInfo.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The identifier that Android Device Policy passes to the 3P sign-in page in the form of provisioningInfo/{provisioning_info}.", "location": "path", "pattern": "^provisioningInfo/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "ProvisioningInfo"}, "scopes": ["https://www.googleapis.com/auth/androidmanagement"]}}}, "signupUrls": {"methods": {"create": {"description": "Creates an enterprise signup URL.", "flatPath": "v1/signupUrls", "httpMethod": "POST", "id": "androidmanagement.signupUrls.create", "parameterOrder": [], "parameters": {"adminEmail": {"description": "Optional. Email address used to prefill the admin field of the enterprise signup form. This value is a hint only and can be altered by the user. If allowedDomains is non-empty then this must belong to one of the allowedDomains.", "location": "query", "type": "string"}, "allowedDomains": {"description": "Optional. A list of domains that are permitted for the admin email. The IT admin cannot enter an email address with a domain name that is not in this list. Subdomains of domains in this list are not allowed but can be allowed by adding a second entry which has *. prefixed to the domain name (e.g. *.example.com). If the field is not present or is an empty list then the IT admin is free to use any valid domain name. Personal email domains are always allowed, but will result in the creation of a managed Google Play Accounts enterprise.", "location": "query", "repeated": true, "type": "string"}, "callbackUrl": {"description": "The callback URL that the admin will be redirected to after successfully creating an enterprise. Before redirecting there the system will add a query parameter to this URL named enterpriseToken which will contain an opaque token to be used for the create enterprise request. The URL will be parsed then reformatted in order to add the enterpriseToken parameter, so there may be some minor formatting changes.", "location": "query", "type": "string"}, "projectId": {"description": "The ID of the Google Cloud Platform project which will own the enterprise.", "location": "query", "type": "string"}}, "path": "v1/signupUrls", "response": {"$ref": "SignupUrl"}, "scopes": ["https://www.googleapis.com/auth/androidmanagement"]}}}}, "revision": "********", "rootUrl": "https://androidmanagement.googleapis.com/", "schemas": {"AdbShellCommandEvent": {"description": "A shell command was issued over ADB via “adb shell command”.", "id": "AdbShellCommandEvent", "properties": {"shellCmd": {"description": "Shell command that was issued over ADB via \"adb shell command\". Redacted to empty string on organization-owned managed profile devices.", "type": "string"}}, "type": "object"}, "AdbShellInteractiveEvent": {"description": "An ADB interactive shell was opened via “adb shell”. Intentionally empty.", "id": "AdbShellInteractiveEvent", "properties": {}, "type": "object"}, "AddEsimParams": {"description": "Parameters associated with the ADD_ESIM command to add an eSIM profile to the device.", "id": "AddEsimParams", "properties": {"activationCode": {"description": "Required. The activation code for the eSIM profile.", "type": "string"}, "activationState": {"description": "Required. The activation state of the eSIM profile once it is downloaded.", "enum": ["ACTIVATION_STATE_UNSPECIFIED", "ACTIVATED", "NOT_ACTIVATED"], "enumDescriptions": ["eSIM activation state is not specified. This defaults to the eSIM profile being NOT_ACTIVATED on personally-owned devices and ACTIVATED on company-owned devices.", "The eSIM is automatically activated after downloading. Setting this as the activation state for personally-owned devices will result in the command being rejected.", "The eSIM profile is downloaded but not activated. In this case, the user will need to activate the eSIM manually on the device."], "type": "string"}}, "type": "object"}, "AdvancedSecurityOverrides": {"description": "Advanced security settings. In most cases, setting these is not needed.", "id": "AdvancedSecurityOverrides", "properties": {"commonCriteriaMode": {"description": "Controls Common Criteria Mode—security standards defined in the Common Criteria for Information Technology Security Evaluation (https://www.commoncriteriaportal.org/) (CC). Enabling Common Criteria Mode increases certain security components on a device, see CommonCriteriaMode for details.Warning: Common Criteria Mode enforces a strict security model typically only required for IT products used in national security systems and other highly sensitive organizations. Standard device use may be affected. Only enabled if required. If Common Criteria Mode is turned off after being enabled previously, all user-configured Wi-Fi networks may be lost and any enterprise-configured Wi-Fi networks that require user input may need to be reconfigured.", "enum": ["COMMON_CRITERIA_MODE_UNSPECIFIED", "COMMON_CRITERIA_MODE_DISABLED", "COMMON_CRITERIA_MODE_ENABLED"], "enumDescriptions": ["Unspecified. Defaults to COMMON_CRITERIA_MODE_DISABLED.", "Default. Disables Common Criteria Mode.", "Enables Common Criteria Mode."], "type": "string"}, "contentProtectionPolicy": {"description": "Optional. Controls whether content protection, which scans for deceptive apps, is enabled. This is supported on Android 15 and above.", "enum": ["CONTENT_PROTECTION_POLICY_UNSPECIFIED", "CONTENT_PROTECTION_DISABLED", "CONTENT_PROTECTION_ENFORCED", "CONTENT_PROTECTION_USER_CHOICE"], "enumDescriptions": ["Unspecified. Defaults to CONTENT_PROTECTION_DISABLED.", "Content protection is disabled and the user cannot change this.", "Content protection is enabled and the user cannot change this.Supported on Android 15 and above. A NonComplianceDetail with API_LEVEL is reported if the Android version is less than 15.", "Content protection is not controlled by the policy. The user is allowed to choose the behavior of content protection.Supported on Android 15 and above. A NonComplianceDetail with API_LEVEL is reported if the Android version is less than 15."], "type": "string"}, "developerSettings": {"description": "Controls access to developer settings: developer options and safe boot. Replaces safeBootDisabled (deprecated) and debuggingFeaturesAllowed (deprecated).", "enum": ["DEVELOPER_SETTINGS_UNSPECIFIED", "DEVELOPER_SETTINGS_DISABLED", "DEVELOPER_SETTINGS_ALLOWED"], "enumDescriptions": ["Unspecified. Defaults to DEVELOPER_SETTINGS_DISABLED.", "Default. Disables all developer settings and prevents the user from accessing them.", "Allows all developer settings. The user can access and optionally configure the settings."], "type": "string"}, "googlePlayProtectVerifyApps": {"description": "Whether Google Play Protect verification (https://support.google.com/accounts/answer/2812853) is enforced. Replaces ensureVerifyAppsEnabled (deprecated).", "enum": ["GOOGLE_PLAY_PROTECT_VERIFY_APPS_UNSPECIFIED", "VERIFY_APPS_ENFORCED", "VERIFY_APPS_USER_CHOICE"], "enumDescriptions": ["Unspecified. Defaults to VERIFY_APPS_ENFORCED.", "Default. Force-enables app verification.", "Allows the user to choose whether to enable app verification."], "type": "string"}, "mtePolicy": {"description": "Optional. Controls Memory Tagging Extension (MTE) (https://source.android.com/docs/security/test/memory-safety/arm-mte) on the device. The device needs to be rebooted to apply changes to the MTE policy.", "enum": ["MTE_POLICY_UNSPECIFIED", "MTE_USER_CHOICE", "MTE_ENFORCED", "MTE_DISABLED"], "enumDescriptions": ["Unspecified. Defaults to MTE_USER_CHOICE.", "The user can choose to enable or disable M<PERSON> on the device if the device supports this.", "MTE is enabled on the device and the user is not allowed to change this setting. This can be set on fully managed devices and work profiles on company-owned devices. A NonComplianceDetail with MANAGEMENT_MODE is reported for other management modes. A NonComplianceDetail with DEVICE_INCOMPATIBLE is reported if the device does not support MTE.Supported on Android 14 and above. A NonComplianceDetail with API_LEVEL is reported if the Android version is less than 14.", "MTE is disabled on the device and the user is not allowed to change this setting. This applies only on fully managed devices. In other cases, a NonComplianceDetail with MANAGEMENT_MODE is reported. A NonComplianceDetail with DEVICE_INCOMPATIBLE is reported if the device does not support MTE.Supported on Android 14 and above. A NonComplianceDetail with API_LEVEL is reported if the Android version is less than 14."], "type": "string"}, "personalAppsThatCanReadWorkNotifications": {"description": "Personal apps that can read work profile notifications using a NotificationListenerService (https://developer.android.com/reference/android/service/notification/NotificationListenerService). By default, no personal apps (aside from system apps) can read work notifications. Each value in the list must be a package name.", "items": {"type": "string"}, "type": "array"}, "untrustedAppsPolicy": {"description": "The policy for untrusted apps (apps from unknown sources) enforced on the device. Replaces install_unknown_sources_allowed (deprecated).", "enum": ["UNTRUSTED_APPS_POLICY_UNSPECIFIED", "DISALLOW_INSTALL", "ALLOW_INSTALL_IN_PERSONAL_PROFILE_ONLY", "ALLOW_INSTALL_DEVICE_WIDE"], "enumDescriptions": ["Unspecified. Defaults to DISALLOW_INSTALL.", "Default. Disallow untrusted app installs on entire device.", "For devices with work profiles, allow untrusted app installs in the device's personal profile only.", "Allow untrusted app installs on entire device."], "type": "string"}}, "type": "object"}, "AlwaysOnVpnPackage": {"description": "Configuration for an always-on VPN connection.", "id": "AlwaysOnVpnPackage", "properties": {"lockdownEnabled": {"description": "Disallows networking when the VPN is not connected.", "type": "boolean"}, "packageName": {"description": "The package name of the VPN app.", "type": "string"}}, "type": "object"}, "ApiLevelCondition": {"deprecated": true, "description": "A compliance rule condition which is satisfied if the Android Framework API level on the device doesn't meet a minimum requirement. There can only be one rule with this type of condition per policy.", "id": "ApiLevelCondition", "properties": {"minApiLevel": {"description": "The minimum desired Android Framework API level. If the device doesn't meet the minimum requirement, this condition is satisfied. Must be greater than zero.", "format": "int32", "type": "integer"}}, "type": "object"}, "ApnPolicy": {"description": "Access Point Name (APN) policy. Configuration for Access Point Names (APNs) which may override any other APNs on the device. See OVERRIDE_APNS_ENABLED and overrideApns for details.", "id": "ApnPolicy", "properties": {"apnSettings": {"description": "Optional. APN settings for override APNs. There must not be any conflict between any of APN settings provided, otherwise the policy will be rejected. Two ApnSettings are considered to conflict when all of the following fields match on both: numericOperatorId, apn, proxyAddress, proxyPort, mmsProxyAddress, mmsProxyPort, mmsc, mvnoType, protocol, roamingProtocol. If some of the APN settings result in non-compliance of INVALID_VALUE , they will be ignored. This can be set on fully managed devices on Android 10 and above. This can also be set on work profiles on Android 13 and above and only with ApnSetting's with ENTERPRISE APN type. A NonComplianceDetail with API_LEVEL is reported if the Android version is less than 10. A NonComplianceDetail with MANAGEMENT_MODE is reported for work profiles on Android versions less than 13.", "items": {"$ref": "ApnSetting"}, "type": "array"}, "overrideApns": {"description": "Optional. Whether override APNs are disabled or enabled. See DevicePolicyManager.setOverrideApnsEnabled (https://developer.android.com/reference/android/app/admin/DevicePolicyManager#setOverrideApnsEnabled) for more details.", "enum": ["OVERRIDE_APNS_UNSPECIFIED", "OVERRIDE_APNS_DISABLED", "OVERRIDE_APNS_ENABLED"], "enumDescriptions": ["Unspecified. Defaults to OVERRIDE_APNS_DISABLED.", "Override APNs disabled. Any configured apnSettings are saved on the device, but are disabled and have no effect. Any other APNs on the device remain in use.", "Override APNs enabled. Only override APNs are in use, any other APNs are ignored. This can only be set on fully managed devices on Android 10 and above. For work profiles override APNs are enabled via preferentialNetworkServiceSettings and this value cannot be set. A NonComplianceDetail with API_LEVEL is reported if the Android version is less than 10. A NonComplianceDetail with MANAGEMENT_MODE is reported for work profiles."], "type": "string"}}, "type": "object"}, "ApnSetting": {"description": "An Access Point Name (APN) configuration for a carrier data connection. The APN provides configuration to connect a cellular network device to an IP data network. A carrier uses this setting to decide which IP address to assign, any security methods to apply, and how the device might be connected to private networks.", "id": "ApnSetting", "properties": {"alwaysOnSetting": {"description": "Optional. Whether User Plane resources have to be activated during every transition from CM-IDLE mode to CM-CONNECTED state for this APN. See 3GPP TS 23.501 section 5.6.13.", "enum": ["ALWAYS_ON_SETTING_UNSPECIFIED", "NOT_ALWAYS_ON", "ALWAYS_ON"], "enumDescriptions": ["Unspecified. Defaults to NOT_ALWAYS_ON.", "The PDU session brought up by this APN should not be always on.", "The PDU session brought up by this APN should always be on. Supported on Android 15 and above. A NonComplianceDetail with API_LEVEL is reported if the Android version is less than 15."], "type": "string"}, "apn": {"description": "Required. Name of the APN. Policy will be rejected if this field is empty.", "type": "string"}, "apnTypes": {"description": "Required. Usage categories for the APN. Policy will be rejected if this field is empty or contains APN_TYPE_UNSPECIFIED or duplicates. Multiple APN types can be set on fully managed devices. ENTERPRISE is the only allowed APN type on work profiles. A NonComplianceDetail with MANAGEMENT_MODE is reported for any other value on work profiles. APN types that are not supported on the device or management mode will be ignored. If this results in the empty list, the APN setting will be ignored, because apnTypes is a required field. A NonComplianceDetail with INVALID_VALUE is reported if none of the APN types are supported on the device or management mode.", "items": {"enum": ["APN_TYPE_UNSPECIFIED", "ENTERPRISE", "BIP", "CBS", "DEFAULT", "DUN", "EMERGENCY", "FOTA", "HIPRI", "IA", "IMS", "MCX", "MMS", "RCS", "SUPL", "VSIM", "XCAP"], "enumDescriptions": ["Unspecified. This value is not used.", "APN type for enterprise traffic. Supported on Android 13 and above. A NonComplianceDetail with API_LEVEL is reported if the Android version is less than 13.", "APN type for BIP (Bearer Independent Protocol). This can only be set on fully managed devices on Android 12 and above. A NonComplianceDetail with API_LEVEL is reported if the Android version is less than 12. A NonComplianceDetail with MANAGEMENT_MODE is reported for work profiles.", "APN type for CBS (Carrier Branded Services). This can only be set on fully managed devices. A NonComplianceDetail with MANAGEMENT_MODE is reported for work profiles.", "APN type for default data traffic. This can only be set on fully managed devices. A NonComplianceDetail with MANAGEMENT_MODE is reported for work profiles.", "APN type for DUN (Dial-up networking) traffic. This can only be set on fully managed devices. A NonComplianceDetail with MANAGEMENT_MODE is reported for work profiles.", "APN type for Emergency PDN. This is not an IA apn, but is used for access to carrier services in an emergency call situation. This can only be set on fully managed devices. A NonComplianceDetail with MANAGEMENT_MODE is reported for work profiles.", "APN type for accessing the carrier's FOTA (Firmware Over-the-Air) portal, used for over the air updates. This can only be set on fully managed devices. A NonComplianceDetail with MANAGEMENT_MODE is reported for work profiles.", "APN type for HiPri (high-priority) traffic. This can only be set on fully managed devices. A NonComplianceDetail with MANAGEMENT_MODE is reported for work profiles.", "APN type for IA (Initial Attach) APN. This can only be set on fully managed devices. A NonComplianceDetail with MANAGEMENT_MODE is reported for work profiles.", "APN type for IMS (IP Multimedia Subsystem) traffic. This can only be set on fully managed devices. A NonComplianceDetail with MANAGEMENT_MODE is reported for work profiles.", "APN type for MCX (Mission Critical Service) where X can be PTT/Video/Data. This can only be set on fully managed devices. A NonComplianceDetail with MANAGEMENT_MODE is reported for work profiles.", "APN type for MMS (Multimedia Messaging Service) traffic. This can only be set on fully managed devices. A NonComplianceDetail with MANAGEMENT_MODE is reported for work profiles.", "APN type for RCS (Rich Communication Services). This can only be set on fully managed devices on Android 15 and above. A NonComplianceDetail with API_LEVEL is reported if the Android version is less than 15. A NonComplianceDetail with MANAGEMENT_MODE is reported for work profiles.", "APN type for SUPL (Secure User Plane Location) assisted GPS. This can only be set on fully managed devices. A NonComplianceDetail with MANAGEMENT_MODE is reported for work profiles.", "APN type for VSIM (Virtual SIM) service. This can only be set on fully managed devices on Android 12 and above. A NonComplianceDetail with API_LEVEL is reported if the Android version is less than 12. A NonComplianceDetail with MANAGEMENT_MODE is reported for work profiles.", "APN type for XCAP (XML Configuration Access Protocol) traffic. This can only be set on fully managed devices on Android 11 and above. A NonComplianceDetail with API_LEVEL is reported if the Android version is less than 11. A NonComplianceDetail with MANAGEMENT_MODE is reported for work profiles."], "type": "string"}, "type": "array"}, "authType": {"description": "Optional. Authentication type of the APN.", "enum": ["AUTH_TYPE_UNSPECIFIED", "NONE", "PAP", "CHAP", "PAP_OR_CHAP"], "enumDescriptions": ["Unspecified. If username is empty, defaults to NONE. Otherwise, defaults to PAP_OR_CHAP.", "Authentication is not required.", "Authentication type for PAP.", "Authentication type for CHAP.", "Authentication type for PAP or CHAP."], "type": "string"}, "carrierId": {"description": "Optional. Carrier ID for the APN. A value of 0 (default) means not set and negative values are rejected.", "format": "int32", "type": "integer"}, "displayName": {"description": "Required. Human-readable name that describes the APN. Policy will be rejected if this field is empty.", "type": "string"}, "mmsProxyAddress": {"description": "Optional. MMS (Multimedia Messaging Service) proxy address of the APN which can be an IP address or hostname (not a URL).", "type": "string"}, "mmsProxyPort": {"description": "Optional. MMS (Multimedia Messaging Service) proxy port of the APN. A value of 0 (default) means not set and negative values are rejected.", "format": "int32", "type": "integer"}, "mmsc": {"description": "Optional. MMSC (Multimedia Messaging Service Center) URI of the APN.", "type": "string"}, "mtuV4": {"description": "Optional. The default MTU (Maximum Transmission Unit) size in bytes of the IPv4 routes brought up by this APN setting. A value of 0 (default) means not set and negative values are rejected. Supported on Android 13 and above. A NonComplianceDetail with API_LEVEL is reported if the Android version is less than 13.", "format": "int32", "type": "integer"}, "mtuV6": {"description": "Optional. The MTU (Maximum Transmission Unit) size of the IPv6 mobile interface to which the APN connected. A value of 0 (default) means not set and negative values are rejected. Supported on Android 13 and above. A NonComplianceDetail with API_LEVEL is reported if the Android version is less than 13.", "format": "int32", "type": "integer"}, "mvnoType": {"description": "Optional. MVNO match type for the APN.", "enum": ["MVNO_TYPE_UNSPECIFIED", "GID", "ICCID", "IMSI", "SPN"], "enumDescriptions": ["The MVNO type is not specified.", "MVNO type for group identifier level 1.", "MVNO type for ICCID.", "MVNO type for IMSI.", "MVNO type for SPN (service provider name)."], "type": "string"}, "networkTypes": {"description": "Optional. Radio technologies (network types) the APN may use. Policy will be rejected if this field contains NETWORK_TYPE_UNSPECIFIED or duplicates.", "items": {"enum": ["NETWORK_TYPE_UNSPECIFIED", "EDGE", "GPRS", "GSM", "HSDPA", "HSPA", "HSPAP", "HSUPA", "IWLAN", "LTE", "NR", "TD_SCDMA", "UMTS"], "enumDescriptions": ["Unspecified. This value must not be used.", "Radio technology EDGE.", "Radio technology GPRS.", "Radio technology GSM.", "Radio technology HSDPA.", "Radio technology HSPA.", "Radio technology HSPAP.", "Radio technology HSUPA.", "Radio technology IWLAN.", "Radio technology LTE.", "Radio technology NR (New Radio) 5G.", "Radio technology TD_SCDMA.", "Radio technology UMTS."], "type": "string"}, "type": "array"}, "numericOperatorId": {"description": "Optional. The numeric operator ID of the APN. Numeric operator ID is defined as MCC (Mobile Country Code) + MNC (Mobile Network Code).", "type": "string"}, "password": {"description": "Optional. APN password of the APN.", "type": "string"}, "protocol": {"description": "Optional. The protocol to use to connect to this APN.", "enum": ["PROTOCOL_UNSPECIFIED", "IP", "IPV4V6", "IPV6", "NON_IP", "PPP", "UNSTRUCTURED"], "enumDescriptions": ["The protocol is not specified.", "Internet protocol.", "Virtual PDP type introduced to handle dual IP stack UE capability.", "Internet protocol, version 6.", "Transfer of Non-IP data to external packet data network.", "Point to point protocol.", "Transfer of Unstructured data to the Data Network via N6."], "type": "string"}, "proxyAddress": {"description": "Optional. The proxy address of the APN.", "type": "string"}, "proxyPort": {"description": "Optional. The proxy port of the APN. A value of 0 (default) means not set and negative values are rejected.", "format": "int32", "type": "integer"}, "roamingProtocol": {"description": "Optional. The protocol to use to connect to this APN while the device is roaming.", "enum": ["PROTOCOL_UNSPECIFIED", "IP", "IPV4V6", "IPV6", "NON_IP", "PPP", "UNSTRUCTURED"], "enumDescriptions": ["The protocol is not specified.", "Internet protocol.", "Virtual PDP type introduced to handle dual IP stack UE capability.", "Internet protocol, version 6.", "Transfer of Non-IP data to external packet data network.", "Point to point protocol.", "Transfer of Unstructured data to the Data Network via N6."], "type": "string"}, "username": {"description": "Optional. APN username of the APN.", "type": "string"}}, "type": "object"}, "AppProcessInfo": {"description": "Information about a process. It contains process name, start time, app Uid, app Pid, seinfo tag, hash of the base APK.", "id": "AppProcessInfo", "properties": {"apkSha256Hash": {"description": "SHA-256 hash of the base APK, in hexadecimal format.", "type": "string"}, "packageNames": {"description": "Package names of all packages that are associated with the particular user ID. In most cases, this will be a single package name, the package that has been assigned that user ID. If multiple application share a UID then all packages sharing UID will be included.", "items": {"type": "string"}, "type": "array"}, "pid": {"description": "Process ID.", "format": "int32", "type": "integer"}, "processName": {"description": "Process name.", "type": "string"}, "seinfo": {"description": "SELinux policy info.", "type": "string"}, "startTime": {"description": "Process start time.", "format": "google-datetime", "type": "string"}, "uid": {"description": "UID of the package.", "format": "int32", "type": "integer"}}, "type": "object"}, "AppProcessStartEvent": {"description": "An app process was started. This is available device-wide on fully managed devices and within the work profile on organization-owned devices with a work profile.", "id": "AppProcessStartEvent", "properties": {"processInfo": {"$ref": "AppProcessInfo", "description": "Information about a process."}}, "type": "object"}, "AppTrackInfo": {"description": "Id to name association of a app track.", "id": "AppTrackInfo", "properties": {"trackAlias": {"description": "The track name associated with the trackId, set in the Play Console. The name is modifiable from Play Console.", "type": "string"}, "trackId": {"description": "The unmodifiable unique track identifier, taken from the releaseTrackId in the URL of the Play Console page that displays the app’s track information.", "type": "string"}}, "type": "object"}, "AppVersion": {"description": "This represents a single version of the app.", "id": "AppVersion", "properties": {"production": {"description": "If the value is True, it indicates that this version is a production track.", "type": "boolean"}, "trackIds": {"description": "Track identifiers that the app version is published in. This does not include the production track (see production instead).", "items": {"type": "string"}, "type": "array"}, "versionCode": {"description": "Unique increasing identifier for the app version.", "format": "int32", "type": "integer"}, "versionString": {"description": "The string used in the Play store by the app developer to identify the version. The string is not necessarily unique or localized (for example, the string could be \"1.4\").", "type": "string"}}, "type": "object"}, "Application": {"description": "Information about an app.", "id": "Application", "properties": {"appPricing": {"description": "Whether this app is free, free with in-app purchases, or paid. If the pricing is unspecified, this means the app is not generally available anymore (even though it might still be available to people who own it).", "enum": ["APP_PRICING_UNSPECIFIED", "FREE", "FREE_WITH_IN_APP_PURCHASE", "PAID"], "enumDescriptions": ["Unknown pricing, used to denote an approved app that is not generally available.", "The app is free.", "The app is free, but offers in-app purchases.", "The app is paid."], "type": "string"}, "appTracks": {"description": "Application tracks visible to the enterprise.", "items": {"$ref": "AppTrackInfo"}, "type": "array"}, "appVersions": {"description": "Versions currently available for this app.", "items": {"$ref": "AppVersion"}, "type": "array"}, "author": {"description": "The name of the author of the apps (for example, the app developer).", "type": "string"}, "availableCountries": {"description": "The countries which this app is available in as per ISO 3166-1 alpha-2.", "items": {"type": "string"}, "type": "array"}, "category": {"description": "The app category (e.g. RACING, SOCIAL, etc.)", "type": "string"}, "contentRating": {"description": "The content rating for this app.", "enum": ["CONTENT_RATING_UNSPECIFIED", "THREE_YEARS", "SEVEN_YEARS", "TWELVE_YEARS", "SIXTEEN_YEARS", "EIGHTEEN_YEARS"], "enumDescriptions": ["Unspecified.", "Content suitable for ages 3 and above only.", "Content suitable for ages 7 and above only.", "Content suitable for ages 12 and above only.", "Content suitable for ages 16 and above only.", "Content suitable for ages 18 and above only."], "type": "string"}, "description": {"description": "The localized promotional description, if available.", "type": "string"}, "distributionChannel": {"description": "How and to whom the package is made available.", "enum": ["DISTRIBUTION_CHANNEL_UNSPECIFIED", "PUBLIC_GOOGLE_HOSTED", "PRIVATE_GOOGLE_HOSTED", "PRIVATE_SELF_HOSTED"], "enumDescriptions": ["Unspecified.", "Package is available through the Play store and not restricted to a specific enterprise.", "Package is a private app (restricted to an enterprise) but hosted by Google.", "Private app (restricted to an enterprise) and is privately hosted."], "type": "string"}, "features": {"description": "Noteworthy features (if any) of this app.", "items": {"enum": ["APP_FEATURE_UNSPECIFIED", "VPN_APP"], "enumDescriptions": ["Unspecified.", "The app is a VPN."], "type": "string"}, "type": "array"}, "fullDescription": {"description": "Full app description, if available.", "type": "string"}, "iconUrl": {"description": "A link to an image that can be used as an icon for the app. This image is suitable for use up to a pixel size of 512 x 512.", "type": "string"}, "managedProperties": {"description": "The set of managed properties available to be pre-configured for the app.", "items": {"$ref": "ManagedProperty"}, "type": "array"}, "minAndroidSdkVersion": {"description": "The minimum Android SDK necessary to run the app.", "format": "int32", "type": "integer"}, "name": {"description": "The name of the app in the form enterprises/{enterprise}/applications/{package_name}.", "type": "string"}, "permissions": {"description": "The permissions required by the app.", "items": {"$ref": "ApplicationPermission"}, "type": "array"}, "playStoreUrl": {"description": "A link to the (consumer) Google Play details page for the app.", "type": "string"}, "recentChanges": {"description": "A localised description of the recent changes made to the app.", "type": "string"}, "screenshotUrls": {"description": "A list of screenshot links representing the app.", "items": {"type": "string"}, "type": "array"}, "smallIconUrl": {"description": "A link to a smaller image that can be used as an icon for the app. This image is suitable for use up to a pixel size of 128 x 128.", "type": "string"}, "title": {"description": "The title of the app. Localized.", "type": "string"}, "updateTime": {"description": "Output only. The approximate time (within 7 days) the app was last published.", "format": "google-datetime", "readOnly": true, "type": "string"}}, "type": "object"}, "ApplicationEvent": {"description": "An app-related event.", "id": "ApplicationEvent", "properties": {"createTime": {"description": "The creation time of the event.", "format": "google-datetime", "type": "string"}, "eventType": {"description": "App event type.", "enum": ["APPLICATION_EVENT_TYPE_UNSPECIFIED", "INSTALLED", "CHANGED", "DATA_CLEARED", "REMOVED", "REPLACED", "RESTARTED", "PINNED", "UNPINNED"], "enumDescriptions": ["This value is disallowed.", "The app was installed.", "The app was changed, for example, a component was enabled or disabled.", "The app data was cleared.", "The app was removed.", "A new version of the app has been installed, replacing the old version.", "The app was restarted.", "The app was pinned to the foreground.", "The app was unpinned."], "type": "string"}}, "type": "object"}, "ApplicationPermission": {"description": "A permission required by the app.", "id": "ApplicationPermission", "properties": {"description": {"description": "A longer description of the permission, providing more detail on what it affects. Localized.", "type": "string"}, "name": {"description": "The name of the permission. Localized.", "type": "string"}, "permissionId": {"description": "An opaque string uniquely identifying the permission. Not localized.", "type": "string"}}, "type": "object"}, "ApplicationPolicy": {"description": "Policy for an individual app. Note: Application availability on a given device cannot be changed using this policy if installAppsDisabled is enabled. The maximum number of applications that you can specify per policy is 3,000.", "id": "ApplicationPolicy", "properties": {"accessibleTrackIds": {"description": "List of the app’s track IDs that a device belonging to the enterprise can access. If the list contains multiple track IDs, devices receive the latest version among all accessible tracks. If the list contains no track IDs, devices only have access to the app’s production track. More details about each track are available in AppTrackInfo.", "items": {"type": "string"}, "type": "array"}, "alwaysOnVpnLockdownExemption": {"description": "Specifies whether the app is allowed networking when the VPN is not connected and alwaysOnVpnPackage.lockdownEnabled is enabled. If set to VPN_LOCKDOWN_ENFORCED, the app is not allowed networking, and if set to VPN_LOCKDOWN_EXEMPTION, the app is allowed networking. Only supported on devices running Android 10 and above. If this is not supported by the device, the device will contain a NonComplianceDetail with non_compliance_reason set to API_LEVEL and a fieldPath. If this is not applicable to the app, the device will contain a NonComplianceDetail with non_compliance_reason set to UNSUPPORTED and a fieldPath. The fieldPath is set to applications[i].alwaysOnVpnLockdownExemption, where i is the index of the package in the applications policy.", "enum": ["ALWAYS_ON_VPN_LOCKDOWN_EXEMPTION_UNSPECIFIED", "VPN_LOCKDOWN_ENFORCED", "VPN_LOCKDOWN_EXEMPTION"], "enumDescriptions": ["Unspecified. Defaults to VPN_LOCKDOWN_ENFORCED.", "The app respects the always-on VPN lockdown setting.", "The app is exempt from the always-on VPN lockdown setting."], "type": "string"}, "autoUpdateMode": {"description": "Controls the auto-update mode for the app.", "enum": ["AUTO_UPDATE_MODE_UNSPECIFIED", "AUTO_UPDATE_DEFAULT", "AUTO_UPDATE_POSTPONED", "AUTO_UPDATE_HIGH_PRIORITY"], "enumDescriptions": ["Unspecified. Defaults to AUTO_UPDATE_DEFAULT.", "The default update mode.The app is automatically updated with low priority to minimize the impact on the user.The app is updated when all of the following constraints are met: The device is not actively used. The device is connected to an unmetered network. The device is charging. The app to be updated is not running in the foreground.The device is notified about a new update within 24 hours after it is published by the developer, after which the app is updated the next time the constraints above are met.", "The app is not automatically updated for a maximum of 90 days after the app becomes out of date.90 days after the app becomes out of date, the latest available version is installed automatically with low priority (see AUTO_UPDATE_DEFAULT). After the app is updated it is not automatically updated again until 90 days after it becomes out of date again.The user can still manually update the app from the Play Store at any time.", "The app is updated as soon as possible. No constraints are applied.The device is notified as soon as possible about a new update after it becomes available.*NOTE:* Updates to apps with larger deployments across Android's ecosystem can take up to 24h."], "type": "string"}, "connectedWorkAndPersonalApp": {"description": "Controls whether the app can communicate with itself across a device’s work and personal profiles, subject to user consent.", "enum": ["CONNECTED_WORK_AND_PERSONAL_APP_UNSPECIFIED", "CONNECTED_WORK_AND_PERSONAL_APP_DISALLOWED", "CONNECTED_WORK_AND_PERSONAL_APP_ALLOWED"], "enumDescriptions": ["Unspecified. Defaults to CONNECTED_WORK_AND_PERSONAL_APPS_DISALLOWED.", "Default. Prevents the app from communicating cross-profile.", "Allows the app to communicate across profiles after receiving user consent."], "type": "string"}, "credentialProviderPolicy": {"description": "Optional. Whether the app is allowed to act as a credential provider on Android 14 and above.", "enum": ["CREDENTIAL_PROVIDER_POLICY_UNSPECIFIED", "CREDENTIAL_PROVIDER_ALLOWED"], "enumDescriptions": ["Unspecified. The behaviour is governed by credentialProviderPolicyDefault.", "App is allowed to act as a credential provider."], "type": "string"}, "defaultPermissionPolicy": {"description": "The default policy for all permissions requested by the app. If specified, this overrides the policy-level default_permission_policy which applies to all apps. It does not override the permission_grants which applies to all apps.", "enum": ["PERMISSION_POLICY_UNSPECIFIED", "PROMPT", "GRANT", "DENY"], "enumDescriptions": ["Policy not specified. If no policy is specified for a permission at any level, then the PROMPT behavior is used by default.", "Prompt the user to grant a permission.", "Automatically grant a permission.On Android 12 and above, READ_SMS (https://developer.android.com/reference/android/Manifest.permission#READ_SMS) and following sensor-related permissions can only be granted on fully managed devices: ACCESS_FINE_LOCATION (https://developer.android.com/reference/android/Manifest.permission#ACCESS_FINE_LOCATION) ACCESS_BACKGROUND_LOCATION (https://developer.android.com/reference/android/Manifest.permission#ACCESS_BACKGROUND_LOCATION) ACCESS_COARSE_LOCATION (https://developer.android.com/reference/android/Manifest.permission#ACCESS_COARSE_LOCATION) CAMERA (https://developer.android.com/reference/android/Manifest.permission#CAMERA) RECORD_AUDIO (https://developer.android.com/reference/android/Manifest.permission#RECORD_AUDIO) ACTIVITY_RECOGNITION (https://developer.android.com/reference/android/Manifest.permission#ACTIVITY_RECOGNITION) BODY_SENSORS (https://developer.android.com/reference/android/Manifest.permission#BODY_SENSORS)", "Automatically deny a permission."], "type": "string"}, "delegatedScopes": {"description": "The scopes delegated to the app from Android Device Policy. These provide additional privileges for the applications they are applied to.", "items": {"enum": ["DELEGATED_SCOPE_UNSPECIFIED", "CERT_INSTALL", "MANAGED_CONFIGURATIONS", "BLOCK_UNINSTALL", "PERMISSION_GRANT", "PACKAGE_ACCESS", "ENABLE_SYSTEM_APP", "NETWORK_ACTIVITY_LOGS", "SECURITY_LOGS", "CERT_SELECTION"], "enumDescriptions": ["No delegation scope specified.", "Grants access to certificate installation and management. This scope can be delegated to multiple applications.", "Grants access to managed configurations management. This scope can be delegated to multiple applications.", "Grants access to blocking uninstallation. This scope can be delegated to multiple applications.", "Grants access to permission policy and permission grant state. This scope can be delegated to multiple applications.", "Grants access to package access state. This scope can be delegated to multiple applications.", "Grants access for enabling system apps. This scope can be delegated to multiple applications.", "Grants access to network activity logs. Allows the delegated application to call setNetworkLoggingEnabled (https://developer.android.com/reference/android/app/admin/DevicePolicyManager#setNetworkLoggingEnabled%28android.content.ComponentName,%20boolean%29), isNetworkLoggingEnabled (https://developer.android.com/reference/android/app/admin/DevicePolicyManager#isNetworkLoggingEnabled%28android.content.ComponentName%29) and retrieveNetworkLogs (https://developer.android.com/reference/android/app/admin/DevicePolicyManager#retrieveNetworkLogs%28android.content.ComponentName,%20long%29) methods. This scope can be delegated to at most one application. Supported for fully managed devices on Android 10 and above. Supported for a work profile on Android 12 and above. When delegation is supported and set, NETWORK_ACTIVITY_LOGS is ignored.", "Grants access to security logs. Allows the delegated application to call setSecurityLoggingEnabled (https://developer.android.com/reference/android/app/admin/DevicePolicyManager#setSecurityLoggingEnabled%28android.content.ComponentName,%20boolean%29), isSecurityLoggingEnabled (https://developer.android.com/reference/android/app/admin/DevicePolicyManager#isSecurityLoggingEnabled%28android.content.ComponentName%29), retrieveSecurityLogs (https://developer.android.com/reference/android/app/admin/DevicePolicyManager#retrieveSecurityLogs%28android.content.ComponentName%29) and retrievePreRebootSecurityLogs (https://developer.android.com/reference/android/app/admin/DevicePolicyManager#retrievePreRebootSecurityLogs%28android.content.ComponentName%29) methods. This scope can be delegated to at most one application. Supported for fully managed devices and company-owned devices with a work profile on Android 12 and above. When delegation is supported and set, SECURITY_LOGS is ignored.", "Grants access to selection of KeyChain certificates on behalf of requesting apps. Once granted, the delegated application will start receiving DelegatedAdminReceiver#onChoosePrivateKeyAlias (https://developer.android.com/reference/android/app/admin/DelegatedAdminReceiver#onChoosePrivateKeyAlias%28android.content.Context,%20android.content.Intent,%20int,%20android.net.Uri,%20java.lang.String%29). Allows the delegated application to call grantKeyPairToApp (https://developer.android.com/reference/android/app/admin/DevicePolicyManager#grantKeyPairToApp%28android.content.ComponentName,%20java.lang.String,%20java.lang.String%29) and revokeKeyPairFromApp (https://developer.android.com/reference/android/app/admin/DevicePolicyManager#revokeKeyPairFromApp%28android.content.ComponentName,%20java.lang.String,%20java.lang.String%29) methods. This scope can be delegated to at most one application. choosePrivateKeyRules must be empty and privateKeySelectionEnabled has no effect if certificate selection is delegated to an application."], "type": "string"}, "type": "array"}, "disabled": {"description": "Whether the app is disabled. When disabled, the app data is still preserved.", "type": "boolean"}, "extensionConfig": {"$ref": "ExtensionConfig", "description": "Configuration to enable this app as an extension app, with the capability of interacting with Android Device Policy offline.This field can be set for at most one app.The signing key certificate fingerprint of the app on the device must match one of the entries in signingKeyFingerprintsSha256 or the signing key certificate fingerprints obtained from Play Store for the app to be able to communicate with Android Device Policy. If the app is not on Play Store and signingKeyFingerprintsSha256 is not set, a NonComplianceDetail with INVALID_VALUE is reported."}, "installConstraint": {"description": "Optional. The constraints for installing the app. You can specify a maximum of one InstallConstraint. Multiple constraints are rejected.", "items": {"$ref": "InstallConstraint"}, "type": "array"}, "installPriority": {"description": "Optional. Amongst apps with installType set to: FORCE_INSTALLED PREINSTALLEDthis controls the relative priority of installation. A value of 0 (default) means this app has no priority over other apps. For values between 1 and 10,000, a lower value means a higher priority. Values outside of the range 0 to 10,000 inclusive are rejected.", "format": "int32", "type": "integer"}, "installType": {"description": "The type of installation to perform.", "enum": ["INSTALL_TYPE_UNSPECIFIED", "PREINSTALLED", "FORCE_INSTALLED", "BLOCKED", "AVAILABLE", "REQUIRED_FOR_SETUP", "KIOSK"], "enumDescriptions": ["Unspecified. Defaults to AVAILABLE.", "The app is automatically installed and can be removed by the user.", "The app is automatically installed regardless of a set maintenance window and can't be removed by the user.", "The app is blocked and can't be installed. If the app was installed under a previous policy, it will be uninstalled. This also blocks its instant app functionality.", "The app is available to install.", "The app is automatically installed and can't be removed by the user and will prevent setup from completion until installation is complete.", "The app is automatically installed in kiosk mode: it's set as the preferred home intent and whitelisted for lock task mode. Device setup won't complete until the app is installed. After installation, users won't be able to remove the app. You can only set this installType for one app per policy. When this is present in the policy, status bar will be automatically disabled."], "type": "string"}, "lockTaskAllowed": {"deprecated": true, "description": "Whether the app is allowed to lock itself in full-screen mode. DEPRECATED. Use InstallType KIOSK or kioskCustomLauncherEnabled to configure a dedicated device.", "type": "boolean"}, "managedConfiguration": {"additionalProperties": {"description": "Properties of the object.", "type": "any"}, "description": "Managed configuration applied to the app. The format for the configuration is dictated by the ManagedProperty values supported by the app. Each field name in the managed configuration must match the key field of the ManagedProperty. The field value must be compatible with the type of the ManagedProperty: *type* *JSON value* BOOL true or false STRING string INTEGER number CHOICE string MULTISELECT array of strings HIDDEN string BUNDLE_ARRAY array of objects ", "type": "object"}, "managedConfigurationTemplate": {"$ref": "ManagedConfigurationTemplate", "description": "The managed configurations template for the app, saved from the managed configurations iframe. This field is ignored if managed_configuration is set."}, "minimumVersionCode": {"description": "The minimum version of the app that runs on the device. If set, the device attempts to update the app to at least this version code. If the app is not up-to-date, the device will contain a NonComplianceDetail with non_compliance_reason set to APP_NOT_UPDATED. The app must already be published to Google Play with a version code greater than or equal to this value. At most 20 apps may specify a minimum version code per policy.", "format": "int32", "type": "integer"}, "packageName": {"description": "The package name of the app. For example, com.google.android.youtube for the YouTube app.", "type": "string"}, "permissionGrants": {"description": "Explicit permission grants or denials for the app. These values override the default_permission_policy and permission_grants which apply to all apps.", "items": {"$ref": "PermissionGrant"}, "type": "array"}, "preferentialNetworkId": {"description": "Optional. ID of the preferential network the application uses. There must be a configuration for the specified network ID in preferentialNetworkServiceConfigs. If set to PREFERENTIAL_NETWORK_ID_UNSPECIFIED, the application will use the default network ID specified in defaultPreferentialNetworkId. See the documentation of defaultPreferentialNetworkId for the list of apps excluded from this defaulting. This applies on both work profiles and fully managed devices on Android 13 and above.", "enum": ["PREFERENTIAL_NETWORK_ID_UNSPECIFIED", "NO_PREFERENTIAL_NETWORK", "PREFERENTIAL_NETWORK_ID_ONE", "PREFERENTIAL_NETWORK_ID_TWO", "PREFERENTIAL_NETWORK_ID_THREE", "PREFERENTIAL_NETWORK_ID_FOUR", "PREFERENTIAL_NETWORK_ID_FIVE"], "enumDescriptions": ["Whether this value is valid and what it means depends on where it is used, and this is documented on the relevant fields.", "Application does not use any preferential network.", "Preferential network identifier 1.", "Preferential network identifier 2.", "Preferential network identifier 3.", "Preferential network identifier 4.", "Preferential network identifier 5."], "type": "string"}, "userControlSettings": {"description": "Optional. Specifies whether user control is permitted for the app. User control includes user actions like force-stopping and clearing app data. Certain types of apps have special treatment, see USER_CONTROL_SETTINGS_UNSPECIFIED and USER_CONTROL_ALLOWED for more details.", "enum": ["USER_CONTROL_SETTINGS_UNSPECIFIED", "USER_CONTROL_ALLOWED", "USER_CONTROL_DISALLOWED"], "enumDescriptions": ["Uses the default behaviour of the app to determine if user control is allowed or disallowed. User control is allowed by default for most apps but disallowed for following types of apps: extension apps (see extensionConfig for more details) kiosk apps (see KIOSK install type for more details) other critical system apps", "User control is allowed for the app. Kiosk apps can use this to allow user control. For extension apps (see extensionConfig for more details), user control is disallowed even if this value is set. For kiosk apps (see KIOSK install type for more details), this value can be used to allow user control.", "User control is disallowed for the app. This is supported on Android 11 and above. A NonComplianceDetail with API_LEVEL is reported if the Android version is less than 11."], "type": "string"}, "workProfileWidgets": {"description": "Specifies whether the app installed in the work profile is allowed to add widgets to the home screen.", "enum": ["WORK_PROFILE_WIDGETS_UNSPECIFIED", "WORK_PROFILE_WIDGETS_ALLOWED", "WORK_PROFILE_WIDGETS_DISALLOWED"], "enumDescriptions": ["Unspecified. Defaults to work_profile_widgets_default", "Work profile widgets are allowed. This means the application will be able to add widgets to the home screen.", "Work profile widgets are disallowed. This means the application will not be able to add widgets to the home screen."], "type": "string"}}, "type": "object"}, "ApplicationPolicyChange": {"description": "A change to be made to a single ApplicationPolicy object.", "id": "ApplicationPolicyChange", "properties": {"application": {"$ref": "ApplicationPolicy", "description": "If ApplicationPolicy.packageName matches an existing ApplicationPolicy object within the Policy being modified, then that object will be updated. Otherwise, it will be added to the end of the Policy.applications."}, "updateMask": {"description": "The field mask indicating the fields to update. If omitted, all modifiable fields are updated.", "format": "google-fieldmask", "type": "string"}}, "type": "object"}, "ApplicationReport": {"description": "Information reported about an installed app.", "id": "ApplicationReport", "properties": {"applicationSource": {"description": "The source of the package.", "enum": ["APPLICATION_SOURCE_UNSPECIFIED", "SYSTEM_APP_FACTORY_VERSION", "SYSTEM_APP_UPDATED_VERSION", "INSTALLED_FROM_PLAY_STORE"], "enumDescriptions": ["The app was sideloaded from an unspecified source.", "This is a system app from the device's factory image.", "This is an updated system app.", "The app was installed from the Google Play Store."], "type": "string"}, "displayName": {"description": "The display name of the app.", "type": "string"}, "events": {"description": "The list of app events which have occurred in the last 30 hours.", "items": {"$ref": "ApplicationEvent"}, "type": "array"}, "installerPackageName": {"description": "The package name of the app that installed this app.", "type": "string"}, "keyedAppStates": {"description": "List of keyed app states reported by the app.", "items": {"$ref": "KeyedAppState"}, "type": "array"}, "packageName": {"description": "Package name of the app.", "type": "string"}, "packageSha256Hash": {"description": "The SHA-256 hash of the app's APK file, which can be used to verify the app hasn't been modified. Each byte of the hash value is represented as a two-digit hexadecimal number.", "type": "string"}, "signingKeyCertFingerprints": {"description": "The SHA-1 hash of each android.content.pm.Signature (https://developer.android.com/reference/android/content/pm/Signature.html) associated with the app package. Each byte of each hash value is represented as a two-digit hexadecimal number.", "items": {"type": "string"}, "type": "array"}, "state": {"description": "Application state.", "enum": ["APPLICATION_STATE_UNSPECIFIED", "REMOVED", "INSTALLED"], "enumDescriptions": ["App state is unspecified", "A<PERSON> was removed from the device", "App is installed on the device"], "type": "string"}, "userFacingType": {"description": "Whether the app is user facing.", "enum": ["USER_FACING_TYPE_UNSPECIFIED", "NOT_USER_FACING", "USER_FACING"], "enumDescriptions": ["App user facing type is unspecified.", "<PERSON><PERSON> is not user facing.", "App is user facing."], "type": "string"}, "versionCode": {"description": "The app version code, which can be used to determine whether one version is more recent than another.", "format": "int32", "type": "integer"}, "versionName": {"description": "The app version as displayed to the user.", "type": "string"}}, "type": "object"}, "ApplicationReportingSettings": {"description": "Settings controlling the behavior of application reports.", "id": "ApplicationReportingSettings", "properties": {"includeRemovedApps": {"description": "Whether removed apps are included in application reports.", "type": "boolean"}}, "type": "object"}, "BackupServiceToggledEvent": {"description": "An admin has enabled or disabled backup service.", "id": "BackupServiceToggledEvent", "properties": {"adminPackageName": {"description": "Package name of the admin app requesting the change.", "type": "string"}, "adminUserId": {"description": "User ID of the admin app from the which the change was requested.", "format": "int32", "type": "integer"}, "backupServiceState": {"description": "Whether the backup service is enabled", "enum": ["BACKUP_SERVICE_STATE_UNSPECIFIED", "BACKUP_SERVICE_DISABLED", "BACKUP_SERVICE_ENABLED"], "enumDescriptions": ["No value is set", "Backup service is enabled", "Backup service is disabled"], "type": "string"}}, "type": "object"}, "BatchUsageLogEvents": {"description": "Batched event logs of events from the device.", "id": "BatchUsageLogEvents", "properties": {"device": {"description": "If present, the name of the device in the form ‘enterprises/{enterpriseId}/devices/{deviceId}’", "type": "string"}, "retrievalTime": {"description": "The device timestamp when the batch of events were collected from the device.", "format": "google-datetime", "type": "string"}, "usageLogEvents": {"description": "The list of UsageLogEvent that were reported by the device, sorted chronologically by the event time.", "items": {"$ref": "UsageLogEvent"}, "type": "array"}, "user": {"description": "If present, the resource name of the user that owns this device in the form ‘enterprises/{enterpriseId}/users/{userId}’.", "type": "string"}}, "type": "object"}, "BlockAction": {"description": "An action to block access to apps and data on a fully managed device or in a work profile. This action also triggers a device or work profile to displays a user-facing notification with information (where possible) on how to correct the compliance issue. Note: wipeAction must also be specified.", "id": "BlockAction", "properties": {"blockAfterDays": {"description": "Number of days the policy is non-compliant before the device or work profile is blocked. To block access immediately, set to 0. blockAfterDays must be less than wipeAfterDays.", "format": "int32", "type": "integer"}, "blockScope": {"description": "Specifies the scope of this BlockAction. Only applicable to devices that are company-owned.", "enum": ["BLOCK_SCOPE_UNSPECIFIED", "BLOCK_SCOPE_WORK_PROFILE", "BLOCK_SCOPE_DEVICE"], "enumDescriptions": ["Unspecified. Defaults to BLOCK_SCOPE_WORK_PROFILE.", "Block action is only applied to apps in the work profile. Apps in the personal profile are unaffected.", "Block action is applied to the entire device, including apps in the personal profile."], "type": "string"}}, "type": "object"}, "CertAuthorityInstalledEvent": {"description": "A new root certificate was installed into the system's trusted credential storage. This is available device-wide on fully managed devices and within the work profile on organization-owned devices with a work profile.", "id": "CertAuthorityInstalledEvent", "properties": {"certificate": {"description": "Subject of the certificate.", "type": "string"}, "success": {"description": "Whether the installation event succeeded.", "type": "boolean"}, "userId": {"description": "The user in which the certificate install event happened. Only available for devices running Android 11 and above.", "format": "int32", "type": "integer"}}, "type": "object"}, "CertAuthorityRemovedEvent": {"description": "A root certificate was removed from the system's trusted credential storage. This is available device-wide on fully managed devices and within the work profile on organization-owned devices with a work profile.", "id": "CertAuthorityRemovedEvent", "properties": {"certificate": {"description": "Subject of the certificate.", "type": "string"}, "success": {"description": "Whether the removal succeeded.", "type": "boolean"}, "userId": {"description": "The user in which the certificate removal event occurred. Only available for devices running Android 11 and above.", "format": "int32", "type": "integer"}}, "type": "object"}, "CertValidationFailureEvent": {"description": "An X.509v3 certificate failed to validate, currently this validation is performed on the Wi-FI access point and failure may be due to a mismatch upon server certificate validation. However it may in the future include other validation events of an X.509v3 certificate.", "id": "CertValidationFailureEvent", "properties": {"failureReason": {"description": "The reason why certification validation failed.", "type": "string"}}, "type": "object"}, "ChoosePrivateKeyRule": {"description": "Controls apps' access to private keys. The rule determines which private key, if any, Android Device Policy grants to the specified app. Access is granted either when the app calls KeyChain.choosePrivateKeyAlias (https://developer.android.com/reference/android/security/KeyChain#choosePrivateKeyAlias%28android.app.Activity,%20android.security.KeyChainAliasCallback,%20java.lang.String[],%20java.security.Principal[],%20java.lang.String,%20int,%20java.lang.String%29) (or any overloads) to request a private key alias for a given URL, or for rules that are not URL-specific (that is, if urlPattern is not set, or set to the empty string or .*) on Android 11 and above, directly so that the app can call KeyChain.getPrivateKey (https://developer.android.com/reference/android/security/KeyChain#getPrivateKey%28android.content.Context,%20java.lang.String%29), without first having to call KeyChain.choosePrivateKeyAlias.When an app calls KeyChain.choosePrivateKeyAlias if more than one choosePrivateKeyRules matches, the last matching rule defines which key alias to return.", "id": "ChoosePrivateKeyRule", "properties": {"packageNames": {"description": "The package names to which this rule applies. The hash of the signing certificate for each app is verified against the hash provided by Play. If no package names are specified, then the alias is provided to all apps that call KeyChain.choosePrivateKeyAlias (https://developer.android.com/reference/android/security/KeyChain#choosePrivateKeyAlias%28android.app.Activity,%20android.security.KeyChainAliasCallback,%20java.lang.String[],%20java.security.Principal[],%20java.lang.String,%20int,%20java.lang.String%29) or any overloads (but not without calling KeyChain.choosePrivateKeyAlias, even on Android 11 and above). Any app with the same Android UID as a package specified here will have access when they call KeyChain.choosePrivateKeyAlias.", "items": {"type": "string"}, "type": "array"}, "privateKeyAlias": {"description": "The alias of the private key to be used.", "type": "string"}, "urlPattern": {"description": "The URL pattern to match against the URL of the request. If not set or empty, it matches all URLs. This uses the regular expression syntax of java.util.regex.Pattern.", "type": "string"}}, "type": "object"}, "ClearAppsDataParams": {"description": "Parameters associated with the CLEAR_APP_DATA command to clear the data of specified apps from the device.", "id": "ClearAppsDataParams", "properties": {"packageNames": {"description": "The package names of the apps whose data will be cleared when the command is executed.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "ClearAppsDataStatus": {"description": "Status of the CLEAR_APP_DATA command to clear the data of specified apps from the device.", "id": "ClearAppsDataStatus", "properties": {"results": {"additionalProperties": {"$ref": "PerAppResult"}, "description": "The per-app results, a mapping from package names to the respective clearing result.", "type": "object"}}, "type": "object"}, "Command": {"description": "A command.", "id": "Command", "properties": {"addEsimParams": {"$ref": "AddEsimParams", "description": "Optional. Parameters for the ADD_ESIM command to add an eSIM profile to the device. If this is set, then it is suggested that type should not be set. In this case, the server automatically sets it to ADD_ESIM. It is also acceptable to explicitly set type to ADD_ESIM."}, "clearAppsDataParams": {"$ref": "ClearAppsDataParams", "description": "Parameters for the CLEAR_APP_DATA command to clear the data of specified apps from the device. See ClearAppsDataParams. If this is set, then it is suggested that type should not be set. In this case, the server automatically sets it to CLEAR_APP_DATA. It is also acceptable to explicitly set type to CLEAR_APP_DATA."}, "clearAppsDataStatus": {"$ref": "ClearAppsDataStatus", "description": "Output only. Status of the CLEAR_APP_DATA command to clear the data of specified apps from the device. See ClearAppsDataStatus.", "readOnly": true}, "createTime": {"description": "The timestamp at which the command was created. The timestamp is automatically generated by the server.", "format": "google-datetime", "type": "string"}, "duration": {"description": "The duration for which the command is valid. The command will expire if not executed by the device during this time. The default duration if unspecified is ten minutes. There is no maximum duration.", "format": "google-duration", "type": "string"}, "errorCode": {"description": "If the command failed, an error code explaining the failure. This is not set when the command is cancelled by the caller. For reasoning about command errors, prefer fields in the following order (most preferred first): 1. Command-specific fields like clearAppsDataStatus, startLostModeStatus, or similar, if they exist. 2. This field, if set. 3. The generic error field in the Operation that wraps the command.", "enum": ["COMMAND_ERROR_CODE_UNSPECIFIED", "UNKNOWN", "API_LEVEL", "MANAGEMENT_MODE", "INVALID_VALUE", "UNSUPPORTED"], "enumDescriptions": ["There was no error.", "An unknown error occurred.", "The API level of the device does not support this command.", "The management mode (profile owner, device owner, etc.) does not support the command.", "The command has an invalid parameter value.", "The device doesn't support the command. Updating Android Device Policy to the latest version may resolve the issue."], "type": "string"}, "esimStatus": {"$ref": "EsimCommandStatus", "description": "Output only. Status of an ADD_ESIM or REMOVE_ESIM command.", "readOnly": true}, "newPassword": {"description": "For commands of type RESET_PASSWORD, optionally specifies the new password. Note: The new password must be at least 6 characters long if it is numeric in case of Android 14 devices. Else the command will fail with INVALID_VALUE.", "type": "string"}, "removeEsimParams": {"$ref": "RemoveEsimParams", "description": "Optional. Parameters for the REMOVE_ESIM command to remove an eSIM profile from the device. If this is set, then it is suggested that type should not be set. In this case, the server automatically sets it to REMOVE_ESIM. It is also acceptable to explicitly set type to REMOVE_ESIM."}, "requestDeviceInfoParams": {"$ref": "RequestDeviceInfoParams", "description": "Optional. Parameters for the REQUEST_DEVICE_INFO command to get device related information. If this is set, then it is suggested that type should not be set. In this case, the server automatically sets it to REQUEST_DEVICE_INFO . It is also acceptable to explicitly set type to REQUEST_DEVICE_INFO."}, "requestDeviceInfoStatus": {"$ref": "RequestDeviceInfoStatus", "description": "Output only. Status of the REQUEST_DEVICE_INFO command.", "readOnly": true}, "resetPasswordFlags": {"description": "For commands of type RESET_PASSWORD, optionally specifies flags.", "items": {"enum": ["RESET_PASSWORD_FLAG_UNSPECIFIED", "REQUIRE_ENTRY", "DO_NOT_ASK_CREDENTIALS_ON_BOOT", "LOCK_NOW"], "enumDescriptions": ["This value is ignored.", "Don't allow other admins to change the password again until the user has entered it.", "Don't ask for user credentials on device boot.", "Lock the device after password reset."], "type": "string"}, "type": "array"}, "startLostModeParams": {"$ref": "StartLostModeParams", "description": "Parameters for the START_LOST_MODE command to put the device into lost mode. See StartLostModeParams. If this is set, then it is suggested that type should not be set. In this case, the server automatically sets it to START_LOST_MODE. It is also acceptable to explicitly set type to START_LOST_MODE."}, "startLostModeStatus": {"$ref": "StartLostModeStatus", "description": "Output only. Status of the START_LOST_MODE command to put the device into lost mode. See StartLostModeStatus.", "readOnly": true}, "stopLostModeParams": {"$ref": "StopLostModeParams", "description": "Parameters for the STOP_LOST_MODE command to take the device out of lost mode. See StopLostModeParams. If this is set, then it is suggested that type should not be set. In this case, the server automatically sets it to STOP_LOST_MODE. It is also acceptable to explicitly set type to STOP_LOST_MODE."}, "stopLostModeStatus": {"$ref": "StopLostModeStatus", "description": "Output only. Status of the STOP_LOST_MODE command to take the device out of lost mode. See StopLostModeStatus.", "readOnly": true}, "type": {"description": "The type of the command.", "enum": ["COMMAND_TYPE_UNSPECIFIED", "LOCK", "RESET_PASSWORD", "REBOOT", "RELINQUISH_OWNERSHIP", "CLEAR_APP_DATA", "START_LOST_MODE", "STOP_LOST_MODE", "ADD_ESIM", "REMOVE_ESIM", "REQUEST_DEVICE_INFO", "WIPE"], "enumDescriptions": ["This value is disallowed.", "Lock the device, as if the lock screen timeout had expired.", "Reset the user's password.", "Reboot the device. Only supported on fully managed devices running Android 7.0 (API level 24) or higher.", "Removes the work profile and all policies from a company-owned Android 8.0+ device, relinquishing the device for personal use. Apps and data associated with the personal profile(s) are preserved. The device will be deleted from the server after it acknowledges the command.", "Clears the application data of specified apps. This is supported on Android 9 and above. Note that an application can store data outside of its application data, for example in external storage or in a user dictionary. See also clear_apps_data_params.", "Puts the device into lost mode. Only supported on fully managed devices or organization-owned devices with a managed profile. See also start_lost_mode_params.", "Takes the device out of lost mode. Only supported on fully managed devices or organization-owned devices with a managed profile. See also stop_lost_mode_params.", "Adds an eSIM profile to the device. This is supported on Android 15 and above. See also addEsimParams. To remove an eSIM profile, use the REMOVE_ESIM command. To determine what happens to the eSIM profile when a device is wiped, set wipeDataFlags in the policy. Note: To provision multiple eSIMs on a single device, it is recommended to introduce a delay of a few minutes between successive executions of the command.", "Removes an eSIM profile from the device. This is supported on Android 15 and above. See also removeEsimParams.", "Request information related to the device.", "Wipes the device, via a factory reset for a company owned device, or by deleting the work profile for a personally owned device with work profile. The wipe only occurs once the device acknowledges the command. The command can be cancelled before then."], "type": "string"}, "userName": {"description": "The resource name of the user that owns the device in the form enterprises/{enterpriseId}/users/{userId}. This is automatically generated by the server based on the device the command is sent to.", "type": "string"}, "wipeParams": {"$ref": "WipeParams", "description": "Optional. Parameters for the WIPE command to wipe the device. If this is set, then it is suggested that type should not be set. In this case, the server automatically sets it to WIPE. It is also acceptable to explicitly set type to WIPE."}}, "type": "object"}, "CommonCriteriaModeInfo": {"description": "Information about Common Criteria Mode—security standards defined in the Common Criteria for Information Technology Security Evaluation (https://www.commoncriteriaportal.org/) (CC).This information is only available if statusReportingSettings.commonCriteriaModeEnabled is true in the device's policy.", "id": "CommonCriteriaModeInfo", "properties": {"commonCriteriaModeStatus": {"description": "Whether Common Criteria Mode is enabled.", "enum": ["COMMON_CRITERIA_MODE_STATUS_UNKNOWN", "COMMON_CRITERIA_MODE_DISABLED", "COMMON_CRITERIA_MODE_ENABLED"], "enumDescriptions": ["Unknown status.", "Common Criteria Mode is currently disabled.", "Common Criteria Mode is currently enabled."], "type": "string"}, "policySignatureVerificationStatus": {"description": "Output only. The status of policy signature verification.", "enum": ["POLICY_SIGNATURE_VERIFICATION_STATUS_UNSPECIFIED", "POLICY_SIGNATURE_VERIFICATION_DISABLED", "POLICY_SIGNATURE_VERIFICATION_SUCCEEDED", "POLICY_SIGNATURE_VERIFICATION_NOT_SUPPORTED", "POLICY_SIGNATURE_VERIFICATION_FAILED"], "enumDescriptions": ["Unspecified. The verification status has not been reported. This is set only if statusReportingSettings.commonCriteriaModeEnabled is false.", "Policy signature verification is disabled on the device as common_criteria_mode is set to false.", "Policy signature verification succeeded.", "Policy signature verification is not supported, e.g. because the device has been enrolled with a CloudDPC version that does not support the policy signature verification.", "The policy signature verification failed. The policy has not been applied."], "readOnly": true, "type": "string"}}, "type": "object"}, "ComplianceRule": {"deprecated": true, "description": "A rule declaring which mitigating actions to take when a device is not compliant with its policy. For every rule, there is always an implicit mitigating action to set policy_compliant to false for the Device resource, and display a message on the device indicating that the device is not compliant with its policy. Other mitigating actions may optionally be taken as well, depending on the field values in the rule.", "id": "ComplianceRule", "properties": {"apiLevelCondition": {"$ref": "ApiLevelCondition", "description": "A condition which is satisfied if the Android Framework API level on the device doesn't meet a minimum requirement."}, "disableApps": {"description": "If set to true, the rule includes a mitigating action to disable apps so that the device is effectively disabled, but app data is preserved. If the device is running an app in locked task mode, the app will be closed and a UI showing the reason for non-compliance will be displayed.", "type": "boolean"}, "nonComplianceDetailCondition": {"$ref": "NonComplianceDetailCondition", "description": "A condition which is satisfied if there exists any matching NonComplianceDetail for the device."}, "packageNamesToDisable": {"description": "If set, the rule includes a mitigating action to disable apps specified in the list, but app data is preserved.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "ConnectEvent": {"description": "A TCP connect event was initiated through the standard network stack.", "id": "ConnectEvent", "properties": {"destinationIpAddress": {"description": "The destination IP address of the connect call.", "type": "string"}, "destinationPort": {"description": "The destination port of the connect call.", "format": "int32", "type": "integer"}, "packageName": {"description": "The package name of the UID that performed the connect call.", "type": "string"}}, "type": "object"}, "ContactInfo": {"description": "Contact details for managed Google Play enterprises.", "id": "ContactInfo", "properties": {"contactEmail": {"description": "Email address for a point of contact, which will be used to send important announcements related to managed Google Play.", "type": "string"}, "dataProtectionOfficerEmail": {"description": "The email of the data protection officer. The email is validated but not verified.", "type": "string"}, "dataProtectionOfficerName": {"description": "The name of the data protection officer.", "type": "string"}, "dataProtectionOfficerPhone": {"description": "The phone number of the data protection officer The phone number is validated but not verified.", "type": "string"}, "euRepresentativeEmail": {"description": "The email of the EU representative. The email is validated but not verified.", "type": "string"}, "euRepresentativeName": {"description": "The name of the EU representative.", "type": "string"}, "euRepresentativePhone": {"description": "The phone number of the EU representative. The phone number is validated but not verified.", "type": "string"}}, "type": "object"}, "ContentProviderEndpoint": {"description": "This feature is not generally available.", "id": "ContentProviderEndpoint", "properties": {"packageName": {"description": "This feature is not generally available.", "type": "string"}, "signingCertsSha256": {"description": "Required. This feature is not generally available.", "items": {"type": "string"}, "type": "array"}, "uri": {"description": "This feature is not generally available.", "type": "string"}}, "type": "object"}, "CrossProfilePolicies": {"description": "Controls the data from the work profile that can be accessed from the personal profile and vice versa. A NonComplianceDetail with MANAGEMENT_MODE is reported if the device does not have a work profile.", "id": "CrossProfilePolicies", "properties": {"crossProfileAppFunctions": {"description": "Optional. Controls whether personal profile apps can invoke app functions exposed by apps in the work profile.", "enum": ["CROSS_PROFILE_APP_FUNCTIONS_UNSPECIFIED", "CROSS_PROFILE_APP_FUNCTIONS_DISALLOWED", "CROSS_PROFILE_APP_FUNCTIONS_ALLOWED"], "enumDescriptions": ["Unspecified. If appFunctions is set to APP_FUNCTIONS_ALLOWED, defaults to CROSS_PROFILE_APP_FUNCTIONS_ALLOWED. If appFunctions is set to APP_FUNCTIONS_DISALLOWED, defaults to CROSS_PROFILE_APP_FUNCTIONS_DISALLOWED.", "Personal profile apps are not allowed to invoke app functions exposed by apps in the work profile.", "Personal profile apps can invoke app functions exposed by apps in the work profile. If this is set, appFunctions must not be set to APP_FUNCTIONS_DISALLOWED, otherwise the policy will be rejected."], "type": "string"}, "crossProfileCopyPaste": {"description": "Whether text copied from one profile (personal or work) can be pasted in the other profile.", "enum": ["CROSS_PROFILE_COPY_PASTE_UNSPECIFIED", "COPY_FROM_WORK_TO_PERSONAL_DISALLOWED", "CROSS_PROFILE_COPY_PASTE_ALLOWED"], "enumDescriptions": ["Unspecified. Defaults to COPY_FROM_WORK_TO_PERSONAL_DISALLOWED", "Default. Prevents users from pasting into the personal profile text copied from the work profile. Text copied from the personal profile can be pasted into the work profile, and text copied from the work profile can be pasted into the work profile.", "Text copied in either profile can be pasted in the other profile."], "type": "string"}, "crossProfileDataSharing": {"description": "Whether data from one profile (personal or work) can be shared with apps in the other profile. Specifically controls simple data sharing via intents. Management of other cross-profile communication channels, such as contact search, copy/paste, or connected work & personal apps, are configured separately.", "enum": ["CROSS_PROFILE_DATA_SHARING_UNSPECIFIED", "CROSS_PROFILE_DATA_SHARING_DISALLOWED", "DATA_SHARING_FROM_WORK_TO_PERSONAL_DISALLOWED", "CROSS_PROFILE_DATA_SHARING_ALLOWED"], "enumDescriptions": ["Unspecified. Defaults to DATA_SHARING_FROM_WORK_TO_PERSONAL_DISALLOWED.", "Prevents data from being shared from both the personal profile to the work profile and the work profile to the personal profile.", "Default. Prevents users from sharing data from the work profile to apps in the personal profile. Personal data can be shared with work apps.", "Data from either profile can be shared with the other profile."], "type": "string"}, "exemptionsToShowWorkContactsInPersonalProfile": {"$ref": "PackageNameList", "description": "List of apps which are excluded from the ShowWorkContactsInPersonalProfile setting. For this to be set, ShowWorkContactsInPersonalProfile must be set to one of the following values: SHOW_WORK_CONTACTS_IN_PERSONAL_PROFILE_ALLOWED. In this case, these exemptions act as a blocklist. SHOW_WORK_CONTACTS_IN_PERSONAL_PROFILE_DISALLOWED. In this case, these exemptions act as an allowlist. SHOW_WORK_CONTACTS_IN_PERSONAL_PROFILE_DISALLOWED_EXCEPT_SYSTEM. In this case, these exemptions act as an allowlist, in addition to the already allowlisted system apps.Supported on Android 14 and above. A NonComplianceDetail with API_LEVEL is reported if the Android version is less than 14."}, "showWorkContactsInPersonalProfile": {"description": "Whether personal apps can access contacts stored in the work profile.See also exemptions_to_show_work_contacts_in_personal_profile.", "enum": ["SHOW_WORK_CONTACTS_IN_PERSONAL_PROFILE_UNSPECIFIED", "SHOW_WORK_CONTACTS_IN_PERSONAL_PROFILE_DISALLOWED", "SHOW_WORK_CONTACTS_IN_PERSONAL_PROFILE_ALLOWED", "SHOW_WORK_CONTACTS_IN_PERSONAL_PROFILE_DISALLOWED_EXCEPT_SYSTEM"], "enumDescriptions": ["Unspecified. Defaults to SHOW_WORK_CONTACTS_IN_PERSONAL_PROFILE_ALLOWED.When this is set, exemptions_to_show_work_contacts_in_personal_profile must not be set.", "Prevents personal apps from accessing work profile contacts and looking up work contacts.When this is set, personal apps specified in exemptions_to_show_work_contacts_in_personal_profile are allowlisted and can access work profile contacts directly.Supported on Android 7.0 and above. A NonComplianceDetail with API_LEVEL is reported if the Android version is less than 7.0.", "Default. Allows apps in the personal profile to access work profile contacts including contact searches and incoming calls.When this is set, personal apps specified in exemptions_to_show_work_contacts_in_personal_profile are blocklisted and can not access work profile contacts directly.Supported on Android 7.0 and above. A NonComplianceDetail with API_LEVEL is reported if the Android version is less than 7.0.", "Prevents most personal apps from accessing work profile contacts including contact searches and incoming calls, except for the OEM default Dialer, Messages, and Contacts apps. Neither user-configured Dialer, Messages, and Contacts apps, nor any other system or play installed apps, will be able to query work contacts directly.When this is set, personal apps specified in exemptions_to_show_work_contacts_in_personal_profile are allowlisted and can access work profile contacts.Supported on Android 14 and above. If this is set on a device with Android version less than 14, the behaviour falls back to SHOW_WORK_CONTACTS_IN_PERSONAL_PROFILE_DISALLOWED and a NonComplianceDetail with API_LEVEL is reported."], "type": "string"}, "workProfileWidgetsDefault": {"description": "Specifies the default behaviour for work profile widgets. If the policy does not specify work_profile_widgets for a specific application, it will behave according to the value specified here.", "enum": ["WORK_PROFILE_WIDGETS_DEFAULT_UNSPECIFIED", "WORK_PROFILE_WIDGETS_DEFAULT_ALLOWED", "WORK_PROFILE_WIDGETS_DEFAULT_DISALLOWED"], "enumDescriptions": ["Unspecified. Defaults to WORK_PROFILE_WIDGETS_DEFAULT_DISALLOWED.", "Work profile widgets are allowed by default. This means that if the policy does not specify work_profile_widgets as WORK_PROFILE_WIDGETS_DISALLOWED for the application, it will be able to add widgets to the home screen.", "Work profile widgets are disallowed by default. This means that if the policy does not specify work_profile_widgets as WORK_PROFILE_WIDGETS_ALLOWED for the application, it will be unable to add widgets to the home screen."], "type": "string"}}, "type": "object"}, "CryptoSelfTestCompletedEvent": {"description": "Validates whether Android’s built-in cryptographic library (BoringSSL) is valid. Should always succeed on device boot, if it fails, the device should be considered untrusted.", "id": "CryptoSelfTestCompletedEvent", "properties": {"success": {"description": "Whether the test succeeded.", "type": "boolean"}}, "type": "object"}, "Date": {"description": "Represents a whole or partial calendar date, such as a birthday. The time of day and time zone are either specified elsewhere or are insignificant. The date is relative to the Gregorian Calendar. This can represent one of the following: A full date, with non-zero year, month, and day values. A month and day, with a zero year (for example, an anniversary). A year on its own, with a zero month and a zero day. A year and month, with a zero day (for example, a credit card expiration date).Related types: google.type.TimeOfDay google.type.DateTime google.protobuf.Timestamp", "id": "Date", "properties": {"day": {"description": "Day of a month. Must be from 1 to 31 and valid for the year and month, or 0 to specify a year by itself or a year and month where the day isn't significant.", "format": "int32", "type": "integer"}, "month": {"description": "Month of a year. Must be from 1 to 12, or 0 to specify a year without a month and day.", "format": "int32", "type": "integer"}, "year": {"description": "Year of the date. Must be from 1 to 9999, or 0 to specify a date without a year.", "format": "int32", "type": "integer"}}, "type": "object"}, "Device": {"description": "A device owned by an enterprise. Unless otherwise noted, all fields are read-only and can't be modified by enterprises.devices.patch.", "id": "<PERSON><PERSON>", "properties": {"apiLevel": {"description": "The API level of the Android platform version running on the device.", "format": "int32", "type": "integer"}, "applicationReports": {"description": "Reports for apps installed on the device. This information is only available when application_reports_enabled is true in the device's policy.", "items": {"$ref": "ApplicationReport"}, "type": "array"}, "appliedPasswordPolicies": {"description": "The password requirements currently applied to the device. The applied requirements may be slightly different from those specified in passwordPolicies in some cases. fieldPath is set based on passwordPolicies.", "items": {"$ref": "PasswordRequirements"}, "type": "array"}, "appliedPolicyName": {"description": "The name of the policy currently applied to the device.", "type": "string"}, "appliedPolicyVersion": {"description": "The version of the policy currently applied to the device.", "format": "int64", "type": "string"}, "appliedState": {"description": "The state currently applied to the device.", "enum": ["DEVICE_STATE_UNSPECIFIED", "ACTIVE", "DISABLED", "DELETED", "PROVISIONING", "LOST", "PREPARING_FOR_MIGRATION", "DEACTIVATED_BY_DEVICE_FINANCE"], "enumDescriptions": ["This value is disallowed.", "The device is active.", "The device is disabled.", "The device was deleted. This state is never returned by an API call, but is used in the final status report when the device acknowledges the deletion. If the device is deleted via the API call, this state is published to Pub/Sub. If the user deletes the work profile or resets the device, the device state will remain unknown to the server.", "The device is being provisioned. Newly enrolled devices are in this state until they have a policy applied.", "The device is lost. This state is only possible on organization-owned devices.", "The device is preparing for migrating to Android Management API. No further action is needed for the migration to continue.", "This is a financed device that has been \"locked\" by the financing agent. This means certain policy settings have been applied which limit device functionality until the device has been \"unlocked\" by the financing agent. The device will continue to apply policy settings excluding those overridden by the financing agent. When the device is \"locked\", the state is reported in appliedState as DEACTIVATED_BY_DEVICE_FINANCE."], "type": "string"}, "commonCriteriaModeInfo": {"$ref": "CommonCriteriaModeInfo", "description": "Information about Common Criteria Mode—security standards defined in the Common Criteria for Information Technology Security Evaluation (https://www.commoncriteriaportal.org/) (CC).This information is only available if statusReportingSettings.commonCriteriaModeEnabled is true in the device's policy the device is company-owned."}, "deviceSettings": {"$ref": "DeviceSettings", "description": "Device settings information. This information is only available if deviceSettingsEnabled is true in the device's policy."}, "disabledReason": {"$ref": "UserFacingMessage", "description": "If the device state is DISABLED, an optional message that is displayed on the device indicating the reason the device is disabled. This field can be modified by a patch request."}, "displays": {"description": "Detailed information about displays on the device. This information is only available if displayInfoEnabled is true in the device's policy.", "items": {"$ref": "Display"}, "type": "array"}, "dpcMigrationInfo": {"$ref": "DpcMigrationInfo", "description": "Output only. Information related to whether this device was migrated from being managed by another Device Policy Controller (DPC).", "readOnly": true}, "enrollmentTime": {"description": "The time of device enrollment.", "format": "google-datetime", "type": "string"}, "enrollmentTokenData": {"description": "If the device was enrolled with an enrollment token with additional data provided, this field contains that data.", "type": "string"}, "enrollmentTokenName": {"description": "If the device was enrolled with an enrollment token, this field contains the name of the token.", "type": "string"}, "hardwareInfo": {"$ref": "HardwareInfo", "description": "Detailed information about the device hardware."}, "hardwareStatusSamples": {"description": "Hardware status samples in chronological order. This information is only available if hardwareStatusEnabled is true in the device's policy.", "items": {"$ref": "HardwareStatus"}, "type": "array"}, "lastPolicyComplianceReportTime": {"deprecated": true, "description": "Deprecated.", "format": "google-datetime", "type": "string"}, "lastPolicySyncTime": {"description": "The last time the device fetched its policy.", "format": "google-datetime", "type": "string"}, "lastStatusReportTime": {"description": "The last time the device sent a status report.", "format": "google-datetime", "type": "string"}, "managementMode": {"description": "The type of management mode Android Device Policy takes on the device. This influences which policy settings are supported.", "enum": ["MANAGEMENT_MODE_UNSPECIFIED", "DEVICE_OWNER", "PROFILE_OWNER"], "enumDescriptions": ["This value is disallowed.", "Device owner. Android Device Policy has full control over the device.", "Profile owner. Android Device Policy has control over a managed profile on the device."], "type": "string"}, "memoryEvents": {"description": "Events related to memory and storage measurements in chronological order. This information is only available if memoryInfoEnabled is true in the device's policy.Events are retained for a certain period of time and old events are deleted.", "items": {"$ref": "MemoryEvent"}, "type": "array"}, "memoryInfo": {"$ref": "MemoryInfo", "description": "Memory information: contains information about device memory and storage."}, "name": {"description": "The name of the device in the form enterprises/{enterpriseId}/devices/{deviceId}.", "type": "string"}, "networkInfo": {"$ref": "NetworkInfo", "description": "Device network information. This information is only available if networkInfoEnabled is true in the device's policy."}, "nonComplianceDetails": {"description": "Details about policy settings that the device is not compliant with.", "items": {"$ref": "NonComplianceDetail"}, "type": "array"}, "ownership": {"description": "Ownership of the managed device.", "enum": ["OWNERSHIP_UNSPECIFIED", "COMPANY_OWNED", "PERSONALLY_OWNED"], "enumDescriptions": ["Ownership is unspecified.", "Device is company-owned.", "<PERSON><PERSON> is personally-owned."], "type": "string"}, "policyCompliant": {"description": "Whether the device is compliant with its policy.", "type": "boolean"}, "policyName": {"description": "The name of the policy applied to the device, in the form enterprises/{enterpriseId}/policies/{policyId}. If not specified, the policy_name for the device's user is applied. This field can be modified by a patch request. You can specify only the policyId when calling enterprises.devices.patch, as long as the policyId doesn’t contain any slashes. The rest of the policy name is inferred.", "type": "string"}, "powerManagementEvents": {"description": "Power management events on the device in chronological order. This information is only available if powerManagementEventsEnabled is true in the device's policy.", "items": {"$ref": "PowerManagementEvent"}, "type": "array"}, "previousDeviceNames": {"description": "If the same physical device has been enrolled multiple times, this field contains its previous device names. The serial number is used as the unique identifier to determine if the same physical device has enrolled previously. The names are in chronological order.", "items": {"type": "string"}, "type": "array"}, "securityPosture": {"$ref": "SecurityPosture", "description": "Device's security posture value that reflects how secure the device is."}, "softwareInfo": {"$ref": "SoftwareInfo", "description": "Detailed information about the device software. This information is only available if softwareInfoEnabled is true in the device's policy."}, "state": {"description": "The state to be applied to the device. This field can be modified by a patch request. Note that when calling enterprises.devices.patch, ACTIVE and DISABLED are the only allowable values. To enter the device into a DELETED state, call enterprises.devices.delete.", "enum": ["DEVICE_STATE_UNSPECIFIED", "ACTIVE", "DISABLED", "DELETED", "PROVISIONING", "LOST", "PREPARING_FOR_MIGRATION", "DEACTIVATED_BY_DEVICE_FINANCE"], "enumDescriptions": ["This value is disallowed.", "The device is active.", "The device is disabled.", "The device was deleted. This state is never returned by an API call, but is used in the final status report when the device acknowledges the deletion. If the device is deleted via the API call, this state is published to Pub/Sub. If the user deletes the work profile or resets the device, the device state will remain unknown to the server.", "The device is being provisioned. Newly enrolled devices are in this state until they have a policy applied.", "The device is lost. This state is only possible on organization-owned devices.", "The device is preparing for migrating to Android Management API. No further action is needed for the migration to continue.", "This is a financed device that has been \"locked\" by the financing agent. This means certain policy settings have been applied which limit device functionality until the device has been \"unlocked\" by the financing agent. The device will continue to apply policy settings excluding those overridden by the financing agent. When the device is \"locked\", the state is reported in appliedState as DEACTIVATED_BY_DEVICE_FINANCE."], "type": "string"}, "systemProperties": {"additionalProperties": {"type": "string"}, "description": "Map of selected system properties name and value related to the device. This information is only available if systemPropertiesEnabled is true in the device's policy.", "type": "object"}, "user": {"$ref": "User", "description": "The user who owns the device."}, "userName": {"description": "The resource name of the user that owns this device in the form enterprises/{enterpriseId}/users/{userId}.", "type": "string"}}, "type": "object"}, "DeviceConnectivityManagement": {"description": "Covers controls for device connectivity such as Wi-Fi, USB data access, keyboard/mouse connections, and more.", "id": "DeviceConnectivityManagement", "properties": {"apnPolicy": {"$ref": "ApnPolicy", "description": "Optional. Access Point Name (APN) policy. Configuration for Access Point Names (APNs) which may override any other APNs on the device. See OVERRIDE_APNS_ENABLED and overrideApns for details."}, "bluetoothSharing": {"description": "Optional. Controls whether Bluetooth sharing is allowed.", "enum": ["BLUETOOTH_SHARING_UNSPECIFIED", "BLUETOOTH_SHARING_ALLOWED", "BLUETOOTH_SHARING_DISALLOWED"], "enumDescriptions": ["Unspecified. Defaults to BLUETOOTH_SHARING_DISALLOWED on work profiles and BLUETOOTH_SHARING_ALLOWED on fully managed devices.", "Bluetooth sharing is allowed.Supported on Android 8 and above. A NonComplianceDetail with API_LEVEL is reported on work profiles if the Android version is less than 8.", "Bluetooth sharing is disallowed.Supported on Android 8 and above. A NonComplianceDetail with API_LEVEL is reported on fully managed devices if the Android version is less than 8."], "type": "string"}, "configureWifi": {"description": "Controls Wi-Fi configuring privileges. Based on the option set, user will have either full or limited or no control in configuring Wi-Fi networks.", "enum": ["CONFIGURE_WIFI_UNSPECIFIED", "ALLOW_CONFIGURING_WIFI", "DISALLOW_ADD_WIFI_CONFIG", "DISALLOW_CONFIGURING_WIFI"], "enumDescriptions": ["Unspecified. Defaults to ALLOW_CONFIGURING_WIFI unless wifiConfigDisabled is set to true. If wifiConfigDisabled is set to true, this is equivalent to DISALLOW_CONFIGURING_WIFI.", "The user is allowed to configure Wi-Fi. wifiConfigDisabled is ignored.", "Adding new Wi-Fi configurations is disallowed. The user is only able to switch between already configured networks. Supported on Android 13 and above, on fully managed devices and work profiles on company-owned devices. If the setting is not supported, ALLOW_CONFIGURING_WIFI is set. A NonComplianceDetail with API_LEVEL is reported if the Android version is less than 13. wifiConfigDisabled is ignored.", "Disallows configuring Wi-Fi networks. The setting wifiConfigDisabled is ignored when this value is set. Supported on fully managed devices and work profile on company-owned devices, on all supported API levels. For fully managed devices, setting this removes all configured networks and retains only the networks configured using openNetworkConfiguration policy. For work profiles on company-owned devices, existing configured networks are not affected and the user is not allowed to add, remove, or modify Wi-Fi networks. Note: If a network connection can't be made at boot time and configuring Wi-Fi is disabled then network escape hatch will be shown in order to refresh the device policy (see networkEscapeHatchEnabled)."], "type": "string"}, "preferentialNetworkServiceSettings": {"$ref": "PreferentialNetworkServiceSettings", "description": "Optional. Preferential network service configuration. Setting this field will override preferentialNetworkService. This can be set on both work profiles and fully managed devices on Android 13 and above. See 5G network slicing (https://developers.google.com/android/management/5g-network-slicing) guide for more details."}, "tetheringSettings": {"description": "Controls tethering settings. Based on the value set, the user is partially or fully disallowed from using different forms of tethering.", "enum": ["TETHERING_SETTINGS_UNSPECIFIED", "ALLOW_ALL_TETHERING", "DISALLOW_WIFI_TETHERING", "DISALLOW_ALL_TETHERING"], "enumDescriptions": ["Unspecified. Defaults to ALLOW_ALL_TETHERING unless tetheringConfigDisabled is set to true. If tetheringConfigDisabled is set to true, this is equivalent to DISALLOW_ALL_TETHERING.", "Allows configuration and use of all forms of tethering. tetheringConfigDisabled is ignored.", "Disallows the user from using Wi-Fi tethering. Supported on company owned devices running Android 13 and above. If the setting is not supported, ALLOW_ALL_TETHERING will be set. A NonComplianceDetail with API_LEVEL is reported if the Android version is less than 13. tetheringConfigDisabled is ignored.", "Disallows all forms of tethering. Supported on fully managed devices and work profile on company-owned devices, on all supported android versions. The setting tetheringConfigDisabled is ignored."], "type": "string"}, "usbDataAccess": {"description": "Controls what files and/or data can be transferred via USB. Supported only on company-owned devices.", "enum": ["USB_DATA_ACCESS_UNSPECIFIED", "ALLOW_USB_DATA_TRANSFER", "DISALLOW_USB_FILE_TRANSFER", "DISALLOW_USB_DATA_TRANSFER"], "enumDescriptions": ["Unspecified. Defaults to DISALLOW_USB_FILE_TRANSFER.", "All types of USB data transfers are allowed. usbFileTransferDisabled is ignored.", "Transferring files over USB is disallowed. Other types of USB data connections, such as mouse and keyboard connection, are allowed. usbFileTransferDisabled is ignored.", "When set, all types of USB data transfers are prohibited. Supported for devices running Android 12 or above with USB HAL 1.3 or above. If the setting is not supported, DISALLOW_USB_FILE_TRANSFER will be set. A NonComplianceDetail with API_LEVEL is reported if the Android version is less than 12. A NonComplianceDetail with DEVICE_INCOMPATIBLE is reported if the device does not have USB HAL 1.3 or above. usbFileTransferDisabled is ignored."], "type": "string"}, "wifiDirectSettings": {"description": "Controls configuring and using Wi-Fi direct settings. Supported on company-owned devices running Android 13 and above.", "enum": ["WIFI_DIRECT_SETTINGS_UNSPECIFIED", "ALLOW_WIFI_DIRECT", "DISALLOW_WIFI_DIRECT"], "enumDescriptions": ["Unspecified. Defaults to ALLOW_WIFI_DIRECT", "The user is allowed to use Wi-Fi direct.", "The user is not allowed to use Wi-Fi direct. A NonComplianceDetail with API_LEVEL is reported if the Android version is less than 13."], "type": "string"}, "wifiRoamingPolicy": {"$ref": "WifiRoamingPolicy", "description": "Optional. Wi-Fi roaming policy."}, "wifiSsidPolicy": {"$ref": "WifiSsidPolicy", "description": "Restrictions on which Wi-Fi SSIDs the device can connect to. Note that this does not affect which networks can be configured on the device. Supported on company-owned devices running Android 13 and above."}}, "type": "object"}, "DeviceRadioState": {"description": "Controls for device radio settings.", "id": "DeviceRadioState", "properties": {"airplaneModeState": {"description": "Controls whether airplane mode can be toggled by the user or not.", "enum": ["AIRPLANE_MODE_STATE_UNSPECIFIED", "AIRPLANE_MODE_USER_CHOICE", "AIRPLANE_MODE_DISABLED"], "enumDescriptions": ["Unspecified. Defaults to AIRPLANE_MODE_USER_CHOICE.", "The user is allowed to toggle airplane mode on or off.", "Airplane mode is disabled. The user is not allowed to toggle airplane mode on. A NonComplianceDetail with API_LEVEL is reported if the Android version is less than 9."], "type": "string"}, "cellularTwoGState": {"description": "Controls whether cellular 2G setting can be toggled by the user or not.", "enum": ["CELLULAR_TWO_G_STATE_UNSPECIFIED", "CELLULAR_TWO_G_USER_CHOICE", "CELLULAR_TWO_G_DISABLED"], "enumDescriptions": ["Unspecified. Defaults to CELLULAR_TWO_G_USER_CHOICE.", "The user is allowed to toggle cellular 2G on or off.", "Cellular 2G is disabled. The user is not allowed to toggle cellular 2G on via settings. A NonComplianceDetail with API_LEVEL is reported if the Android version is less than 14."], "type": "string"}, "minimumWifiSecurityLevel": {"description": "The minimum required security level of Wi-Fi networks that the device can connect to.", "enum": ["MINIMUM_WIFI_SECURITY_LEVEL_UNSPECIFIED", "OPEN_NETWORK_SECURITY", "PERSONAL_NETWORK_SECURITY", "ENTERPRISE_NETWORK_SECURITY", "ENTERPRISE_BIT192_NETWORK_SECURITY"], "enumDescriptions": ["Defaults to OPEN_NETWORK_SECURITY, which means the device will be able to connect to all types of Wi-Fi networks.", "The device will be able to connect to all types of Wi-Fi networks.", "A personal network such as WEP, WPA2-PSK is the minimum required security. The device will not be able to connect to open wifi networks. This is stricter than OPEN_NETWORK_SECURITY. A NonComplianceDetail with API_LEVEL is reported if the Android version is less than 13.", "An enterprise EAP network is the minimum required security level. The device will not be able to connect to Wi-Fi network below this security level. This is stricter than PERSONAL_NETWORK_SECURITY. A NonComplianceDetail with API_LEVEL is reported if the Android version is less than 13.", "A 192-bit enterprise network is the minimum required security level. The device will not be able to connect to Wi-Fi network below this security level. This is stricter than ENTERPRISE_NETWORK_SECURITY. A NonComplianceDetail with API_LEVEL is reported if the Android version is less than 13."], "type": "string"}, "ultraWidebandState": {"description": "Controls the state of the ultra wideband setting and whether the user can toggle it on or off.", "enum": ["ULTRA_WIDEBAND_STATE_UNSPECIFIED", "ULTRA_WIDEBAND_USER_CHOICE", "ULTRA_WIDEBAND_DISABLED"], "enumDescriptions": ["Unspecified. Defaults to ULTRA_WIDEBAND_USER_CHOICE.", "The user is allowed to toggle ultra wideband on or off.", "Ultra wideband is disabled. The user is not allowed to toggle ultra wideband on via settings. A NonComplianceDetail with API_LEVEL is reported if the Android version is less than 14."], "type": "string"}, "wifiState": {"description": "Controls current state of Wi-Fi and if user can change its state.", "enum": ["WIFI_STATE_UNSPECIFIED", "WIFI_STATE_USER_CHOICE", "WIFI_ENABLED", "WIFI_DISABLED"], "enumDescriptions": ["Unspecified. Defaults to WIFI_STATE_USER_CHOICE", "User is allowed to enable/disable Wi-Fi.", "Wi-Fi is on and the user is not allowed to turn it off. A NonComplianceDetail with API_LEVEL is reported if the Android version is less than 13.", "Wi-Fi is off and the user is not allowed to turn it on. A NonComplianceDetail with API_LEVEL is reported if the Android version is less than 13."], "type": "string"}}, "type": "object"}, "DeviceSettings": {"description": "Information about security related device settings on device.", "id": "DeviceSettings", "properties": {"adbEnabled": {"description": "Whether ADB (https://developer.android.com/studio/command-line/adb.html) is enabled on the device.", "type": "boolean"}, "developmentSettingsEnabled": {"description": "Whether developer mode is enabled on the device.", "type": "boolean"}, "encryptionStatus": {"description": "Encryption status from DevicePolicyManager.", "enum": ["ENCRYPTION_STATUS_UNSPECIFIED", "UNSUPPORTED", "INACTIVE", "ACTIVATING", "ACTIVE", "ACTIVE_DEFAULT_KEY", "ACTIVE_PER_USER"], "enumDescriptions": ["Unspecified. No device should have this type.", "Encryption is not supported by the device.", "Encryption is supported by the device, but is not currently active.", "Encryption is not currently active, but is currently being activated.", "Encryption is active.", "Encryption is active, but an encryption key is not set by the user.", "Encryption is active, and the encryption key is tied to the user profile."], "type": "string"}, "isDeviceSecure": {"description": "Whether the device is secured with PIN/password.", "type": "boolean"}, "isEncrypted": {"description": "Whether the storage encryption is enabled.", "type": "boolean"}, "unknownSourcesEnabled": {"description": "Whether installing apps from unknown sources is enabled.", "type": "boolean"}, "verifyAppsEnabled": {"description": "Whether Google Play Protect verification (https://support.google.com/accounts/answer/2812853) is enforced on the device.", "type": "boolean"}}, "type": "object"}, "Display": {"description": "Device display information.", "id": "Display", "properties": {"density": {"description": "Display density expressed as dots-per-inch.", "format": "int32", "type": "integer"}, "displayId": {"description": "Unique display id.", "format": "int32", "type": "integer"}, "height": {"description": "Display height in pixels.", "format": "int32", "type": "integer"}, "name": {"description": "Name of the display.", "type": "string"}, "refreshRate": {"description": "Refresh rate of the display in frames per second.", "format": "int32", "type": "integer"}, "state": {"description": "State of the display.", "enum": ["DISPLAY_STATE_UNSPECIFIED", "OFF", "ON", "DOZE", "SUSPENDED"], "enumDescriptions": ["This value is disallowed.", "Display is off.", "Display is on.", "Display is dozing in a low power state", "Display is dozing in a suspended low power state."], "type": "string"}, "width": {"description": "Display width in pixels.", "format": "int32", "type": "integer"}}, "type": "object"}, "DisplaySettings": {"description": "Controls for the display settings.", "id": "DisplaySettings", "properties": {"screenBrightnessSettings": {"$ref": "ScreenBrightnessSettings", "description": "Optional. Controls the screen brightness settings."}, "screenTimeoutSettings": {"$ref": "ScreenTimeoutSettings", "description": "Optional. Controls the screen timeout settings."}}, "type": "object"}, "DnsEvent": {"description": "A DNS lookup event was initiated through the standard network stack.", "id": "DnsEvent", "properties": {"hostname": {"description": "The hostname that was looked up.", "type": "string"}, "ipAddresses": {"description": "The (possibly truncated) list of the IP addresses returned for DNS lookup (max 10 IPv4 or IPv6 addresses).", "items": {"type": "string"}, "type": "array"}, "packageName": {"description": "The package name of the UID that performed the DNS lookup.", "type": "string"}, "totalIpAddressesReturned": {"description": "The number of IP addresses returned from the DNS lookup event. May be higher than the amount of ip_addresses if there were too many addresses to log.", "format": "int64", "type": "string"}}, "type": "object"}, "DpcMigrationInfo": {"description": "Information related to whether this device was migrated from being managed by another Device Policy Controller (DPC).", "id": "DpcMigrationInfo", "properties": {"additionalData": {"description": "Output only. If this device was migrated from another DPC, the additionalData field of the migration token is populated here.", "readOnly": true, "type": "string"}, "previousDpc": {"description": "Output only. If this device was migrated from another DPC, this is its package name. Not populated otherwise.", "readOnly": true, "type": "string"}}, "type": "object"}, "Eid": {"description": "EID information for each eUICC chip.", "id": "<PERSON><PERSON>", "properties": {"eid": {"description": "Output only. The EID", "readOnly": true, "type": "string"}}, "type": "object"}, "EidInfo": {"description": "Information related to the EIDs of the device.", "id": "EidInfo", "properties": {"eids": {"description": "Output only. EID information for each eUICC chip.", "items": {"$ref": "<PERSON><PERSON>"}, "readOnly": true, "type": "array"}}, "type": "object"}, "Empty": {"description": "A generic empty message that you can re-use to avoid defining duplicated empty messages in your APIs. A typical example is to use it as the request or the response type of an API method. For instance: service Foo { rpc Bar(google.protobuf.Empty) returns (google.protobuf.Empty); } ", "id": "Empty", "properties": {}, "type": "object"}, "EnrollmentCompleteEvent": {"description": "Represents that the device has completed enrollment. User should be in the launcher at this point, device at this point will be compliant and all setup steps have been completed. Intentionally empty.", "id": "EnrollmentCompleteEvent", "properties": {}, "type": "object"}, "EnrollmentToken": {"description": "An enrollment token.", "id": "EnrollmentToken", "properties": {"additionalData": {"description": "Optional, arbitrary data associated with the enrollment token. This could contain, for example, the ID of an org unit the device is assigned to after enrollment. After a device enrolls with the token, this data will be exposed in the enrollment_token_data field of the Device resource. The data must be 1024 characters or less; otherwise, the creation request will fail.", "type": "string"}, "allowPersonalUsage": {"description": "Controls whether personal usage is allowed on a device provisioned with this enrollment token.For company-owned devices: Enabling personal usage allows the user to set up a work profile on the device. Disabling personal usage requires the user provision the device as a fully managed device.For personally-owned devices: Enabling personal usage allows the user to set up a work profile on the device. Disabling personal usage will prevent the device from provisioning. Personal usage cannot be disabled on personally-owned device.", "enum": ["ALLOW_PERSONAL_USAGE_UNSPECIFIED", "PERSONAL_USAGE_ALLOWED", "PERSONAL_USAGE_DISALLOWED", "PERSONAL_USAGE_DISALLOWED_USERLESS"], "enumDescriptions": ["Personal usage restriction is not specified", "Personal usage is allowed", "Personal usage is disallowed", "Device is not associated with a single user, and thus both personal usage and corporate identity authentication are not expected. Important: This setting is mandatory for dedicated device enrollment and it is a breaking change. This change needs to be implemented before January 2025.For additional details see the dedicated device provisioning guide (https://developers.google.com/android/management/provision-device#company-owned_devices_for_work_use_only). "], "type": "string"}, "duration": {"description": "The length of time the enrollment token is valid, ranging from 1 minute to Durations.MAX_VALUE (https://developers.google.com/protocol-buffers/docs/reference/java/com/google/protobuf/util/Durations.html#MAX_VALUE), approximately 10,000 years. If not specified, the default duration is 1 hour. Please note that if requested duration causes the resulting expiration_timestamp to exceed Timestamps.MAX_VALUE (https://developers.google.com/protocol-buffers/docs/reference/java/com/google/protobuf/util/Timestamps.html#MAX_VALUE), then expiration_timestamp is coerced to Timestamps.MAX_VALUE.", "format": "google-duration", "type": "string"}, "expirationTimestamp": {"description": "The expiration time of the token. This is a read-only field generated by the server.", "format": "google-datetime", "type": "string"}, "name": {"description": "The name of the enrollment token, which is generated by the server during creation, in the form enterprises/{enterpriseId}/enrollmentTokens/{enrollmentTokenId}.", "type": "string"}, "oneTimeOnly": {"description": "Whether the enrollment token is for one time use only. If the flag is set to true, only one device can use it for registration.", "type": "boolean"}, "policyName": {"description": "The name of the policy initially applied to the enrolled device, in the form enterprises/{enterpriseId}/policies/{policyId}. If not specified, the policy_name for the device’s user is applied. If user_name is also not specified, enterprises/{enterpriseId}/policies/default is applied by default. When updating this field, you can specify only the policyId as long as the policyId doesn’t contain any slashes. The rest of the policy name will be inferred.", "type": "string"}, "qrCode": {"description": "A JSON string whose UTF-8 representation can be used to generate a QR code to enroll a device with this enrollment token. To enroll a device using NFC, the NFC record must contain a serialized java.util.Properties representation of the properties in the JSON.", "type": "string"}, "user": {"$ref": "User", "deprecated": true, "description": "This field is deprecated and the value is ignored."}, "value": {"description": "The token value that's passed to the device and authorizes the device to enroll. This is a read-only field generated by the server.", "type": "string"}}, "type": "object"}, "Enterprise": {"description": "The configuration applied to an enterprise.", "id": "Enterprise", "properties": {"appAutoApprovalEnabled": {"deprecated": true, "description": "Deprecated and unused.", "type": "boolean"}, "contactInfo": {"$ref": "ContactInfo", "description": "The enterprise contact info of an EMM-managed enterprise."}, "enabledNotificationTypes": {"description": "The types of Google Pub/Sub notifications enabled for the enterprise.", "items": {"enum": ["NOTIFICATION_TYPE_UNSPECIFIED", "ENROLLMENT", "COMPLIANCE_REPORT", "STATUS_REPORT", "COMMAND", "USAGE_LOGS", "ENTERPRISE_UPGRADE"], "enumDeprecated": [false, false, true, false, false, false, false], "enumDescriptions": ["This value is ignored.", "A notification sent when a device enrolls.", "Deprecated.", "A notification sent when a device issues a status report.", "A notification sent when a device command has completed.", "A notification sent when device sends BatchUsageLogEvents.", "A notification sent for an enterprise upgrade. An enterprise upgrade is a process that upgrades a managed Google Play Accounts enterprise to a managed Google domain."], "type": "string"}, "type": "array"}, "enterpriseDisplayName": {"description": "The name of the enterprise displayed to users. This field has a maximum length of 100 characters.", "type": "string"}, "enterpriseType": {"description": "Output only. The type of the enterprise.", "enum": ["ENTERPRISE_TYPE_UNSPECIFIED", "MANAGED_GOOGLE_DOMAIN", "MANAGED_GOOGLE_PLAY_ACCOUNTS_ENTERPRISE"], "enumDescriptions": ["This value is not used.", "The enterprise belongs to a managed Google domain (https://developers.google.com/android/work/terminology#managed_google_domain).", "The enterprise is a managed Google Play Accounts enterprise (https://developers.google.com/android/work/terminology#managed_google_play_accounts_enterprise)."], "readOnly": true, "type": "string"}, "googleAuthenticationSettings": {"$ref": "GoogleAuthenticationSettings", "description": "Settings for Google-provided user authentication."}, "logo": {"$ref": "ExternalData", "description": "An image displayed as a logo during device provisioning. Supported types are: image/bmp, image/gif, image/x-ico, image/jpeg, image/png, image/webp, image/vnd.wap.wbmp, image/x-adobe-dng."}, "managedGoogleDomainType": {"description": "Output only. The type of managed Google domain.", "enum": ["MANAGED_GOOGLE_DOMAIN_TYPE_UNSPECIFIED", "TYPE_TEAM", "TYPE_DOMAIN"], "enumDescriptions": ["The managed Google domain type is not specified.", "The managed Google domain is an email-verified team.", "The managed Google domain is domain-verified."], "readOnly": true, "type": "string"}, "managedGooglePlayAccountsEnterpriseType": {"description": "Output only. The type of a managed Google Play Accounts enterprise.", "enum": ["MANAGED_GOOGLE_PLAY_ACCOUNTS_ENTERPRISE_TYPE_UNSPECIFIED", "CUSTOMER_MANAGED", "EMM_MANAGED"], "enumDescriptions": ["The managed Google Play Accounts enterprise type is not specified.", "The enterprise is customer-managed", "The enterprise is EMM-managed (deprecated)."], "readOnly": true, "type": "string"}, "name": {"description": "The name of the enterprise which is generated by the server during creation, in the form enterprises/{enterpriseId}.", "type": "string"}, "primaryColor": {"description": "A color in RGB format that indicates the predominant color to display in the device management app UI. The color components are stored as follows: (red << 16) | (green << 8) | blue, where the value of each component is between 0 and 255, inclusive.", "format": "int32", "type": "integer"}, "pubsubTopic": {"description": "The topic which Pub/Sub notifications are published to, in the form projects/{project}/topics/{topic}. This field is only required if Pub/Sub notifications are enabled.", "type": "string"}, "signinDetails": {"description": "Sign-in details of the enterprise.", "items": {"$ref": "SigninDetail"}, "type": "array"}, "termsAndConditions": {"description": "Terms and conditions that must be accepted when provisioning a device for this enterprise. A page of terms is generated for each value in this list.", "items": {"$ref": "TermsAndConditions"}, "type": "array"}}, "type": "object"}, "EnterpriseUpgradeEvent": {"description": "An event sent for an enterprise upgrade. An enterprise upgrade is a process that upgrades a managed Google Play Accounts enterprise to a managed Google domain.", "id": "EnterpriseUpgradeEvent", "properties": {"enterprise": {"description": "The name of upgraded enterprise in the format \"enterprises/{enterprise}\"", "type": "string"}, "upgradeState": {"description": "Output only. The upgrade state of the enterprise.", "enum": ["UPGRADE_STATE_UNSPECIFIED", "UPGRADE_STATE_SUCCEEDED"], "enumDescriptions": ["Unspecified. This value is not used.", "The upgrade has succeeded."], "readOnly": true, "type": "string"}}, "type": "object"}, "EsimCommandStatus": {"description": "Status and error details (if present) of an ADD_ESIM or REMOVE_ESIM command.", "id": "EsimCommandStatus", "properties": {"esimInfo": {"$ref": "EsimInfo", "description": "Output only. Information about the eSIM added or removed. This is populated only when the eSIM operation status is SUCCESS.", "readOnly": true}, "internalErrorDetails": {"$ref": "InternalErrorDetails", "description": "Output only. Details of the error if the status is set to INTERNAL_ERROR.", "readOnly": true}, "status": {"description": "Output only. Status of an ADD_ESIM or REMOVE_ESIM command.", "enum": ["STATUS_UNSPECIFIED", "SUCCESS", "IN_PROGRESS", "PENDING_USER_ACTION", "ERROR_SETUP_IN_PROGRESS", "ERROR_USER_DENIED", "INTERNAL_ERROR", "ERROR_ICC_ID_NOT_FOUND", "ERROR_MULTIPLE_ACTIVE_ESIMS_NO_AVAILABLE_SLOT"], "enumDescriptions": ["Unspecified. This value is not used.", "The eSIM operation was successfully performed on the device.", "The eSIM operation is in progress.", "The user needs to take an action for the eSIM operation to proceed.", "The eSIM operation cannot be executed when setup is in progress.", "The user has denied the eSIM operation.", "An error has occurred while trying to add or remove the eSIM on the device, see internal_error_details.", "For a REMOVE_ESIM command, the iccId of the eSIM to be removed was not found on the device. This could either mean the eSIM does not belong to the enterprise or the eSIM corresponding to the iccId is not present on the device.", "The ADD_ESIM command failed when attempting to add a new eSIM with its activation state set to ACTIVATED since multiple eSIM slots on the device contain active eSIM profiles and there is no free eSIM slot available. To resolve this, the new eSIM can be added with its activation state as NOT_ACTIVATED for later manual activation, or the user must first deactivate an existing active eSIM for the operation to proceed."], "readOnly": true, "type": "string"}}, "type": "object"}, "EsimInfo": {"description": "Details of the eSIM added or removed.", "id": "EsimInfo", "properties": {"iccId": {"description": "Output only. ICC ID of the eSIM.", "readOnly": true, "type": "string"}}, "type": "object"}, "EuiccChipInfo": {"description": "Information related to the eUICC chip.", "id": "EuiccChipInfo", "properties": {"eid": {"description": "Output only. The Embedded Identity Document (EID) that identifies the eUICC chip for each eUICC chip on the device. This is available on company owned devices running Android 13 and above.", "readOnly": true, "type": "string"}}, "type": "object"}, "ExtensionConfig": {"description": "Configuration to enable an app as an extension app, with the capability of interacting with Android Device Policy offline. For Android versions 11 and above, extension apps are exempt from battery restrictions so will not be placed into the restricted App Standby Bucket (https://developer.android.com/topic/performance/appstandby#restricted-bucket). Extensions apps are also protected against users clearing their data or force-closing the application, although admins can continue to use the clear app data command on extension apps if needed for Android 11 and above.", "id": "ExtensionConfig", "properties": {"notificationReceiver": {"description": "Fully qualified class name of the receiver service class for Android Device Policy to notify the extension app of any local command status updates. The service must be exported in the extension app's AndroidManifest.xml and extend NotificationReceiverService (https://developers.google.com/android/management/reference/amapi/com/google/android/managementapi/notification/NotificationReceiverService) (see Integrate with the AMAPI SDK (https://developers.google.com/android/management/sdk-integration) guide for more details).", "type": "string"}, "signingKeyFingerprintsSha256": {"description": "Hex-encoded SHA-256 hashes of the signing key certificates of the extension app. Only hexadecimal string representations of 64 characters are valid.The signing key certificate fingerprints are always obtained from the Play Store and this field is used to provide additional signing key certificate fingerprints. However, if the application is not available on the Play Store, this field needs to be set. A NonComplianceDetail with INVALID_VALUE is reported if this field is not set when the application is not available on the Play Store.The signing key certificate fingerprint of the extension app on the device must match one of the signing key certificate fingerprints obtained from the Play Store or the ones provided in this field for the app to be able to communicate with Android Device Policy.In production use cases, it is recommended to leave this empty.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "ExternalData": {"description": "Data hosted at an external location. The data is to be downloaded by Android Device Policy and verified against the hash.", "id": "ExternalData", "properties": {"sha256Hash": {"description": "The base-64 encoded SHA-256 hash of the content hosted at url. If the content doesn't match this hash, Android Device Policy won't use the data.", "type": "string"}, "url": {"description": "The absolute URL to the data, which must use either the http or https scheme. Android Device Policy doesn't provide any credentials in the GET request, so the URL must be publicly accessible. Including a long, random component in the URL may be used to prevent attackers from discovering the URL.", "type": "string"}}, "type": "object"}, "FilePulledEvent": {"description": "A file was downloaded from the device.", "id": "FilePulledEvent", "properties": {"filePath": {"description": "The path of the file being pulled.", "type": "string"}}, "type": "object"}, "FilePushedEvent": {"description": "A file was uploaded onto the device.", "id": "FilePushedEvent", "properties": {"filePath": {"description": "The path of the file being pushed.", "type": "string"}}, "type": "object"}, "FreezePeriod": {"description": "A system freeze period. When a device’s clock is within the freeze period, all incoming system updates (including security patches) are blocked and won’t be installed.When the device is outside any set freeze periods, the normal policy behavior (automatic, windowed, or postponed) applies.Leap years are ignored in freeze period calculations, in particular: If Feb. 29th is set as the start or end date of a freeze period, the freeze period will start or end on Feb. 28th instead. When a device’s system clock reads Feb. 29th, it’s treated as Feb. 28th. When calculating the number of days in a freeze period or the time between two freeze periods, Feb. 29th is ignored and not counted as a day.Note: For Freeze Periods to take effect, SystemUpdateType cannot be specified as SYSTEM_UPDATE_TYPE_UNSPECIFIED, because freeze periods require a defined policy to be specified.", "id": "FreezePeriod", "properties": {"endDate": {"$ref": "Date", "description": "The end date (inclusive) of the freeze period. Must be no later than 90 days from the start date. If the end date is earlier than the start date, the freeze period is considered wrapping year-end. Note: day and month must be set. year should not be set as it is not used. For example, {\"month\": 1,\"date\": 30}."}, "startDate": {"$ref": "Date", "description": "The start date (inclusive) of the freeze period. Note: day and month must be set. year should not be set as it is not used. For example, {\"month\": 1,\"date\": 30}."}}, "type": "object"}, "GenerateEnterpriseUpgradeUrlRequest": {"description": "Request message for generating a URL to upgrade an existing managed Google Play Accounts enterprise to a managed Google domain.Note: This feature is not generally available.", "id": "GenerateEnterpriseUpgradeUrlRequest", "properties": {"adminEmail": {"description": "Optional. Email address used to prefill the admin field of the enterprise signup form as part of the upgrade process. This value is a hint only and can be altered by the user. Personal email addresses are not allowed. If allowedDomains is non-empty then this must belong to one of the allowedDomains.", "type": "string"}, "allowedDomains": {"description": "Optional. A list of domains that are permitted for the admin email. The IT admin cannot enter an email address with a domain name that is not in this list. Subdomains of domains in this list are not allowed but can be allowed by adding a second entry which has *. prefixed to the domain name (e.g. *.example.com). If the field is not present or is an empty list then the IT admin is free to use any valid domain name. Personal email domains are not allowed.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "GenerateEnterpriseUpgradeUrlResponse": {"description": "Response message for generating a URL to upgrade an existing managed Google Play Accounts enterprise to a managed Google domain.Note: This feature is not generally available.", "id": "GenerateEnterpriseUpgradeUrlResponse", "properties": {"url": {"description": "A URL for an enterprise admin to upgrade their enterprise. The page can't be rendered in an iframe.", "type": "string"}}, "type": "object"}, "GoogleAuthenticationSettings": {"description": "Contains settings for Google-provided user authentication.", "id": "GoogleAuthenticationSettings", "properties": {"googleAuthenticationRequired": {"description": "Output only. Whether users need to be authenticated by Google during the enrollment process. IT admin can specify if Google authentication is enabled for the enterprise for knowledge worker devices. This value can be set only via the Google Admin Console. Google authentication can be used with signin_url In the case where Google authentication is required and a signin_url is specified, Google authentication will be launched before signin_url.", "enum": ["GOOGLE_AUTHENTICATION_REQUIRED_UNSPECIFIED", "NOT_REQUIRED", "REQUIRED"], "enumDescriptions": ["This value is not used.", "Google authentication is not required.", "User is required to be successfully authenticated by Google."], "readOnly": true, "type": "string"}}, "type": "object"}, "HardwareInfo": {"description": "Information about device hardware. The fields related to temperature thresholds are only available if hardwareStatusEnabled is true in the device's policy.", "id": "HardwareInfo", "properties": {"batteryShutdownTemperatures": {"description": "Battery shutdown temperature thresholds in Celsius for each battery on the device.", "items": {"format": "float", "type": "number"}, "type": "array"}, "batteryThrottlingTemperatures": {"description": "Battery throttling temperature thresholds in Celsius for each battery on the device.", "items": {"format": "float", "type": "number"}, "type": "array"}, "brand": {"description": "Brand of the device. For example, Google.", "type": "string"}, "cpuShutdownTemperatures": {"description": "CPU shutdown temperature thresholds in Celsius for each CPU on the device.", "items": {"format": "float", "type": "number"}, "type": "array"}, "cpuThrottlingTemperatures": {"description": "CPU throttling temperature thresholds in Celsius for each CPU on the device.", "items": {"format": "float", "type": "number"}, "type": "array"}, "deviceBasebandVersion": {"description": "Baseband version. For example, MDM9625_104662.22.05.34p.", "type": "string"}, "enterpriseSpecificId": {"description": "Output only. ID that uniquely identifies a personally-owned device in a particular organization. On the same physical device when enrolled with the same organization, this ID persists across setups and even factory resets. This ID is available on personally-owned devices with a work profile on devices running Android 12 and above.", "readOnly": true, "type": "string"}, "euiccChipInfo": {"description": "Output only. Information related to the eUICC chip.", "items": {"$ref": "EuiccChipInfo"}, "readOnly": true, "type": "array"}, "gpuShutdownTemperatures": {"description": "GPU shutdown temperature thresholds in Celsius for each GPU on the device.", "items": {"format": "float", "type": "number"}, "type": "array"}, "gpuThrottlingTemperatures": {"description": "GPU throttling temperature thresholds in Celsius for each GPU on the device.", "items": {"format": "float", "type": "number"}, "type": "array"}, "hardware": {"description": "Name of the hardware. For example, <PERSON><PERSON>.", "type": "string"}, "manufacturer": {"description": "Manufacturer. For example, Motorola.", "type": "string"}, "model": {"description": "The model of the device. For example, Asus Nexus 7.", "type": "string"}, "serialNumber": {"description": "The device serial number.", "type": "string"}, "skinShutdownTemperatures": {"description": "Device skin shutdown temperature thresholds in Celsius.", "items": {"format": "float", "type": "number"}, "type": "array"}, "skinThrottlingTemperatures": {"description": "Device skin throttling temperature thresholds in Celsius.", "items": {"format": "float", "type": "number"}, "type": "array"}}, "type": "object"}, "HardwareStatus": {"description": "Hardware status. Temperatures may be compared to the temperature thresholds available in hardwareInfo to determine hardware health.", "id": "HardwareStatus", "properties": {"batteryTemperatures": {"description": "Current battery temperatures in Celsius for each battery on the device.", "items": {"format": "float", "type": "number"}, "type": "array"}, "cpuTemperatures": {"description": "Current CPU temperatures in Celsius for each CPU on the device.", "items": {"format": "float", "type": "number"}, "type": "array"}, "cpuUsages": {"description": "CPU usages in percentage for each core available on the device. Usage is 0 for each unplugged core. Empty array implies that CPU usage is not supported in the system.", "items": {"format": "float", "type": "number"}, "type": "array"}, "createTime": {"description": "The time the measurements were taken.", "format": "google-datetime", "type": "string"}, "fanSpeeds": {"description": "Fan speeds in RPM for each fan on the device. Empty array means that there are no fans or fan speed is not supported on the system.", "items": {"format": "float", "type": "number"}, "type": "array"}, "gpuTemperatures": {"description": "Current GPU temperatures in Celsius for each GPU on the device.", "items": {"format": "float", "type": "number"}, "type": "array"}, "skinTemperatures": {"description": "Current device skin temperatures in Celsius.", "items": {"format": "float", "type": "number"}, "type": "array"}}, "type": "object"}, "InstallConstraint": {"description": "Amongst apps with InstallType set to: FORCE_INSTALLED PREINSTALLEDthis defines a set of restrictions for the app installation. At least one of the fields must be set. When multiple fields are set, then all the constraints need to be satisfied for the app to be installed.", "id": "InstallConstraint", "properties": {"chargingConstraint": {"description": "Optional. Charging constraint.", "enum": ["CHARGING_CONSTRAINT_UNSPECIFIED", "CHARGING_NOT_REQUIRED", "INSTALL_ONLY_WHEN_CHARGING"], "enumDescriptions": ["Unspecified. Default to CHARGING_NOT_REQUIRED.", "<PERSON><PERSON> doesn't have to be charging.", "Device has to be charging."], "type": "string"}, "deviceIdleConstraint": {"description": "Optional. Device idle constraint.", "enum": ["DEVICE_IDLE_CONSTRAINT_UNSPECIFIED", "DEVICE_IDLE_NOT_REQUIRED", "INSTALL_ONLY_WHEN_DEVICE_IDLE"], "enumDescriptions": ["Unspecified. Default to DEVICE_IDLE_NOT_REQUIRED.", "Device doesn't have to be idle, app can be installed while the user is interacting with the device.", "Device has to be idle."], "type": "string"}, "networkTypeConstraint": {"description": "Optional. Network type constraint.", "enum": ["NETWORK_TYPE_CONSTRAINT_UNSPECIFIED", "INSTALL_ON_ANY_NETWORK", "INSTALL_ONLY_ON_UNMETERED_NETWORK"], "enumDescriptions": ["Unspecified. Default to INSTALL_ON_ANY_NETWORK.", "Any active networks (Wi-Fi, cellular, etc.).", "Any unmetered network (e.g. Wi-FI)."], "type": "string"}}, "type": "object"}, "InternalErrorDetails": {"description": "Internal error details if present for the ADD_ESIM or REMOVE_ESIM command.", "id": "InternalErrorDetails", "properties": {"errorCode": {"description": "Output only. Integer representation of the error code as specified here (https://developer.android.com/reference/android/telephony/euicc/EuiccManager#EXTRA_EMBEDDED_SUBSCRIPTION_DETAILED_CODE). See also, OPERATION_SMDX_SUBJECT_REASON_CODE. See error_code_detail for more details.", "format": "int64", "readOnly": true, "type": "string"}, "errorCodeDetail": {"description": "Output only. The error code detail corresponding to the error_code.", "enum": ["ERROR_CODE_DETAIL_UNSPECIFIED", "ERROR_TIME_OUT", "ERROR_EUICC_MISSING", "ERROR_UNSUPPORTED_VERSION", "ERROR_ADDRESS_MISSING", "ERROR_INVALID_CONFIRMATION_CODE", "ERROR_CERTIFICATE_ERROR", "ERROR_NO_PROFILES_AVAILABLE", "ERROR_CONNECTION_ERROR", "ERROR_INVALID_RESPONSE", "ERROR_CARRIER_LOCKED", "ERROR_DISALLOWED_BY_PPR", "ERROR_INVALID_ACTIVATION_CODE", "ERROR_INCOMPATIBLE_CARRIER", "ERROR_OPERATION_BUSY", "ERROR_INSTALL_PROFILE", "ERROR_EUICC_INSUFFICIENT_MEMORY", "ERROR_INVALID_PORT", "ERROR_SIM_MISSING"], "enumDescriptions": ["Error code detail is unspecified. The error_code is not recognized by Android Management API. However, see error_code", "See EuiccManager.ERROR_TIME_OUT (https://developer.android.com/reference/android/telephony/euicc/EuiccManager#ERROR_TIME_OUT) for details.", "See EuiccManager.ERROR_EUICC_MISSING (https://developer.android.com/reference/android/telephony/euicc/EuiccManager#ERROR_EUICC_MISSING) for details.", "See EuiccManager.ERROR_UNSUPPORTED_VERSION (https://developer.android.com/reference/android/telephony/euicc/EuiccManager#ERROR_UNSUPPORTED_VERSION) for details.", "See EuiccManager.ERROR_ADDRESS_MISSING (https://developer.android.com/reference/android/telephony/euicc/EuiccManager#ERROR_ADDRESS_MISSING) for details.", "See EuiccManager.ERROR_INVALID_CONFIRMATION_CODE (https://developer.android.com/reference/android/telephony/euicc/EuiccManager#ERROR_INVALID_CONFIRMATION_CODE) for details.", "See EuiccManager.ERROR_CERTIFICATE_ERROR (https://developer.android.com/reference/android/telephony/euicc/EuiccManager#ERROR_CERTIFICATE_ERROR) for details.", "See EuiccManager.ERROR_NO_PROFILES_AVAILABLE (https://developer.android.com/reference/android/telephony/euicc/EuiccManager#ERROR_NO_PROFILES_AVAILABLE) for details.", "See EuiccManager.ERROR_CONNECTION_ERROR (https://developer.android.com/reference/android/telephony/euicc/EuiccManager#ERROR_CONNECTION_ERROR) for details.", "See EuiccManager.ERROR_INVALID_RESPONSE (https://developer.android.com/reference/android/telephony/euicc/EuiccManager#ERROR_INVALID_RESPONSE) for details.", "See EuiccManager.ERROR_CARRIER_LOCKED (https://developer.android.com/reference/android/telephony/euicc/EuiccManager#ERROR_CARRIER_LOCKED) for details.", "See EuiccManager.ERROR_DISALLOWED_BY_PPR (https://developer.android.com/reference/android/telephony/euicc/EuiccManager#ERROR_DISALLOWED_BY_PPR) for details.", "See EuiccManager.ERROR_INVALID_ACTIVATION_CODE (https://developer.android.com/reference/android/telephony/euicc/EuiccManager#ERROR_INVALID_ACTIVATION_CODE) for details.", "See EuiccManager.ERROR_INCOMPATIBLE_CARRIER (https://developer.android.com/reference/android/telephony/euicc/EuiccManager#ERROR_INCOMPATIBLE_CARRIER) for details.", "See EuiccManager.ERROR_OPERATION_BUSY (https://developer.android.com/reference/android/telephony/euicc/EuiccManager#ERROR_OPERATION_BUSY) for details.", "See EuiccManager.ERROR_INSTALL_PROFILE (https://developer.android.com/reference/android/telephony/euicc/EuiccManager#ERROR_INSTALL_PROFILE) for details.", "See EuiccManager.ERROR_EUICC_INSUFFICIENT_MEMORY (https://developer.android.com/reference/android/telephony/euicc/EuiccManager#ERROR_EUICC_INSUFFICIENT_MEMORY) for details.", "See EuiccManager.ERROR_INVALID_PORT (https://developer.android.com/reference/android/telephony/euicc/EuiccManager#ERROR_INVALID_PORT) for details.", "See EuiccManager.ERROR_SIM_MISSING (https://developer.android.com/reference/android/telephony/euicc/EuiccManager#ERROR_SIM_MISSING) for details."], "readOnly": true, "type": "string"}, "operationCode": {"description": "Output only. Integer representation of the operation code as specified here (https://developer.android.com/reference/android/telephony/euicc/EuiccManager#EXTRA_EMBEDDED_SUBSCRIPTION_DETAILED_CODE). See operation_code_detail for more details.", "format": "int64", "readOnly": true, "type": "string"}, "operationCodeDetail": {"description": "Output only. The operation code detail corresponding to the operation_code.", "enum": ["OPERATION_CODE_DETAIL_UNSPECIFIED", "OPERATION_SYSTEM", "OPERATION_SIM_SLOT", "OPERATION_EUICC_CARD", "OPERATION_SMDX", "OPERATION_SWITCH", "OPERATION_DOWNLOAD", "OPERATION_METADATA", "OPERATION_EUICC_GSMA", "OPERATION_APDU", "OPERATION_SMDX_SUBJECT_REASON_CODE", "OPERATION_HTTP"], "enumDescriptions": ["Operation code detail is unspecified. The operation_code is not recognized by Android Management API. However, see operation_code.", "See EuiccManager.OPERATION_SYSTEM (https://developer.android.com/reference/android/telephony/euicc/EuiccManager#OPERATION_SYSTEM) for details.", "See EuiccManager.OPERATION_SIM_SLOT (https://developer.android.com/reference/android/telephony/euicc/EuiccManager#OPERATION_SIM_SLOT) for details.", "See EuiccManager.OPERATION_EUICC_CARD (https://developer.android.com/reference/android/telephony/euicc/EuiccManager#OPERATION_EUICC_CARD) for details.", "See EuiccManager.OPERATION_SMDX (https://developer.android.com/reference/android/telephony/euicc/EuiccManager#OPERATION_SMDX) for details.", "See EuiccManager.OPERATION_SWITCH (https://developer.android.com/reference/android/telephony/euicc/EuiccManager#OPERATION_SWITCH) for details.", "See EuiccManager.OPERATION_DOWNLOAD (https://developer.android.com/reference/android/telephony/euicc/EuiccManager#OPERATION_DOWNLOAD) for details.", "See EuiccManager.OPERATION_METADATA (https://developer.android.com/reference/android/telephony/euicc/EuiccManager#OPERATION_METADATA) for details.", "See EuiccManager.OPERATION_EUICC_GSMA (https://developer.android.com/reference/android/telephony/euicc/EuiccManager#OPERATION_EUICC_GSMA) for details.", "See EuiccManager.OPERATION_APDU (https://developer.android.com/reference/android/telephony/euicc/EuiccManager#OPERATION_APDU) for details.", "See EuiccManager.OPERATION_SMDX_SUBJECT_REASON_CODE (https://developer.android.com/reference/android/telephony/euicc/EuiccManager#OPERATION_SMDX_SUBJECT_REASON_CODE) for details. Note that, in this case, error_code is the least significant 3 bytes of the EXTRA_EMBEDDED_SUBSCRIPTION_DETAILED_CODE (https://developer.android.com/reference/android/telephony/euicc/EuiccManager#EXTRA_EMBEDDED_SUBSCRIPTION_DETAILED_CODE) specifying the subject code and the reason code as indicated here (https://developer.android.com/reference/android/telephony/euicc/EuiccManager#OPERATION_SMDX_SUBJECT_REASON_CODE). The most significant byte of the integer is zeroed out. For example, a Subject Code of 8.11.1 and a Reason Code of 5.1 is represented in error_code as 0000 0000 1000 1011 0001 0000 0101 0001 in binary, which is 9113681 in decimal.", "See EuiccManager.OPERATION_HTTP (https://developer.android.com/reference/android/telephony/euicc/EuiccManager#OPERATION_HTTP) for details."], "readOnly": true, "type": "string"}}, "type": "object"}, "IssueCommandResponse": {"description": "Response on issuing a command. This is currently empty as a placeholder.", "id": "IssueCommandResponse", "properties": {}, "type": "object"}, "KeyDestructionEvent": {"description": "A cryptographic key including user installed, admin installed and system maintained private key is removed from the device either by the user or management. This is available device-wide on fully managed devices and within the work profile on organization-owned devices with a work profile.", "id": "KeyDestructionEvent", "properties": {"applicationUid": {"description": "UID of the application which owns the key.", "format": "int32", "type": "integer"}, "keyAlias": {"description": "<PERSON><PERSON> of the key.", "type": "string"}, "success": {"description": "Whether the operation was successful.", "type": "boolean"}}, "type": "object"}, "KeyGeneratedEvent": {"description": "A cryptographic key including user installed, admin installed and system maintained private key is installed on the device either by the user or management.This is available device-wide on fully managed devices and within the work profile on organization-owned devices with a work profile.", "id": "KeyGeneratedEvent", "properties": {"applicationUid": {"description": "UID of the application which generated the key.", "format": "int32", "type": "integer"}, "keyAlias": {"description": "<PERSON><PERSON> of the key.", "type": "string"}, "success": {"description": "Whether the operation was successful.", "type": "boolean"}}, "type": "object"}, "KeyImportEvent": {"description": "A cryptographic key including user installed, admin installed and system maintained private key is imported on the device either by the user or management. This is available device-wide on fully managed devices and within the work profile on organization-owned devices with a work profile.", "id": "KeyImportEvent", "properties": {"applicationUid": {"description": "UID of the application which imported the key", "format": "int32", "type": "integer"}, "keyAlias": {"description": "<PERSON><PERSON> of the key.", "type": "string"}, "success": {"description": "Whether the operation was successful.", "type": "boolean"}}, "type": "object"}, "KeyIntegrityViolationEvent": {"description": "A cryptographic key including user installed, admin installed and system maintained private key is determined to be corrupted due to storage corruption, hardware failure or some OS issue. This is available device-wide on fully managed devices and within the work profile on organization-owned devices with a work profile.", "id": "KeyIntegrityViolationEvent", "properties": {"applicationUid": {"description": "UID of the application which owns the key", "format": "int32", "type": "integer"}, "keyAlias": {"description": "<PERSON><PERSON> of the key.", "type": "string"}}, "type": "object"}, "KeyedAppState": {"description": "Keyed app state reported by the app.", "id": "KeyedAppState", "properties": {"createTime": {"description": "The creation time of the app state on the device.", "format": "google-datetime", "type": "string"}, "data": {"description": "Optionally, a machine-readable value to be read by the EMM. For example, setting values that the admin can choose to query against in the EMM console (e.g. “notify me if the battery_warning data < 10”).", "type": "string"}, "key": {"description": "The key for the app state. Acts as a point of reference for what the app is providing state for. For example, when providing managed configuration feedback, this key could be the managed configuration key.", "type": "string"}, "lastUpdateTime": {"description": "The time the app state was most recently updated.", "format": "google-datetime", "type": "string"}, "message": {"description": "Optionally, a free-form message string to explain the app state. If the state was triggered by a particular value (e.g. a managed configuration value), it should be included in the message.", "type": "string"}, "severity": {"description": "The severity of the app state.", "enum": ["SEVERITY_UNSPECIFIED", "INFO", "ERROR"], "enumDescriptions": ["Unspecified severity level.", "Information severity level.", "Error severity level. This should only be set for genuine error conditions that a management organization needs to take action to fix."], "type": "string"}}, "type": "object"}, "KeyguardDismissAuthAttemptEvent": {"description": "An attempt was made to unlock the device.", "id": "KeyguardDismissAuthAttemptEvent", "properties": {"strongAuthMethodUsed": {"description": "Whether a strong form of authentication (password, PIN, or pattern) was used to unlock device.", "type": "boolean"}, "success": {"description": "Whether the unlock attempt was successful.", "type": "boolean"}}, "type": "object"}, "KeyguardDismissedEvent": {"description": "The keyguard was dismissed. Intentionally empty.", "id": "KeyguardDismissedEvent", "properties": {}, "type": "object"}, "KeyguardSecuredEvent": {"description": "The device was locked either by user or timeout. Intentionally empty.", "id": "KeyguardSecuredEvent", "properties": {}, "type": "object"}, "KioskCustomization": {"description": "Settings controlling the behavior of a device in kiosk mode. To enable kiosk mode, set kioskCustomLauncherEnabled to true or specify an app in the policy with installType KIOSK.", "id": "KioskCustomization", "properties": {"deviceSettings": {"description": "Specifies whether the Settings app is allowed in kiosk mode.", "enum": ["DEVICE_SETTINGS_UNSPECIFIED", "SETTINGS_ACCESS_ALLOWED", "SETTINGS_ACCESS_BLOCKED"], "enumDescriptions": ["Unspecified, defaults to SETTINGS_ACCESS_ALLOWED.", "Access to the Settings app is allowed in kiosk mode.", "Access to the Settings app is not allowed in kiosk mode."], "type": "string"}, "powerButtonActions": {"description": "Sets the behavior of a device in kiosk mode when a user presses and holds (long-presses) the Power button.", "enum": ["POWER_BUTTON_ACTIONS_UNSPECIFIED", "POWER_BUTTON_AVAILABLE", "POWER_BUTTON_BLOCKED"], "enumDescriptions": ["Unspecified, defaults to POWER_BUTTON_AVAILABLE.", "The power menu (e.g. Power off, Restart) is shown when a user long-presses the Power button of a device in kiosk mode.", "The power menu (e.g. Power off, Restart) is not shown when a user long-presses the Power button of a device in kiosk mode. Note: this may prevent users from turning off the device."], "type": "string"}, "statusBar": {"description": "Specifies whether system info and notifications are disabled in kiosk mode.", "enum": ["STATUS_BAR_UNSPECIFIED", "NOTIFICATIONS_AND_SYSTEM_INFO_ENABLED", "NOTIFICATIONS_AND_SYSTEM_INFO_DISABLED", "SYSTEM_INFO_ONLY"], "enumDescriptions": ["Unspecified, defaults to INFO_AND_NOTIFICATIONS_DISABLED.", "System info and notifications are shown on the status bar in kiosk mode.Note: For this policy to take effect, the device's home button must be enabled using kioskCustomization.systemNavigation.", "System info and notifications are disabled in kiosk mode.", "Only system info is shown on the status bar."], "type": "string"}, "systemErrorWarnings": {"description": "Specifies whether system error dialogs for crashed or unresponsive apps are blocked in kiosk mode. When blocked, the system will force-stop the app as if the user chooses the \"close app\" option on the UI.", "enum": ["SYSTEM_ERROR_WARNINGS_UNSPECIFIED", "ERROR_AND_WARNINGS_ENABLED", "ERROR_AND_WARNINGS_MUTED"], "enumDescriptions": ["Unspecified, defaults to ERROR_AND_WARNINGS_MUTED.", "All system error dialogs such as crash and app not responding (ANR) are displayed.", "All system error dialogs, such as crash and app not responding (ANR) are blocked. When blocked, the system force-stops the app as if the user closes the app from the UI."], "type": "string"}, "systemNavigation": {"description": "Specifies which navigation features are enabled (e.g. Home, Overview buttons) in kiosk mode.", "enum": ["SYSTEM_NAVIGATION_UNSPECIFIED", "NAVIGATION_ENABLED", "NAVIGATION_DISABLED", "HOME_BUTTON_ONLY"], "enumDescriptions": ["Unspecified, defaults to NAVIGATION_DISABLED.", "Home and overview buttons are enabled.", "The home and Overview buttons are not accessible.", "Only the home button is enabled."], "type": "string"}}, "type": "object"}, "LaunchAppAction": {"description": "An action to launch an app.", "id": "LaunchAppAction", "properties": {"packageName": {"description": "Package name of app to be launched", "type": "string"}}, "type": "object"}, "ListDevicesResponse": {"description": "Response to a request to list devices for a given enterprise.", "id": "ListDevicesResponse", "properties": {"devices": {"description": "The list of devices.", "items": {"$ref": "<PERSON><PERSON>"}, "type": "array"}, "nextPageToken": {"description": "If there are more results, a token to retrieve next page of results.", "type": "string"}}, "type": "object"}, "ListEnrollmentTokensResponse": {"description": "Response to a request to list enrollment tokens for a given enterprise.", "id": "ListEnrollmentTokensResponse", "properties": {"enrollmentTokens": {"description": "The list of enrollment tokens.", "items": {"$ref": "EnrollmentToken"}, "type": "array"}, "nextPageToken": {"description": "If there are more results, a token to retrieve next page of results.", "type": "string"}}, "type": "object"}, "ListEnterprisesResponse": {"description": "Response to a request to list enterprises.", "id": "ListEnterprisesResponse", "properties": {"enterprises": {"description": "The list of enterprises.", "items": {"$ref": "Enterprise"}, "type": "array"}, "nextPageToken": {"description": "If there are more results, a token to retrieve next page of results.", "type": "string"}}, "type": "object"}, "ListMigrationTokensResponse": {"description": "Response to a request to list migration tokens for a given enterprise.", "id": "ListMigrationTokensResponse", "properties": {"migrationTokens": {"description": "The migration tokens from the specified enterprise.", "items": {"$ref": "MigrationToken"}, "type": "array"}, "nextPageToken": {"description": "A token, which can be sent as page_token to retrieve the next page. If this field is omitted, there are no subsequent pages.", "type": "string"}}, "type": "object"}, "ListOperationsResponse": {"description": "The response message for Operations.ListOperations.", "id": "ListOperationsResponse", "properties": {"nextPageToken": {"description": "The standard List next-page token.", "type": "string"}, "operations": {"description": "A list of operations that matches the specified filter in the request.", "items": {"$ref": "Operation"}, "type": "array"}}, "type": "object"}, "ListPoliciesResponse": {"description": "Response to a request to list policies for a given enterprise.", "id": "ListPoliciesResponse", "properties": {"nextPageToken": {"description": "If there are more results, a token to retrieve next page of results.", "type": "string"}, "policies": {"description": "The list of policies.", "items": {"$ref": "Policy"}, "type": "array"}}, "type": "object"}, "ListWebAppsResponse": {"description": "Response to a request to list web apps for a given enterprise.", "id": "ListWebAppsResponse", "properties": {"nextPageToken": {"description": "If there are more results, a token to retrieve next page of results.", "type": "string"}, "webApps": {"description": "The list of web apps.", "items": {"$ref": "WebApp"}, "type": "array"}}, "type": "object"}, "Location": {"description": "The device location containing the latitude and longitude.", "id": "Location", "properties": {"latitude": {"description": "The latitude position of the location", "format": "double", "type": "number"}, "longitude": {"description": "The longitude position of the location", "format": "double", "type": "number"}}, "type": "object"}, "LogBufferSizeCriticalEvent": {"description": "The usageLog buffer on the device has reached 90% of its capacity, therefore older events may be dropped. Intentionally empty.", "id": "LogBufferSizeCriticalEvent", "properties": {}, "type": "object"}, "LoggingStartedEvent": {"description": "usageLog policy has been enabled. Intentionally empty.", "id": "LoggingStartedEvent", "properties": {}, "type": "object"}, "LoggingStoppedEvent": {"description": "usageLog policy has been disabled. Intentionally empty.", "id": "LoggingStoppedEvent", "properties": {}, "type": "object"}, "LostModeLocationEvent": {"description": "A lost mode event containing the device location and battery level as a percentage.", "id": "LostModeLocationEvent", "properties": {"batteryLevel": {"description": "The battery level as a number between 0 and 100 inclusive", "format": "int32", "type": "integer"}, "location": {"$ref": "Location", "description": "The device location"}}, "type": "object"}, "LostModeOutgoingPhoneCallEvent": {"description": "An event indicating an outgoing phone call has been made when a device is in lost mode. Intentionally empty.", "id": "LostModeOutgoingPhoneCallEvent", "properties": {}, "type": "object"}, "ManagedConfigurationTemplate": {"description": "The managed configurations template for the app, saved from the managed configurations iframe.", "id": "ManagedConfigurationTemplate", "properties": {"configurationVariables": {"additionalProperties": {"type": "string"}, "description": "Optional, a map containing configuration variables defined for the configuration.", "type": "object"}, "templateId": {"description": "The ID of the managed configurations template.", "type": "string"}}, "type": "object"}, "ManagedProperty": {"description": "Managed property.", "id": "ManagedProperty", "properties": {"defaultValue": {"description": "The default value of the property. BUNDLE_ARRAY properties don't have a default value.", "type": "any"}, "description": {"description": "A longer description of the property, providing more detail of what it affects. Localized.", "type": "string"}, "entries": {"description": "For CHOICE or MULTISELECT properties, the list of possible entries.", "items": {"$ref": "ManagedPropertyEntry"}, "type": "array"}, "key": {"description": "The unique key that the app uses to identify the property, e.g. \"com.google.android.gm.fieldname\".", "type": "string"}, "nestedProperties": {"description": "For BUNDLE_ARRAY properties, the list of nested properties. A BUNDLE_ARRAY property is at most two levels deep.", "items": {"$ref": "ManagedProperty"}, "type": "array"}, "title": {"description": "The name of the property. Localized.", "type": "string"}, "type": {"description": "The type of the property.", "enum": ["MANAGED_PROPERTY_TYPE_UNSPECIFIED", "BOOL", "STRING", "INTEGER", "CHOICE", "MULTISELECT", "HIDDEN", "BUNDLE", "BUNDLE_ARRAY"], "enumDescriptions": ["Not used.", "A property of boolean type.", "A property of string type.", "A property of integer type.", "A choice of one item from a set.", "A choice of multiple items from a set.", "A hidden restriction of string type (the default value can be used to pass along information that can't be modified, such as a version code).", "A bundle of properties", "An array of property bundles."], "type": "string"}}, "type": "object"}, "ManagedPropertyEntry": {"description": "An entry of a managed property.", "id": "ManagedPropertyEntry", "properties": {"name": {"description": "The human-readable name of the value. Localized.", "type": "string"}, "value": {"description": "The machine-readable value of the entry, which should be used in the configuration. Not localized.", "type": "string"}}, "type": "object"}, "MediaMountEvent": {"description": "Removable media was mounted.", "id": "MediaMountEvent", "properties": {"mountPoint": {"description": "Mount point.", "type": "string"}, "volumeLabel": {"description": "Volume label. Redacted to empty string on organization-owned managed profile devices.", "type": "string"}}, "type": "object"}, "MediaUnmountEvent": {"description": "Removable media was unmounted.", "id": "MediaUnmountEvent", "properties": {"mountPoint": {"description": "Mount point.", "type": "string"}, "volumeLabel": {"description": "Volume label. Redacted to empty string on organization-owned managed profile devices.", "type": "string"}}, "type": "object"}, "MemoryEvent": {"description": "An event related to memory and storage measurements.To distinguish between new and old events, we recommend using the createTime field.", "id": "MemoryEvent", "properties": {"byteCount": {"description": "The number of free bytes in the medium, or for EXTERNAL_STORAGE_DETECTED, the total capacity in bytes of the storage medium.", "format": "int64", "type": "string"}, "createTime": {"description": "The creation time of the event.", "format": "google-datetime", "type": "string"}, "eventType": {"description": "Event type.", "enum": ["MEMORY_EVENT_TYPE_UNSPECIFIED", "RAM_MEASURED", "INTERNAL_STORAGE_MEASURED", "EXTERNAL_STORAGE_DETECTED", "EXTERNAL_STORAGE_REMOVED", "EXTERNAL_STORAGE_MEASURED"], "enumDescriptions": ["Unspecified. No events have this type.", "Free space in RAM was measured.", "Free space in internal storage was measured.", "A new external storage medium was detected. The reported byte count is the total capacity of the storage medium.", "An external storage medium was removed. The reported byte count is zero.", "Free space in an external storage medium was measured."], "type": "string"}}, "type": "object"}, "MemoryInfo": {"description": "Information about device memory and storage.", "id": "MemoryInfo", "properties": {"totalInternalStorage": {"description": "Total internal storage on device in bytes.", "format": "int64", "type": "string"}, "totalRam": {"description": "Total RAM on device in bytes.", "format": "int64", "type": "string"}}, "type": "object"}, "MigrationToken": {"description": "A token to initiate the migration of a device from being managed by a third-party DPC to being managed by Android Management API. A migration token is valid only for a single device. See the guide (https://developers.google.com/android/management/dpc-migration) for more details.", "id": "MigrationToken", "properties": {"additionalData": {"description": "Immutable. Optional EMM-specified additional data. Once the device is migrated this will be populated in the migrationAdditionalData field of the Device resource. This must be at most 1024 characters.", "type": "string"}, "createTime": {"description": "Output only. Time when this migration token was created.", "format": "google-datetime", "readOnly": true, "type": "string"}, "device": {"description": "Output only. Once this migration token is used to migrate a device, the name of the resulting Device resource will be populated here, in the form enterprises/{enterprise}/devices/{device}.", "readOnly": true, "type": "string"}, "deviceId": {"description": "Required. Immutable. The id of the device, as in the Play EMM API. This corresponds to the deviceId parameter in Play EMM API's Devices.get (https://developers.google.com/android/work/play/emm-api/v1/devices/get#parameters) call.", "type": "string"}, "expireTime": {"description": "Immutable. The time when this migration token expires. This can be at most seven days from the time of creation. The migration token is deleted seven days after it expires.", "format": "google-datetime", "type": "string"}, "managementMode": {"description": "Required. Immutable. The management mode of the device or profile being migrated.", "enum": ["MANAGEMENT_MODE_UNSPECIFIED", "WORK_PROFILE_PERSONALLY_OWNED", "WORK_PROFILE_COMPANY_OWNED", "FULLY_MANAGED"], "enumDescriptions": ["This value must not be used.", "A work profile on a personally owned device. Supported only on devices running Android 9 and above.", "A work profile on a company-owned device. Supported only on devices running Android 11 and above.", "A fully-managed device. Supported only on devices running Android 9 and above."], "type": "string"}, "name": {"description": "Output only. The name of the migration token, which is generated by the server during creation, in the form enterprises/{enterprise}/migrationTokens/{migration_token}.", "readOnly": true, "type": "string"}, "policy": {"description": "Required. Immutable. The name of the policy initially applied to the enrolled device, in the form enterprises/{enterprise}/policies/{policy}.", "type": "string"}, "ttl": {"description": "Input only. The time that this migration token is valid for. This is input-only, and for returning a migration token the server will populate the expireTime field. This can be at most seven days. The default is seven days.", "format": "google-duration", "type": "string"}, "userId": {"description": "Required. Immutable. The user id of the Managed Google Play account on the device, as in the Play EMM API. This corresponds to the userId parameter in Play EMM API's Devices.get (https://developers.google.com/android/work/play/emm-api/v1/devices/get#parameters) call.", "type": "string"}, "value": {"description": "Output only. The value of the migration token.", "readOnly": true, "type": "string"}}, "type": "object"}, "ModifyPolicyApplicationsRequest": {"description": "Request to update or create ApplicationPolicy objects in the given Policy.", "id": "ModifyPolicyApplicationsRequest", "properties": {"changes": {"description": "Required. The changes to be made to the ApplicationPolicy objects. There must be at least one ApplicationPolicyChange.", "items": {"$ref": "ApplicationPolicyChange"}, "type": "array"}}, "type": "object"}, "ModifyPolicyApplicationsResponse": {"description": "Response to a request to update or create ApplicationPolicy objects in the given policy.", "id": "ModifyPolicyApplicationsResponse", "properties": {"policy": {"$ref": "Policy", "description": "The updated policy."}}, "type": "object"}, "NetworkInfo": {"description": "Device network info.", "id": "NetworkInfo", "properties": {"imei": {"description": "IMEI number of the GSM device. For example, A1000031212.", "type": "string"}, "meid": {"description": "MEID number of the CDMA device. For example, A00000292788E1.", "type": "string"}, "networkOperatorName": {"deprecated": true, "description": "Alphabetic name of current registered operator. For example, Vodafone.", "type": "string"}, "telephonyInfos": {"description": "Provides telephony information associated with each SIM card on the device. Only supported on fully managed devices starting from Android API level 23.", "items": {"$ref": "TelephonyInfo"}, "type": "array"}, "wifiMacAddress": {"description": "Wi-Fi MAC address of the device. For example, 7c:11:11:11:11:11.", "type": "string"}}, "type": "object"}, "NonComplianceDetail": {"description": "Provides detail about non-compliance with a policy setting.", "id": "NonComplianceDetail", "properties": {"currentValue": {"description": "If the policy setting could not be applied, the current value of the setting on the device.", "type": "any"}, "fieldPath": {"description": "For settings with nested fields, if a particular nested field is out of compliance, this specifies the full path to the offending field. The path is formatted in the same way the policy JSON field would be referenced in JavaScript, that is: 1) For object-typed fields, the field name is followed by a dot then by a subfield name. 2) For array-typed fields, the field name is followed by the array index enclosed in brackets. For example, to indicate a problem with the url field in the externalData field in the 3rd application, the path would be applications[2].externalData.url", "type": "string"}, "installationFailureReason": {"description": "If package_name is set and the non-compliance reason is APP_NOT_INSTALLED or APP_NOT_UPDATED, the detailed reason the app can't be installed or updated.", "enum": ["INSTALLATION_FAILURE_REASON_UNSPECIFIED", "INSTALLATION_FAILURE_REASON_UNKNOWN", "IN_PROGRESS", "NOT_FOUND", "NOT_COMPATIBLE_WITH_DEVICE", "NOT_APPROVED", "PERMISSIONS_NOT_ACCEPTED", "NOT_AVAILABLE_IN_COUNTRY", "NO_LICENSES_REMAINING", "NOT_ENROLLED", "USER_INVALID", "NETWORK_ERROR_UNRELIABLE_CONNECTION", "INSUFFICIENT_STORAGE"], "enumDescriptions": ["This value is disallowed.", "An unknown condition is preventing the app from being installed. Some potential reasons are that the device doesn't have enough storage, the device network connection is unreliable, or the installation is taking longer than expected. The installation will be retried automatically.", "The installation is still in progress.", "The app was not found in Play.", "The app is incompatible with the device.", "The app has not been approved by the admin.", "The app has new permissions that have not been accepted by the admin.", "The app is not available in the user's country.", "There are no licenses available to assign to the user.", "The enterprise is no longer enrolled with Managed Google Play or the admin has not accepted the latest Managed Google Play Terms of Service.", "The user is no longer valid. The user may have been deleted or disabled.", "A network error on the user's device has prevented the install from succeeding. This usually happens when the device's internet connectivity is degraded, unavailable or there's a network configuration issue. Please ensure the device has access to full internet connectivity on a network that meets Android Enterprise Network Requirements (https://support.google.com/work/android/answer/10513641). App install or update will automatically resume once this is the case.", "The user's device does not have sufficient storage space to install the app. This can be resolved by clearing up storage space on the device. App install or update will automatically resume once the device has sufficient storage."], "type": "string"}, "nonComplianceReason": {"description": "The reason the device is not in compliance with the setting.", "enum": ["NON_COMPLIANCE_REASON_UNSPECIFIED", "API_LEVEL", "MANAGEMENT_MODE", "USER_ACTION", "INVALID_VALUE", "APP_NOT_INSTALLED", "UNSUPPORTED", "APP_INSTALLED", "PENDING", "APP_INCOMPATIBLE", "APP_NOT_UPDATED", "DEVICE_INCOMPATIBLE", "PROJECT_NOT_PERMITTED"], "enumDescriptions": ["This value is not used.", "The setting is not supported in the API level of the Android version running on the device.", "The management mode (such as fully managed or work profile) doesn't support the setting.", "The user has not taken required action to comply with the setting.", "The setting has an invalid value.", "The app required to implement the policy is not installed.", "The policy is not supported by the version of Android Device Policy on the device.", "A blocked app is installed.", "The setting hasn't been applied at the time of the report, but is expected to be applied shortly.", "The setting can't be applied to the app because the app doesn't support it, for example because its target SDK version is not high enough.", "The app is installed, but it hasn't been updated to the minimum version code specified by policy.", "The device is incompatible with the policy requirements.", "The Google Cloud Platform project used to manage the device is not permitted to use this policy."], "type": "string"}, "packageName": {"description": "The package name indicating which app is out of compliance, if applicable.", "type": "string"}, "settingName": {"description": "The name of the policy setting. This is the JSON field name of a top-level Policy field.", "type": "string"}, "specificNonComplianceContext": {"$ref": "SpecificNonComplianceContext", "description": "Additional context for specific_non_compliance_reason."}, "specificNonComplianceReason": {"description": "The policy-specific reason the device is not in compliance with the setting.", "enum": ["SPECIFIC_NON_COMPLIANCE_REASON_UNSPECIFIED", "PASSWORD_POLICIES_USER_CREDENTIALS_CONFIRMATION_REQUIRED", "PASSWORD_POLICIES_PASSWORD_EXPIRED", "PASSWORD_POLICIES_PASSWORD_NOT_SUFFICIENT", "ONC_WIFI_INVALID_VALUE", "ONC_WIFI_API_LEVEL", "ONC_WIFI_INVALID_ENTERPRISE_CONFIG", "ONC_WIFI_USER_SHOULD_REMOVE_NETWORK", "ONC_WIFI_KEY_PAIR_ALIAS_NOT_CORRESPONDING_TO_EXISTING_KEY", "PERMISSIBLE_USAGE_RESTRICTION", "REQUIRED_ACCOUNT_NOT_IN_ENTERPRISE", "NEW_ACCOUNT_NOT_IN_ENTERPRISE"], "enumDescriptions": ["Specific non-compliance reason is not specified. Fields in specific_non_compliance_context are not set.", "User needs to confirm credentials by entering the screen lock. Fields in specific_non_compliance_context are not set. nonComplianceReason is set to USER_ACTION.", "The device or profile password has expired. passwordPoliciesContext is set. nonComplianceReason is set to USER_ACTION.", "The device password does not satisfy password requirements. passwordPoliciesContext is set. nonComplianceReason is set to USER_ACTION.", "There is an incorrect value in ONC Wi-Fi configuration. fieldPath specifies which field value is incorrect. oncWifiContext is set. nonComplianceReason is set to INVALID_VALUE.", "The ONC Wi-Fi setting is not supported in the API level of the Android version running on the device. fieldPath specifies which field value is not supported. oncWifiContext is set. nonComplianceReason is set to API_LEVEL.", "The enterprise Wi-Fi network is missing either the root CA or domain name. nonComplianceReason is set to INVALID_VALUE.", "User needs to remove the configured Wi-Fi network manually. This is applicable only on work profiles on personally-owned devices. nonComplianceReason is set to USER_ACTION.", "Key pair alias specified via ClientCertKeyPairAlias (https://chromium.googlesource.com/chromium/src/+/main/components/onc/docs/onc_spec.md#eap-type) field in openNetworkConfiguration does not correspond to an existing key installed on the device. nonComplianceReason is set to INVALID_VALUE.", "This policy setting is restricted and cannot be set for this Google Cloud Platform project. More details (including how to enable usage of this policy setting) are available in the Permissible Usage policy (https://developers.google.com/android/management/permissible-usage). nonComplianceReason is set to PROJECT_NOT_PERMITTED.", "Work account required by the workAccountSetupConfig policy setting is not part of the enterprise anymore. nonComplianceReason is set to USER_ACTION.", "Work account added by the user is not part of the enterprise. nonComplianceReason is set to USER_ACTION."], "type": "string"}}, "type": "object"}, "NonComplianceDetailCondition": {"deprecated": true, "description": "A compliance rule condition which is satisfied if there exists any matching NonComplianceDetail for the device. A NonComplianceDetail matches a NonComplianceDetailCondition if all the fields which are set within the NonComplianceDetailCondition match the corresponding NonComplianceDetail fields.", "id": "NonComplianceDetailCondition", "properties": {"nonComplianceReason": {"description": "The reason the device is not in compliance with the setting. If not set, then this condition matches any reason.", "enum": ["NON_COMPLIANCE_REASON_UNSPECIFIED", "API_LEVEL", "MANAGEMENT_MODE", "USER_ACTION", "INVALID_VALUE", "APP_NOT_INSTALLED", "UNSUPPORTED", "APP_INSTALLED", "PENDING", "APP_INCOMPATIBLE", "APP_NOT_UPDATED", "DEVICE_INCOMPATIBLE", "PROJECT_NOT_PERMITTED"], "enumDescriptions": ["This value is not used.", "The setting is not supported in the API level of the Android version running on the device.", "The management mode (such as fully managed or work profile) doesn't support the setting.", "The user has not taken required action to comply with the setting.", "The setting has an invalid value.", "The app required to implement the policy is not installed.", "The policy is not supported by the version of Android Device Policy on the device.", "A blocked app is installed.", "The setting hasn't been applied at the time of the report, but is expected to be applied shortly.", "The setting can't be applied to the app because the app doesn't support it, for example because its target SDK version is not high enough.", "The app is installed, but it hasn't been updated to the minimum version code specified by policy.", "The device is incompatible with the policy requirements.", "The Google Cloud Platform project used to manage the device is not permitted to use this policy."], "type": "string"}, "packageName": {"description": "The package name of the app that's out of compliance. If not set, then this condition matches any package name.", "type": "string"}, "settingName": {"description": "The name of the policy setting. This is the JSON field name of a top-level Policy field. If not set, then this condition matches any setting name.", "type": "string"}}, "type": "object"}, "OncCertificateProvider": {"description": "This feature is not generally available.", "id": "OncCertificateProvider", "properties": {"certificateReferences": {"description": "This feature is not generally available.", "items": {"type": "string"}, "type": "array"}, "contentProviderEndpoint": {"$ref": "ContentProviderEndpoint", "description": "This feature is not generally available."}}, "type": "object"}, "OncWifiContext": {"description": "Additional context for non-compliance related to Wi-Fi configuration.", "id": "OncWifiContext", "properties": {"wifiGuid": {"description": "The GUID of non-compliant Wi-Fi configuration.", "type": "string"}}, "type": "object"}, "Operation": {"description": "This resource represents a long-running operation that is the result of a network API call.", "id": "Operation", "properties": {"done": {"description": "If the value is false, it means the operation is still in progress. If true, the operation is completed, and either error or response is available.", "type": "boolean"}, "error": {"$ref": "Status", "description": "The error result of the operation in case of failure or cancellation."}, "metadata": {"additionalProperties": {"description": "Properties of the object. Contains field @type with type URL.", "type": "any"}, "description": "Service-specific metadata associated with the operation. It typically contains progress information and common metadata such as create time. Some services might not provide such metadata. Any method that returns a long-running operation should document the metadata type, if any.", "type": "object"}, "name": {"description": "The server-assigned name, which is only unique within the same service that originally returns it. If you use the default HTTP mapping, the name should be a resource name ending with operations/{unique_id}.", "type": "string"}, "response": {"additionalProperties": {"description": "Properties of the object. Contains field @type with type URL.", "type": "any"}, "description": "The normal, successful response of the operation. If the original method returns no data on success, such as Delete, the response is google.protobuf.Empty. If the original method is standard Get/Create/Update, the response should be the resource. For other methods, the response should have the type XxxResponse, where Xxx is the original method name. For example, if the original method name is TakeSnapshot(), the inferred response type is TakeSnapshotResponse.", "type": "object"}}, "type": "object"}, "OsShutdownEvent": {"description": "Device was shutdown. Intentionally empty.", "id": "OsShutdownEvent", "properties": {}, "type": "object"}, "OsStartupEvent": {"description": "Device was started.", "id": "OsStartupEvent", "properties": {"verifiedBootState": {"description": "Verified Boot state.", "enum": ["VERIFIED_BOOT_STATE_UNSPECIFIED", "GREEN", "YELLOW", "ORANGE"], "enumDescriptions": ["Unknown value.", "Indicates that there is a full chain of trust extending from the bootloader to verified partitions including the bootloader, boot partition, and all verified partitions.", "Indicates that the boot partition has been verified using the embedded certificate and the signature is valid.", "Indicates that the device may be freely modified. Device integrity is left to the user to verify out-of-band."], "type": "string"}, "verityMode": {"description": "dm-verity mode.", "enum": ["DM_VERITY_MODE_UNSPECIFIED", "ENFORCING", "IO_ERROR", "DISABLED"], "enumDescriptions": ["Unknown value.", "Indicates that the device will be restarted when corruption is detected.", "Indicates that an I/O error will be returned for an attempt to read corrupted data blocks (also known as eio boot state).", "Indicates that dm-verity is disabled on device."], "type": "string"}}, "type": "object"}, "PackageNameList": {"description": "A list of package names.", "id": "PackageNameList", "properties": {"packageNames": {"description": "A list of package names.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "PasswordPoliciesContext": {"description": "Additional context for non-compliance related to password policies.", "id": "PasswordPoliciesContext", "properties": {"passwordPolicyScope": {"description": "The scope of non-compliant password.", "enum": ["SCOPE_UNSPECIFIED", "SCOPE_DEVICE", "SCOPE_PROFILE"], "enumDescriptions": ["The scope is unspecified. The password requirements are applied to the work profile for work profile devices and the whole device for fully managed or dedicated devices.", "The password requirements are only applied to the device.", "The password requirements are only applied to the work profile."], "type": "string"}}, "type": "object"}, "PasswordRequirements": {"description": "Requirements for the password used to unlock a device.", "id": "PasswordRequirements", "properties": {"maximumFailedPasswordsForWipe": {"description": "Number of incorrect device-unlock passwords that can be entered before a device is wiped. A value of 0 means there is no restriction.", "format": "int32", "type": "integer"}, "passwordExpirationTimeout": {"description": "Password expiration timeout.", "format": "google-duration", "type": "string"}, "passwordHistoryLength": {"description": "The length of the password history. After setting this field, the user won't be able to enter a new password that is the same as any password in the history. A value of 0 means there is no restriction.", "format": "int32", "type": "integer"}, "passwordMinimumLength": {"description": "The minimum allowed password length. A value of 0 means there is no restriction. Only enforced when password_quality is NUMERIC, NUMERIC_COMPLEX, ALPHABETIC, ALPHANUMERIC, or COMPLEX.", "format": "int32", "type": "integer"}, "passwordMinimumLetters": {"description": "Minimum number of letters required in the password. Only enforced when password_quality is COMPLEX.", "format": "int32", "type": "integer"}, "passwordMinimumLowerCase": {"description": "Minimum number of lower case letters required in the password. Only enforced when password_quality is COMPLEX.", "format": "int32", "type": "integer"}, "passwordMinimumNonLetter": {"description": "Minimum number of non-letter characters (numerical digits or symbols) required in the password. Only enforced when password_quality is COMPLEX.", "format": "int32", "type": "integer"}, "passwordMinimumNumeric": {"description": "Minimum number of numerical digits required in the password. Only enforced when password_quality is COMPLEX.", "format": "int32", "type": "integer"}, "passwordMinimumSymbols": {"description": "Minimum number of symbols required in the password. Only enforced when password_quality is COMPLEX.", "format": "int32", "type": "integer"}, "passwordMinimumUpperCase": {"description": "Minimum number of upper case letters required in the password. Only enforced when password_quality is COMPLEX.", "format": "int32", "type": "integer"}, "passwordQuality": {"description": "The required password quality.", "enum": ["PASSWORD_QUALITY_UNSPECIFIED", "BIOMETRIC_WEAK", "SOMETHING", "NUMERIC", "NUMERIC_COMPLEX", "ALPHABETIC", "ALPHANUMERIC", "COMPLEX", "COMPLEXITY_LOW", "COMPLEXITY_MEDIUM", "COMPLEXITY_HIGH"], "enumDescriptions": ["There are no password requirements.", "The device must be secured with a low-security biometric recognition technology, at minimum. This includes technologies that can recognize the identity of an individual that are roughly equivalent to a 3-digit PIN (false detection is less than 1 in 1,000).This, when applied on personally owned work profile devices on Android 12 device-scoped, will be treated as COMPLEXITY_LOW for application. See PasswordQuality for details.", "A password is required, but there are no restrictions on what the password must contain.This, when applied on personally owned work profile devices on Android 12 device-scoped, will be treated as COMPLEXITY_LOW for application. See PasswordQuality for details.", "The password must contain numeric characters.This, when applied on personally owned work profile devices on Android 12 device-scoped, will be treated as COMPLEXITY_MEDIUM for application. See PasswordQuality for details.", "The password must contain numeric characters with no repeating (4444) or ordered (1234, 4321, 2468) sequences.This, when applied on personally owned work profile devices on Android 12 device-scoped, will be treated as COMPLEXITY_MEDIUM for application. See PasswordQuality for details.", "The password must contain alphabetic (or symbol) characters.This, when applied on personally owned work profile devices on Android 12 device-scoped, will be treated as COMPLEXITY_HIGH for application. See PasswordQuality for details.", "The password must contain both numeric and alphabetic (or symbol) characters.This, when applied on personally owned work profile devices on Android 12 device-scoped, will be treated as COMPLEXITY_HIGH for application. See PasswordQuality for details.", "The password must meet the minimum requirements specified in passwordMinimumLength, passwordMinimumLetters, passwordMinimumSymbols, etc. For example, if passwordMinimumSymbols is 2, the password must contain at least two symbols.This, when applied on personally owned work profile devices on Android 12 device-scoped, will be treated as COMPLEXITY_HIGH for application. In this case, the requirements in passwordMinimumLength, passwordMinimumLetters, passwordMinimumSymbols, etc are not applied. See PasswordQuality for details.", "Define the low password complexity band as: pattern PIN with repeating (4444) or ordered (1234, 4321, 2468) sequencesThis sets the minimum complexity band which the password must meet.Enforcement varies among different Android versions, management modes and password scopes. See PasswordQuality for details.", "Define the medium password complexity band as: PIN with no repeating (4444) or ordered (1234, 4321, 2468) sequences, length at least 4 alphabetic, length at least 4 alphanumeric, length at least 4This sets the minimum complexity band which the password must meet.Enforcement varies among different Android versions, management modes and password scopes. See PasswordQuality for details.", "Define the high password complexity band as:On Android 12 and above: PIN with no repeating (4444) or ordered (1234, 4321, 2468) sequences, length at least 8 alphabetic, length at least 6 alphanumeric, length at least 6This sets the minimum complexity band which the password must meet.Enforcement varies among different Android versions, management modes and password scopes. See PasswordQuality for details."], "type": "string"}, "passwordScope": {"description": "The scope that the password requirement applies to.", "enum": ["SCOPE_UNSPECIFIED", "SCOPE_DEVICE", "SCOPE_PROFILE"], "enumDescriptions": ["The scope is unspecified. The password requirements are applied to the work profile for work profile devices and the whole device for fully managed or dedicated devices.", "The password requirements are only applied to the device.", "The password requirements are only applied to the work profile."], "type": "string"}, "requirePasswordUnlock": {"description": "The length of time after a device or work profile is unlocked using a strong form of authentication (password, PIN, pattern) that it can be unlocked using any other authentication method (e.g. fingerprint, trust agents, face). After the specified time period elapses, only strong forms of authentication can be used to unlock the device or work profile.", "enum": ["REQUIRE_PASSWORD_UNLOCK_UNSPECIFIED", "USE_DEFAULT_DEVICE_TIMEOUT", "REQUIRE_EVERY_DAY"], "enumDescriptions": ["Unspecified. Defaults to USE_DEFAULT_DEVICE_TIMEOUT.", "The timeout period is set to the device’s default.", "The timeout period is set to 24 hours."], "type": "string"}, "unifiedLockSettings": {"description": "Controls whether a unified lock is allowed for the device and the work profile, on devices running Android 9 and above with a work profile. This can be set only if password_scope is set to SCOPE_PROFILE, the policy will be rejected otherwise. If user has not set a separate work lock and this field is set to REQUIRE_SEPARATE_WORK_LOCK, a NonComplianceDetail is reported with nonComplianceReason set to USER_ACTION.", "enum": ["UNIFIED_LOCK_SETTINGS_UNSPECIFIED", "ALLOW_UNIFIED_WORK_AND_PERSONAL_LOCK", "REQUIRE_SEPARATE_WORK_LOCK"], "enumDescriptions": ["Unspecified. Defaults to ALLOW_UNIFIED_WORK_AND_PERSONAL_LOCK.", "A common lock for the device and the work profile is allowed.", "A separate lock for the work profile is required."], "type": "string"}}, "type": "object"}, "PerAppResult": {"description": "The result of an attempt to clear the data of a single app.", "id": "PerAppResult", "properties": {"clearingResult": {"description": "The result of an attempt to clear the data of a single app.", "enum": ["CLEARING_RESULT_UNSPECIFIED", "SUCCESS", "APP_NOT_FOUND", "APP_PROTECTED", "API_LEVEL"], "enumDescriptions": ["Unspecified result.", "This app’s data was successfully cleared.", "This app’s data could not be cleared because the app was not found.", "This app’s data could not be cleared because the app is protected. For example, this may apply to apps critical to the functioning of the device, such as Google Play Store.", "This app’s data could not be cleared because the device API level does not support this command."], "type": "string"}}, "type": "object"}, "PermissionGrant": {"description": "Configuration for an Android permission and its grant state.", "id": "PermissionGrant", "properties": {"permission": {"description": "The Android permission or group, e.g. android.permission.READ_CALENDAR or android.permission_group.CALENDAR.", "type": "string"}, "policy": {"description": "The policy for granting the permission.", "enum": ["PERMISSION_POLICY_UNSPECIFIED", "PROMPT", "GRANT", "DENY"], "enumDescriptions": ["Policy not specified. If no policy is specified for a permission at any level, then the PROMPT behavior is used by default.", "Prompt the user to grant a permission.", "Automatically grant a permission.On Android 12 and above, READ_SMS (https://developer.android.com/reference/android/Manifest.permission#READ_SMS) and following sensor-related permissions can only be granted on fully managed devices: ACCESS_FINE_LOCATION (https://developer.android.com/reference/android/Manifest.permission#ACCESS_FINE_LOCATION) ACCESS_BACKGROUND_LOCATION (https://developer.android.com/reference/android/Manifest.permission#ACCESS_BACKGROUND_LOCATION) ACCESS_COARSE_LOCATION (https://developer.android.com/reference/android/Manifest.permission#ACCESS_COARSE_LOCATION) CAMERA (https://developer.android.com/reference/android/Manifest.permission#CAMERA) RECORD_AUDIO (https://developer.android.com/reference/android/Manifest.permission#RECORD_AUDIO) ACTIVITY_RECOGNITION (https://developer.android.com/reference/android/Manifest.permission#ACTIVITY_RECOGNITION) BODY_SENSORS (https://developer.android.com/reference/android/Manifest.permission#BODY_SENSORS)", "Automatically deny a permission."], "type": "string"}}, "type": "object"}, "PersistentPreferredActivity": {"description": "A default activity for handling intents that match a particular intent filter. Note: To set up a kiosk, use InstallType to KIOSK rather than use persistent preferred activities.", "id": "PersistentPreferredActivity", "properties": {"actions": {"description": "The intent actions to match in the filter. If any actions are included in the filter, then an intent's action must be one of those values for it to match. If no actions are included, the intent action is ignored.", "items": {"type": "string"}, "type": "array"}, "categories": {"description": "The intent categories to match in the filter. An intent includes the categories that it requires, all of which must be included in the filter in order to match. In other words, adding a category to the filter has no impact on matching unless that category is specified in the intent.", "items": {"type": "string"}, "type": "array"}, "receiverActivity": {"description": "The activity that should be the default intent handler. This should be an Android component name, e.g. com.android.enterprise.app/.MainActivity. Alternatively, the value may be the package name of an app, which causes Android Device Policy to choose an appropriate activity from the app to handle the intent.", "type": "string"}}, "type": "object"}, "PersonalApplicationPolicy": {"description": "Policies for apps in the personal profile of a company-owned device with a work profile.", "id": "PersonalApplicationPolicy", "properties": {"installType": {"description": "The type of installation to perform.", "enum": ["INSTALL_TYPE_UNSPECIFIED", "BLOCKED", "AVAILABLE"], "enumDescriptions": ["Unspecified. Defaults to AVAILABLE.", "The app is blocked and can't be installed in the personal profile. If the app was previously installed in the device, it will be uninstalled.", "The app is available to install in the personal profile."], "type": "string"}, "packageName": {"description": "The package name of the application.", "type": "string"}}, "type": "object"}, "PersonalUsagePolicies": {"description": "Policies controlling personal usage on a company-owned device with a work profile.", "id": "PersonalUsagePolicies", "properties": {"accountTypesWithManagementDisabled": {"description": "Account types that can't be managed by the user.", "items": {"type": "string"}, "type": "array"}, "bluetoothSharing": {"description": "Optional. Whether bluetooth sharing is allowed.", "enum": ["BLUETOOTH_SHARING_UNSPECIFIED", "BLUETOOTH_SHARING_ALLOWED", "BLUETOOTH_SHARING_DISALLOWED"], "enumDescriptions": ["Unspecified. Defaults to BLUETOOTH_SHARING_ALLOWED.", "Bluetooth sharing is allowed on personal profile.Supported on Android 8 and above. A NonComplianceDetail with MANAGEMENT_MODE is reported if this is set for a personal device.", "Bluetooth sharing is disallowed on personal profile.Supported on Android 8 and above. A NonComplianceDetail with API_LEVEL is reported if the Android version is less than 8. A NonComplianceDetail with MANAGEMENT_MODE is reported if this is set for a personal device."], "type": "string"}, "cameraDisabled": {"description": "If true, the camera is disabled on the personal profile.", "type": "boolean"}, "maxDaysWithWorkOff": {"description": "Controls how long the work profile can stay off. The minimum duration must be at least 3 days. Other details are as follows: - If the duration is set to 0, the feature is turned off. - If the duration is set to a value smaller than the minimum duration, the feature returns an error. *Note:* If you want to avoid personal profiles being suspended during long periods of off-time, you can temporarily set a large value for this parameter.", "format": "int32", "type": "integer"}, "personalApplications": {"description": "Policy applied to applications in the personal profile.", "items": {"$ref": "PersonalApplicationPolicy"}, "type": "array"}, "personalPlayStoreMode": {"description": "Used together with personalApplications to control how apps in the personal profile are allowed or blocked.", "enum": ["PLAY_STORE_MODE_UNSPECIFIED", "BLACKLIST", "BLOCKLIST", "ALLOWLIST"], "enumDeprecated": [false, true, false, false], "enumDescriptions": ["Unspecified. Defaults to BLOCKLIST.", "All Play Store apps are available for installation in the personal profile, except those whose installType is BLOCKED in personalApplications.", "All Play Store apps are available for installation in the personal profile, except those whose installType is BLOCKED in personalApplications.", "Only apps explicitly specified in personalApplications with installType set to AVAILABLE are allowed to be installed in the personal profile."], "type": "string"}, "privateSpacePolicy": {"description": "Optional. Controls whether a private space is allowed on the device.", "enum": ["PRIVATE_SPACE_POLICY_UNSPECIFIED", "PRIVATE_SPACE_ALLOWED", "PRIVATE_SPACE_DISALLOWED"], "enumDescriptions": ["Unspecified. Defaults to PRIVATE_SPACE_ALLOWED.", "Users can create a private space profile.", "Users cannot create a private space profile. Supported only for company-owned devices with a work profile. Caution: Any existing private space will be removed."], "type": "string"}, "screenCaptureDisabled": {"description": "If true, screen capture is disabled for all users.", "type": "boolean"}}, "type": "object"}, "Policy": {"description": "A policy resource represents a group of settings that govern the behavior of a managed device and the apps installed on it.", "id": "Policy", "properties": {"accountTypesWithManagementDisabled": {"description": "Account types that can't be managed by the user.", "items": {"type": "string"}, "type": "array"}, "addUserDisabled": {"description": "Whether adding new users and profiles is disabled. For devices where managementMode is DEVICE_OWNER this field is ignored and the user is never allowed to add or remove users.", "type": "boolean"}, "adjustVolumeDisabled": {"description": "Whether adjusting the master volume is disabled. Also mutes the device. The setting has effect only on fully managed devices.", "type": "boolean"}, "advancedSecurityOverrides": {"$ref": "AdvancedSecurityOverrides", "description": "Advanced security settings. In most cases, setting these is not needed."}, "alwaysOnVpnPackage": {"$ref": "AlwaysOnVpnPackage", "description": "Configuration for an always-on VPN connection. Use with vpn_config_disabled to prevent modification of this setting."}, "androidDevicePolicyTracks": {"deprecated": true, "description": "This setting is not supported. Any value is ignored.", "items": {"enum": ["APP_TRACK_UNSPECIFIED", "PRODUCTION", "BETA"], "enumDescriptions": ["This value is ignored.", "The production track, which provides the latest stable release.", "The beta track, which provides the latest beta release."], "type": "string"}, "type": "array"}, "appAutoUpdatePolicy": {"description": "Recommended alternative: autoUpdateMode which is set per app, provides greater flexibility around update frequency.When autoUpdateMode is set to AUTO_UPDATE_POSTPONED or AUTO_UPDATE_HIGH_PRIORITY, this field has no effect.The app auto update policy, which controls when automatic app updates can be applied.", "enum": ["APP_AUTO_UPDATE_POLICY_UNSPECIFIED", "CHOICE_TO_THE_USER", "NEVER", "WIFI_ONLY", "ALWAYS"], "enumDescriptions": ["The auto-update policy is not set. Equivalent to CHOICE_TO_THE_USER.", "The user can control auto-updates.", "Apps are never auto-updated.", "Apps are auto-updated over Wi-Fi only.", "Apps are auto-updated at any time. Data charges may apply."], "type": "string"}, "appFunctions": {"description": "Optional. Controls whether apps on the device for fully managed devices or in the work profile for devices with work profiles are allowed to expose app functions.", "enum": ["APP_FUNCTIONS_UNSPECIFIED", "APP_FUNCTIONS_DISALLOWED", "APP_FUNCTIONS_ALLOWED"], "enumDescriptions": ["Unspecified. Defaults to APP_FUNCTIONS_ALLOWED.", "Apps on the device for fully managed devices or in the work profile for devices with work profiles are not allowed to expose app functions. If this is set, crossProfileAppFunctions must not be set to CROSS_PROFILE_APP_FUNCTIONS_ALLOWED, otherwise the policy will be rejected.", "Apps on the device for fully managed devices or in the work profile for devices with work profiles are allowed to expose app functions."], "type": "string"}, "applications": {"description": "Policy applied to apps. This can have at most 3,000 elements.", "items": {"$ref": "ApplicationPolicy"}, "type": "array"}, "assistContentPolicy": {"description": "Optional. Controls whether <PERSON><PERSON><PERSON>onte<PERSON> (https://developer.android.com/reference/android/app/assist/AssistContent) is allowed to be sent to a privileged app such as an assistant app. AssistContent includes screenshots and information about an app, such as package name. This is supported on Android 15 and above.", "enum": ["ASSIST_CONTENT_POLICY_UNSPECIFIED", "ASSIST_CONTENT_DISALLOWED", "ASSIST_CONTENT_ALLOWED"], "enumDescriptions": ["Unspecified. Defaults to ASSIST_CONTENT_ALLOWED.", "Assist content is blocked from being sent to a privileged app.Supported on Android 15 and above. A NonComplianceDetail with API_LEVEL is reported if the Android version is less than 15.", "Assist content is allowed to be sent to a privileged app.Supported on Android 15 and above."], "type": "string"}, "autoDateAndTimeZone": {"description": "Whether auto date, time, and time zone are enabled on a company-owned device. If this is set, then autoTimeRequired is ignored.", "enum": ["AUTO_DATE_AND_TIME_ZONE_UNSPECIFIED", "AUTO_DATE_AND_TIME_ZONE_USER_CHOICE", "AUTO_DATE_AND_TIME_ZONE_ENFORCED"], "enumDescriptions": ["Unspecified. Defaults to AUTO_DATE_AND_TIME_ZONE_USER_CHOICE.", "Auto date, time, and time zone are left to user's choice.", "Enforce auto date, time, and time zone on the device."], "type": "string"}, "autoTimeRequired": {"deprecated": true, "description": "Whether auto time is required, which prevents the user from manually setting the date and time. If autoDateAndTimeZone is set, this field is ignored.", "type": "boolean"}, "blockApplicationsEnabled": {"deprecated": true, "description": "Whether applications other than the ones configured in applications are blocked from being installed. When set, applications that were installed under a previous policy but no longer appear in the policy are automatically uninstalled.", "type": "boolean"}, "bluetoothConfigDisabled": {"description": "Whether configuring bluetooth is disabled.", "type": "boolean"}, "bluetoothContactSharingDisabled": {"description": "Whether bluetooth contact sharing is disabled.", "type": "boolean"}, "bluetoothDisabled": {"description": "Whether bluetooth is disabled. Prefer this setting over bluetooth_config_disabled because bluetooth_config_disabled can be bypassed by the user.", "type": "boolean"}, "cameraAccess": {"description": "Controls the use of the camera and whether the user has access to the camera access toggle.", "enum": ["CAMERA_ACCESS_UNSPECIFIED", "CAMERA_ACCESS_USER_CHOICE", "CAMERA_ACCESS_DISABLED", "CAMERA_ACCESS_ENFORCED"], "enumDescriptions": ["If camera_disabled is true, this is equivalent to CAMERA_ACCESS_DISABLED. Otherwise, this is equivalent to CAMERA_ACCESS_USER_CHOICE.", "The field camera_disabled is ignored. This is the default device behaviour: all cameras on the device are available. On Android 12 and above, the user can use the camera access toggle.", "The field camera_disabled is ignored. All cameras on the device are disabled (for fully managed devices, this applies device-wide and for work profiles this applies only to the work profile).There are no explicit restrictions placed on the camera access toggle on Android 12 and above: on fully managed devices, the camera access toggle has no effect as all cameras are disabled. On devices with a work profile, this toggle has no effect on apps in the work profile, but it affects apps outside the work profile.", "The field camera_disabled is ignored. All cameras on the device are available. On fully managed devices running Android 12 and above, the user is unable to use the camera access toggle. On devices which are not fully managed or which run Android 11 or below, this is equivalent to CAMERA_ACCESS_USER_CHOICE."], "type": "string"}, "cameraDisabled": {"deprecated": true, "description": "If camera_access is set to any value other than CAMERA_ACCESS_UNSPECIFIED, this has no effect. Otherwise this field controls whether cameras are disabled: If true, all cameras are disabled, otherwise they are available. For fully managed devices this field applies for all apps on the device. For work profiles, this field applies only to apps in the work profile, and the camera access of apps outside the work profile is unaffected.", "type": "boolean"}, "cellBroadcastsConfigDisabled": {"description": "Whether configuring cell broadcast is disabled.", "type": "boolean"}, "choosePrivateKeyRules": {"description": "Rules for determining apps' access to private keys. See ChoosePrivateKeyRule for details. This must be empty if any application has CERT_SELECTION delegation scope.", "items": {"$ref": "ChoosePrivateKeyRule"}, "type": "array"}, "complianceRules": {"deprecated": true, "description": "Rules declaring which mitigating actions to take when a device is not compliant with its policy. When the conditions for multiple rules are satisfied, all of the mitigating actions for the rules are taken. There is a maximum limit of 100 rules. Use policy enforcement rules instead.", "items": {"$ref": "ComplianceRule"}, "type": "array"}, "createWindowsDisabled": {"description": "Whether creating windows besides app windows is disabled.", "type": "boolean"}, "credentialProviderPolicyDefault": {"description": "Controls which apps are allowed to act as credential providers on Android 14 and above. These apps store credentials, see this (https://developer.android.com/training/sign-in/passkeys) and this (https://developer.android.com/reference/androidx/credentials/CredentialManager) for details. See also credentialProviderPolicy.", "enum": ["CREDENTIAL_PROVIDER_POLICY_DEFAULT_UNSPECIFIED", "CREDENTIAL_PROVIDER_DEFAULT_DISALLOWED", "CREDENTIAL_PROVIDER_DEFAULT_DISALLOWED_EXCEPT_SYSTEM"], "enumDescriptions": ["Unspecified. Defaults to CREDENTIAL_PROVIDER_DEFAULT_DISALLOWED.", "Apps with credentialProviderPolicy unspecified are not allowed to act as a credential provider.", "Apps with credentialProviderPolicy unspecified are not allowed to act as a credential provider except for the OEM default credential providers. OEM default credential providers are always allowed to act as credential providers."], "type": "string"}, "credentialsConfigDisabled": {"description": "Whether configuring user credentials is disabled.", "type": "boolean"}, "crossProfilePolicies": {"$ref": "CrossProfilePolicies", "description": "Cross-profile policies applied on the device."}, "dataRoamingDisabled": {"description": "Whether roaming data services are disabled.", "type": "boolean"}, "debuggingFeaturesAllowed": {"deprecated": true, "description": "Whether the user is allowed to enable debugging features.", "type": "boolean"}, "defaultPermissionPolicy": {"description": "The default permission policy for runtime permission requests.", "enum": ["PERMISSION_POLICY_UNSPECIFIED", "PROMPT", "GRANT", "DENY"], "enumDescriptions": ["Policy not specified. If no policy is specified for a permission at any level, then the PROMPT behavior is used by default.", "Prompt the user to grant a permission.", "Automatically grant a permission.On Android 12 and above, READ_SMS (https://developer.android.com/reference/android/Manifest.permission#READ_SMS) and following sensor-related permissions can only be granted on fully managed devices: ACCESS_FINE_LOCATION (https://developer.android.com/reference/android/Manifest.permission#ACCESS_FINE_LOCATION) ACCESS_BACKGROUND_LOCATION (https://developer.android.com/reference/android/Manifest.permission#ACCESS_BACKGROUND_LOCATION) ACCESS_COARSE_LOCATION (https://developer.android.com/reference/android/Manifest.permission#ACCESS_COARSE_LOCATION) CAMERA (https://developer.android.com/reference/android/Manifest.permission#CAMERA) RECORD_AUDIO (https://developer.android.com/reference/android/Manifest.permission#RECORD_AUDIO) ACTIVITY_RECOGNITION (https://developer.android.com/reference/android/Manifest.permission#ACTIVITY_RECOGNITION) BODY_SENSORS (https://developer.android.com/reference/android/Manifest.permission#BODY_SENSORS)", "Automatically deny a permission."], "type": "string"}, "deviceConnectivityManagement": {"$ref": "DeviceConnectivityManagement", "description": "Covers controls for device connectivity such as Wi-Fi, USB data access, keyboard/mouse connections, and more."}, "deviceOwnerLockScreenInfo": {"$ref": "UserFacingMessage", "description": "The device owner information to be shown on the lock screen."}, "deviceRadioState": {"$ref": "DeviceRadioState", "description": "Covers controls for radio state such as Wi-Fi, bluetooth, and more."}, "displaySettings": {"$ref": "DisplaySettings", "description": "Optional. Controls for the display settings."}, "encryptionPolicy": {"description": "Whether encryption is enabled", "enum": ["ENCRYPTION_POLICY_UNSPECIFIED", "ENABLED_WITHOUT_PASSWORD", "ENABLED_WITH_PASSWORD"], "enumDescriptions": ["This value is ignored, i.e. no encryption required", "Encryption required but no password required to boot", "Encryption required with password required to boot"], "type": "string"}, "ensureVerifyAppsEnabled": {"deprecated": true, "description": "Whether app verification is force-enabled.", "type": "boolean"}, "enterpriseDisplayNameVisibility": {"description": "Optional. Controls whether the enterpriseDisplayName is visible on the device (e.g. lock screen message on company-owned devices).", "enum": ["ENTERPRISE_DISPLAY_NAME_VISIBILITY_UNSPECIFIED", "ENTERPRISE_DISPLAY_NAME_VISIBLE", "ENTERPRISE_DISPLAY_NAME_HIDDEN"], "enumDescriptions": ["Unspecified. Defaults to displaying the enterprise name that's set at the time of device setup. In future, this will default to ENTERPRISE_DISPLAY_NAME_VISIBLE.", "The enterprise display name is visible on the device. Supported on work profiles on Android 7 and above. Supported on fully managed devices on Android 8 and above. A NonComplianceDetail with API_LEVEL is reported if the Android version is less than 7. A NonComplianceDetail with MANAGEMENT_MODE is reported on fully managed devices on Android 7.", "The enterprise display name is hidden on the device."], "type": "string"}, "factoryResetDisabled": {"description": "Whether factory resetting from settings is disabled.", "type": "boolean"}, "frpAdminEmails": {"description": "Email addresses of device administrators for factory reset protection. When the device is factory reset, it will require one of these admins to log in with the Google account email and password to unlock the device. If no admins are specified, the device won't provide factory reset protection.", "items": {"type": "string"}, "type": "array"}, "funDisabled": {"description": "Whether the user is allowed to have fun. Controls whether the Easter egg game in Settings is disabled.", "type": "boolean"}, "installAppsDisabled": {"description": "Whether user installation of apps is disabled.", "type": "boolean"}, "installUnknownSourcesAllowed": {"deprecated": true, "description": "This field has no effect.", "type": "boolean"}, "keyguardDisabled": {"description": "If true, this disables the Lock Screen (https://source.android.com/docs/core/display/multi_display/lock-screen) for primary and/or secondary displays. This policy is supported only in dedicated device management mode.", "type": "boolean"}, "keyguardDisabledFeatures": {"description": "Disabled keyguard customizations, such as widgets.", "items": {"enum": ["KEYGUARD_DISABLED_FEATURE_UNSPECIFIED", "CAMERA", "NOTIFICATIONS", "UNREDACTED_NOTIFICATIONS", "TRUST_AGENTS", "DISABLE_FINGERPRINT", "DISABLE_REMOTE_INPUT", "FACE", "IRIS", "BIOMETRICS", "SHORTCUTS", "ALL_FEATURES"], "enumDescriptions": ["This value is ignored.", "Disable the camera on secure keyguard screens (e.g. PIN).", "Disable showing all notifications on secure keyguard screens.", "Disable unredacted notifications on secure keyguard screens.", "Ignore trust agent state on secure keyguard screens.", "Disable fingerprint sensor on secure keyguard screens.", "On devices running Android 6 and below, disables text entry into notifications on secure keyguard screens. Has no effect on Android 7 and above.", "Disable face authentication on secure keyguard screens.", "Disable iris authentication on secure keyguard screens.", "Disable all biometric authentication on secure keyguard screens.", "Disable all shortcuts on secure keyguard screen on Android 14 and above.", "Disable all current and future keyguard customizations."], "type": "string"}, "type": "array"}, "kioskCustomLauncherEnabled": {"description": "Whether the kiosk custom launcher is enabled. This replaces the home screen with a launcher that locks down the device to the apps installed via the applications setting. Apps appear on a single page in alphabetical order. Use kioskCustomization to further configure the kiosk device behavior.", "type": "boolean"}, "kioskCustomization": {"$ref": "KioskCustomization", "description": "Settings controlling the behavior of a device in kiosk mode. To enable kiosk mode, set kioskCustomLauncherEnabled to true or specify an app in the policy with installType KIOSK."}, "locationMode": {"description": "The degree of location detection enabled.", "enum": ["LOCATION_MODE_UNSPECIFIED", "HIGH_ACCURACY", "SENSORS_ONLY", "BATTERY_SAVING", "OFF", "LOCATION_USER_CHOICE", "LOCATION_ENFORCED", "LOCATION_DISABLED"], "enumDeprecated": [false, true, true, true, true, false, false, false], "enumDescriptions": ["Defaults to LOCATION_USER_CHOICE.", "On Android 8 and below, all location detection methods are enabled, including GPS, networks, and other sensors. On Android 9 and above, this is equivalent to LOCATION_ENFORCED.", "On Android 8 and below, only GPS and other sensors are enabled. On Android 9 and above, this is equivalent to LOCATION_ENFORCED.", "On Android 8 and below, only the network location provider is enabled. On Android 9 and above, this is equivalent to LOCATION_ENFORCED.", "On Android 8 and below, location setting and accuracy are disabled. On Android 9 and above, this is equivalent to LOCATION_DISABLED.", "Location setting is not restricted on the device. No specific behavior is set or enforced.", "Enable location setting on the device. Important: On Android 11 and above, work profiles on company-owned devices cannot directly enforce enabling of location services. When LOCATION_ENFORCED is set, then a NonComplianceDetail with USER_ACTION is reported. Compliance can only be restored once the user manually turns on location services through the device's Settings application. ", "Disable location setting on the device. Important: On Android 11 and above, work profiles on company-owned devices cannot directly enforce disabling of location services. When LOCATION_DISABLED is set, then a nonComplianceDetail with USER_ACTION is reported. Compliance can only be restored once the user manually turns off location services through the device's Settings application. "], "type": "string"}, "longSupportMessage": {"$ref": "UserFacingMessage", "description": "A message displayed to the user in the device administators settings screen."}, "maximumTimeToLock": {"description": "Maximum time in milliseconds for user activity until the device locks. A value of 0 means there is no restriction.", "format": "int64", "type": "string"}, "microphoneAccess": {"description": "Controls the use of the microphone and whether the user has access to the microphone access toggle. This applies only on fully managed devices.", "enum": ["MICROPHONE_ACCESS_UNSPECIFIED", "MICROPHONE_ACCESS_USER_CHOICE", "MICROPHONE_ACCESS_DISABLED", "MICROPHONE_ACCESS_ENFORCED"], "enumDescriptions": ["If unmute_microphone_disabled is true, this is equivalent to MICROPHONE_ACCESS_DISABLED. Otherwise, this is equivalent to MICROPHONE_ACCESS_USER_CHOICE.", "The field unmute_microphone_disabled is ignored. This is the default device behaviour: the microphone on the device is available. On Android 12 and above, the user can use the microphone access toggle.", "The field unmute_microphone_disabled is ignored. The microphone on the device is disabled (for fully managed devices, this applies device-wide).The microphone access toggle has no effect as the microphone is disabled.", "The field unmute_microphone_disabled is ignored. The microphone on the device is available. On devices running Android 12 and above, the user is unable to use the microphone access toggle. On devices which run Android 11 or below, this is equivalent to MICROPHONE_ACCESS_USER_CHOICE."], "type": "string"}, "minimumApiLevel": {"description": "The minimum allowed Android API level.", "format": "int32", "type": "integer"}, "mobileNetworksConfigDisabled": {"description": "Whether configuring mobile networks is disabled.", "type": "boolean"}, "modifyAccountsDisabled": {"description": "Whether adding or removing accounts is disabled.", "type": "boolean"}, "mountPhysicalMediaDisabled": {"description": "Whether the user mounting physical external media is disabled.", "type": "boolean"}, "name": {"description": "The name of the policy in the form enterprises/{enterpriseId}/policies/{policyId}.", "type": "string"}, "networkEscapeHatchEnabled": {"description": "Whether the network escape hatch is enabled. If a network connection can't be made at boot time, the escape hatch prompts the user to temporarily connect to a network in order to refresh the device policy. After applying policy, the temporary network will be forgotten and the device will continue booting. This prevents being unable to connect to a network if there is no suitable network in the last policy and the device boots into an app in lock task mode, or the user is otherwise unable to reach device settings.Note: Setting wifiConfigDisabled to true will override this setting under specific circumstances. Please see wifiConfigDisabled for further details. Setting configureWifi to DISALLOW_CONFIGURING_WIFI will override this setting under specific circumstances. Please see DISALLOW_CONFIGURING_WIFI for further details.", "type": "boolean"}, "networkResetDisabled": {"description": "Whether resetting network settings is disabled.", "type": "boolean"}, "oncCertificateProviders": {"description": "This feature is not generally available.", "items": {"$ref": "OncCertificateProvider"}, "type": "array"}, "openNetworkConfiguration": {"additionalProperties": {"description": "Properties of the object.", "type": "any"}, "description": "Network configuration for the device. See configure networks for more information.", "type": "object"}, "outgoingBeamDisabled": {"description": "Whether using NFC to beam data from apps is disabled.", "type": "boolean"}, "outgoingCallsDisabled": {"description": "Whether outgoing calls are disabled.", "type": "boolean"}, "passwordPolicies": {"description": "Password requirement policies. Different policies can be set for work profile or fully managed devices by setting the password_scope field in the policy.", "items": {"$ref": "PasswordRequirements"}, "type": "array"}, "passwordRequirements": {"$ref": "PasswordRequirements", "deprecated": true, "description": "Password requirements. The field password_requirements.require_password_unlock must not be set. DEPRECATED - Use passwordPolicies.Note:Complexity-based values of PasswordQuality, that is, COMPLEXITY_LOW, COMPLEXITY_MEDIUM, and COMPLEXITY_HIGH, cannot be used here. unified_lock_settings cannot be used here."}, "permissionGrants": {"description": "Explicit permission or group grants or denials for all apps. These values override the default_permission_policy.", "items": {"$ref": "PermissionGrant"}, "type": "array"}, "permittedAccessibilityServices": {"$ref": "PackageNameList", "description": "Specifies permitted accessibility services. If the field is not set, any accessibility service can be used. If the field is set, only the accessibility services in this list and the system's built-in accessibility service can be used. In particular, if the field is set to empty, only the system's built-in accessibility servicess can be used. This can be set on fully managed devices and on work profiles. When applied to a work profile, this affects both the personal profile and the work profile."}, "permittedInputMethods": {"$ref": "PackageNameList", "description": "If present, only the input methods provided by packages in this list are permitted. If this field is present, but the list is empty, then only system input methods are permitted."}, "persistentPreferredActivities": {"description": "Default intent handler activities.", "items": {"$ref": "PersistentPreferredActivity"}, "type": "array"}, "personalUsagePolicies": {"$ref": "PersonalUsagePolicies", "description": "Policies managing personal usage on a company-owned device."}, "playStoreMode": {"description": "This mode controls which apps are available to the user in the Play Store and the behavior on the device when apps are removed from the policy.", "enum": ["PLAY_STORE_MODE_UNSPECIFIED", "WHITELIST", "BLACKLIST"], "enumDescriptions": ["Unspecified. Defaults to WHITELIST.", "Only apps that are in the policy are available and any app not in the policy will be automatically uninstalled from the device.", "All apps are available and any app that should not be on the device should be explicitly marked as 'BLOCKED' in the applications policy."], "type": "string"}, "policyEnforcementRules": {"description": "Rules that define the behavior when a particular policy can not be applied on device", "items": {"$ref": "PolicyEnforcementRule"}, "type": "array"}, "preferentialNetworkService": {"description": "Controls whether preferential network service is enabled on the work profile or on fully managed devices. For example, an organization may have an agreement with a carrier that all of the work data from its employees' devices will be sent via a network service dedicated for enterprise use. An example of a supported preferential network service is the enterprise slice on 5G networks. This policy has no effect if preferentialNetworkServiceSettings or ApplicationPolicy.preferentialNetworkId is set on devices running Android 13 or above.", "enum": ["PREFERENTIAL_NETWORK_SERVICE_UNSPECIFIED", "PREFERENTIAL_NETWORK_SERVICE_DISABLED", "PREFERENTIAL_NETWORK_SERVICE_ENABLED"], "enumDescriptions": ["Unspecified. Defaults to PREFERENTIAL_NETWORK_SERVICES_DISABLED.", "Preferential network service is disabled on the work profile.", "Preferential network service is enabled on the work profile. This setting is only supported on work profiles on devices running Android 12 or above. Starting with Android 13, fully managed devices are also supported."], "type": "string"}, "printingPolicy": {"description": "Optional. Controls whether printing is allowed. This is supported on devices running Android 9 and above. .", "enum": ["PRINTING_POLICY_UNSPECIFIED", "PRINTING_DISALLOWED", "PRINTING_ALLOWED"], "enumDescriptions": ["Unspecified. Defaults to PRINTING_ALLOWED.", "Printing is disallowed. A NonComplianceDetail with API_LEVEL is reported if the Android version is less than 9.", "Printing is allowed."], "type": "string"}, "privateKeySelectionEnabled": {"description": "Allows showing UI on a device for a user to choose a private key alias if there are no matching rules in ChoosePrivateKeyRules. For devices below Android P, setting this may leave enterprise keys vulnerable. This value will have no effect if any application has CERT_SELECTION delegation scope.", "type": "boolean"}, "recommendedGlobalProxy": {"$ref": "ProxyInfo", "description": "The network-independent global HTTP proxy. Typically proxies should be configured per-network in open_network_configuration. However for unusual configurations like general internal filtering a global HTTP proxy may be useful. If the proxy is not accessible, network access may break. The global proxy is only a recommendation and some apps may ignore it."}, "removeUserDisabled": {"description": "Whether removing other users is disabled.", "type": "boolean"}, "safeBootDisabled": {"deprecated": true, "description": "Whether rebooting the device into safe boot is disabled.", "type": "boolean"}, "screenCaptureDisabled": {"description": "Whether screen capture is disabled.", "type": "boolean"}, "setUserIconDisabled": {"description": "Whether changing the user icon is disabled. The setting has effect only on fully managed devices.", "type": "boolean"}, "setWallpaperDisabled": {"description": "Whether changing the wallpaper is disabled.", "type": "boolean"}, "setupActions": {"description": "Action to take during the setup process. At most one action may be specified.", "items": {"$ref": "SetupAction"}, "type": "array"}, "shareLocationDisabled": {"description": "Whether location sharing is disabled. share_location_disabled is supported for both fully managed devices and personally owned work profiles.", "type": "boolean"}, "shortSupportMessage": {"$ref": "UserFacingMessage", "description": "A message displayed to the user in the settings screen wherever functionality has been disabled by the admin. If the message is longer than 200 characters it may be truncated."}, "skipFirstUseHintsEnabled": {"description": "Flag to skip hints on the first use. Enterprise admin can enable the system recommendation for apps to skip their user tutorial and other introductory hints on first start-up.", "type": "boolean"}, "smsDisabled": {"description": "Whether sending and receiving SMS messages is disabled.", "type": "boolean"}, "statusBarDisabled": {"deprecated": true, "description": "Whether the status bar is disabled. This disables notifications, quick settings, and other screen overlays that allow escape from full-screen mode. DEPRECATED. To disable the status bar on a kiosk device, use InstallType KIOSK or kioskCustomLauncherEnabled.", "type": "boolean"}, "statusReportingSettings": {"$ref": "StatusReportingSettings", "description": "Status reporting settings"}, "stayOnPluggedModes": {"description": "The battery plugged in modes for which the device stays on. When using this setting, it is recommended to clear maximum_time_to_lock so that the device doesn't lock itself while it stays on.", "items": {"enum": ["BATTERY_PLUGGED_MODE_UNSPECIFIED", "AC", "USB", "WIRELESS"], "enumDescriptions": ["This value is ignored.", "Power source is an AC charger.", "Power source is a USB port.", "Power source is wireless."], "type": "string"}, "type": "array"}, "systemUpdate": {"$ref": "SystemUpdate", "description": "The system update policy, which controls how OS updates are applied. If the update type is WINDOWED, the update window will automatically apply to Play app updates as well.Note: Google Play system updates (https://source.android.com/docs/core/ota/modular-system) (also called Mainline updates) are automatically downloaded and require a device reboot to be installed. Refer to the mainline section in Manage system updates (https://developer.android.com/work/dpc/system-updates#mainline) for further details."}, "tetheringConfigDisabled": {"deprecated": true, "description": "Whether configuring tethering and portable hotspots is disabled. If tetheringSettings is set to anything other than TETHERING_SETTINGS_UNSPECIFIED, this setting is ignored.", "type": "boolean"}, "uninstallAppsDisabled": {"description": "Whether user uninstallation of applications is disabled. This prevents apps from being uninstalled, even those removed using applications", "type": "boolean"}, "unmuteMicrophoneDisabled": {"deprecated": true, "description": "If microphone_access is set to any value other than MICROPHONE_ACCESS_UNSPECIFIED, this has no effect. Otherwise this field controls whether microphones are disabled: If true, all microphones are disabled, otherwise they are available. This is available only on fully managed devices.", "type": "boolean"}, "usageLog": {"$ref": "UsageLog", "description": "Configuration of device activity logging."}, "usbFileTransferDisabled": {"deprecated": true, "description": "Whether transferring files over USB is disabled. This is supported only on company-owned devices.", "type": "boolean"}, "usbMassStorageEnabled": {"deprecated": true, "description": "Whether USB storage is enabled. Deprecated.", "type": "boolean"}, "version": {"description": "The version of the policy. This is a read-only field. The version is incremented each time the policy is updated.", "format": "int64", "type": "string"}, "vpnConfigDisabled": {"description": "Whether configuring VPN is disabled.", "type": "boolean"}, "wifiConfigDisabled": {"deprecated": true, "description": "Whether configuring Wi-Fi networks is disabled. Supported on fully managed devices and work profiles on company-owned devices. For fully managed devices, setting this to true removes all configured networks and retains only the networks configured using openNetworkConfiguration. For work profiles on company-owned devices, existing configured networks are not affected and the user is not allowed to add, remove, or modify Wi-Fi networks. If configureWifi is set to anything other than CONFIGURE_WIFI_UNSPECIFIED, this setting is ignored. Note: If a network connection can't be made at boot time and configuring Wi-Fi is disabled then network escape hatch will be shown in order to refresh the device policy (see networkEscapeHatchEnabled).", "type": "boolean"}, "wifiConfigsLockdownEnabled": {"deprecated": true, "description": "This is deprecated.", "type": "boolean"}, "wipeDataFlags": {"description": "Optional. Wipe flags to indicate what data is wiped when a device or profile wipe is triggered due to any reason (for example, non-compliance). This does not apply to the enterprises.devices.delete method. . This list must not have duplicates.", "items": {"enum": ["WIPE_DATA_FLAG_UNSPECIFIED", "WIPE_ESIMS"], "enumDescriptions": ["This value must not be used.", "For company-owned devices, setting this in wipeDataFlags will remove all eSIMs on the device when wipe is triggered due to any reason. On personally-owned devices, this will remove only managed eSIMs on the device. (eSIMs which are added via the ADD_ESIM command). This is supported on devices running Android 15 and above. A NonComplianceDetail with API_LEVEL is reported if the Android version is less than 15."], "type": "string"}, "type": "array"}, "workAccountSetupConfig": {"$ref": "WorkAccountSetupConfig", "description": "Optional. Controls the work account setup configuration, such as details of whether a Google authenticated account is required."}}, "type": "object"}, "PolicyEnforcementRule": {"description": "A rule that defines the actions to take if a device or work profile is not compliant with the policy specified in settingName. In the case of multiple matching or multiple triggered enforcement rules, a merge will occur with the most severe action being taken. However, all triggered rules are still kept track of: this includes initial trigger time and all associated non-compliance details. In the situation where the most severe enforcement rule is satisfied, the next most appropriate action is applied.", "id": "PolicyEnforcementRule", "properties": {"blockAction": {"$ref": "BlockAction", "description": "An action to block access to apps and data on a company owned device or in a work profile. This action also triggers a user-facing notification with information (where possible) on how to correct the compliance issue. Note: wipeAction must also be specified."}, "settingName": {"description": "The top-level policy to enforce. For example, applications or passwordPolicies.", "type": "string"}, "wipeAction": {"$ref": "WipeAction", "description": "An action to reset a company owned device or delete a work profile. Note: blockAction must also be specified."}}, "type": "object"}, "PostureDetail": {"description": "Additional details regarding the security posture of the device.", "id": "PostureDetail", "properties": {"advice": {"description": "Corresponding admin-facing advice to mitigate this security risk and improve the security posture of the device.", "items": {"$ref": "UserFacingMessage"}, "type": "array"}, "securityRisk": {"description": "A specific security risk that negatively affects the security posture of the device.", "enum": ["SECURITY_RISK_UNSPECIFIED", "UNKNOWN_OS", "COMPROMISED_OS", "HARDWARE_BACKED_EVALUATION_FAILED"], "enumDescriptions": ["Unspecified.", "Play Integrity API detects that the device is running an unknown OS (basicIntegrity check succeeds but ctsProfileMatch fails).", "Play Integrity API detects that the device is running a compromised OS (basicIntegrity check fails).", "Play Integrity API detects that the device does not have a strong guarantee of system integrity, if the MEETS_STRONG_INTEGRITY label doesn't show in the device integrity field (https://developer.android.com/google/play/integrity/verdicts#device-integrity-field)."], "type": "string"}}, "type": "object"}, "PowerManagementEvent": {"description": "A power management event.", "id": "PowerManagementEvent", "properties": {"batteryLevel": {"description": "For BATTERY_LEVEL_COLLECTED events, the battery level as a percentage.", "format": "float", "type": "number"}, "createTime": {"description": "The creation time of the event.", "format": "google-datetime", "type": "string"}, "eventType": {"description": "Event type.", "enum": ["POWER_MANAGEMENT_EVENT_TYPE_UNSPECIFIED", "BATTERY_LEVEL_COLLECTED", "POWER_CONNECTED", "POWER_DISCONNECTED", "BATTERY_LOW", "BATTERY_OKAY", "BOOT_COMPLETED", "SHUTDOWN"], "enumDescriptions": ["Unspecified. No events have this type.", "Battery level was measured.", "The device started charging.", "The device stopped charging.", "The device entered low-power mode.", "The device exited low-power mode.", "The device booted.", "The device shut down."], "type": "string"}}, "type": "object"}, "PreferentialNetworkServiceConfig": {"description": "Individual preferential network service configuration.", "id": "PreferentialNetworkServiceConfig", "properties": {"fallbackToDefaultConnection": {"description": "Optional. Whether fallback to the device-wide default network is allowed. If this is set to FALLBACK_TO_DEFAULT_CONNECTION_ALLOWED, then nonMatchingNetworks must not be set to NON_MATCHING_NETWORKS_DISALLOWED, the policy will be rejected otherwise. Note: If this is set to FALLBACK_TO_DEFAULT_CONNECTION_DISALLOWED, applications are not able to access the internet if the 5G slice is not available.", "enum": ["FALLBACK_TO_DEFAULT_CONNECTION_UNSPECIFIED", "FALLBACK_TO_DEFAULT_CONNECTION_ALLOWED", "FALLBACK_TO_DEFAULT_CONNECTION_DISALLOWED"], "enumDescriptions": ["Unspecified. Defaults to FALLBACK_TO_DEFAULT_CONNECTION_ALLOWED.", "Fallback to default connection is allowed. If this is set, nonMatchingNetworks must not be set to NON_MATCHING_NETWORKS_DISALLOWED, the policy will be rejected otherwise.", "Fallback to default connection is not allowed."], "type": "string"}, "nonMatchingNetworks": {"description": "Optional. Whether apps this configuration applies to are blocked from using networks other than the preferential service. If this is set to NON_MATCHING_NETWORKS_DISALLOWED, then fallbackToDefaultConnection must be set to FALLBACK_TO_DEFAULT_CONNECTION_DISALLOWED.", "enum": ["NON_MATCHING_NETWORKS_UNSPECIFIED", "NON_MATCHING_NETWORKS_ALLOWED", "NON_MATCHING_NETWORKS_DISALLOWED"], "enumDescriptions": ["Unspecified. Defaults to NON_MATCHING_NETWORKS_ALLOWED.", "Apps this configuration applies to are allowed to use networks other than the preferential service.", "Apps this configuration applies to are disallowed from using other networks than the preferential service. This can be set on Android 14 and above. A NonComplianceDetail with API_LEVEL is reported if the Android version is less than 14. If this is set, fallbackToDefaultConnection must be set to FALLBACK_TO_DEFAULT_CONNECTION_DISALLOWED, the policy will be rejected otherwise."], "type": "string"}, "preferentialNetworkId": {"description": "Required. Preferential network identifier. This must not be set to NO_PREFERENTIAL_NETWORK or PREFERENTIAL_NETWORK_ID_UNSPECIFIED, the policy will be rejected otherwise.", "enum": ["PREFERENTIAL_NETWORK_ID_UNSPECIFIED", "NO_PREFERENTIAL_NETWORK", "PREFERENTIAL_NETWORK_ID_ONE", "PREFERENTIAL_NETWORK_ID_TWO", "PREFERENTIAL_NETWORK_ID_THREE", "PREFERENTIAL_NETWORK_ID_FOUR", "PREFERENTIAL_NETWORK_ID_FIVE"], "enumDescriptions": ["Whether this value is valid and what it means depends on where it is used, and this is documented on the relevant fields.", "Application does not use any preferential network.", "Preferential network identifier 1.", "Preferential network identifier 2.", "Preferential network identifier 3.", "Preferential network identifier 4.", "Preferential network identifier 5."], "type": "string"}}, "type": "object"}, "PreferentialNetworkServiceSettings": {"description": "Preferential network service settings.", "id": "PreferentialNetworkServiceSettings", "properties": {"defaultPreferentialNetworkId": {"description": "Required. Default preferential network ID for the applications that are not in applications or if ApplicationPolicy.preferentialNetworkId is set to PREFERENTIAL_NETWORK_ID_UNSPECIFIED. There must be a configuration for the specified network ID in preferentialNetworkServiceConfigs, unless this is set to NO_PREFERENTIAL_NETWORK. If set to PREFERENTIAL_NETWORK_ID_UNSPECIFIED or unset, this defaults to NO_PREFERENTIAL_NETWORK. Note: If the default preferential network is misconfigured, applications with no ApplicationPolicy.preferentialNetworkId set are not able to access the internet. This setting does not apply to the following critical apps: com.google.android.apps.work.clouddpc com.google.android.gmsApplicationPolicy.preferentialNetworkId can still be used to configure the preferential network for them.", "enum": ["PREFERENTIAL_NETWORK_ID_UNSPECIFIED", "NO_PREFERENTIAL_NETWORK", "PREFERENTIAL_NETWORK_ID_ONE", "PREFERENTIAL_NETWORK_ID_TWO", "PREFERENTIAL_NETWORK_ID_THREE", "PREFERENTIAL_NETWORK_ID_FOUR", "PREFERENTIAL_NETWORK_ID_FIVE"], "enumDescriptions": ["Whether this value is valid and what it means depends on where it is used, and this is documented on the relevant fields.", "Application does not use any preferential network.", "Preferential network identifier 1.", "Preferential network identifier 2.", "Preferential network identifier 3.", "Preferential network identifier 4.", "Preferential network identifier 5."], "type": "string"}, "preferentialNetworkServiceConfigs": {"description": "Required. Preferential network service configurations which enables having multiple enterprise slices. There must not be multiple configurations with the same preferentialNetworkId. If a configuration is not referenced by any application by setting ApplicationPolicy.preferentialNetworkId or by setting defaultPreferentialNetworkId, it will be ignored. For devices on 4G networks, enterprise APN needs to be configured additionally to set up data call for preferential network service. These APNs can be added using apnPolicy.", "items": {"$ref": "PreferentialNetworkServiceConfig"}, "type": "array"}}, "type": "object"}, "ProvisioningInfo": {"description": "Information about a device that is available during setup.", "id": "ProvisioningInfo", "properties": {"apiLevel": {"description": "The API level of the Android platform version running on the device.", "format": "int32", "type": "integer"}, "authenticatedUserEmail": {"description": "The email address of the authenticated user (only present for Google Account provisioning method).", "type": "string"}, "brand": {"description": "The brand of the device. For example, Google.", "type": "string"}, "enterprise": {"description": "The name of the enterprise in the form enterprises/{enterprise}.", "type": "string"}, "imei": {"description": "For corporate-owned devices, IMEI number of the GSM device. For example, A1000031212.", "type": "string"}, "managementMode": {"description": "The management mode of the device or profile.", "enum": ["MANAGEMENT_MODE_UNSPECIFIED", "DEVICE_OWNER", "PROFILE_OWNER"], "enumDescriptions": ["This value is disallowed.", "Device owner. Android Device Policy has full control over the device.", "Profile owner. Android Device Policy has control over a managed profile on the device."], "type": "string"}, "meid": {"description": "For corporate-owned devices, MEID number of the CDMA device. For example, A00000292788E1.", "type": "string"}, "model": {"description": "The model of the device. For example, Asus Nexus 7.", "type": "string"}, "name": {"description": "The name of this resource in the form provisioningInfo/{provisioning_info}.", "type": "string"}, "ownership": {"description": "Ownership of the managed device.", "enum": ["OWNERSHIP_UNSPECIFIED", "COMPANY_OWNED", "PERSONALLY_OWNED"], "enumDescriptions": ["Ownership is unspecified.", "Device is company-owned.", "<PERSON><PERSON> is personally-owned."], "type": "string"}, "serialNumber": {"description": "For corporate-owned devices, The device serial number.", "type": "string"}}, "type": "object"}, "ProxyInfo": {"description": "Configuration info for an HTTP proxy. For a direct proxy, set the host, port, and excluded_hosts fields. For a PAC script proxy, set the pac_uri field.", "id": "ProxyInfo", "properties": {"excludedHosts": {"description": "For a direct proxy, the hosts for which the proxy is bypassed. The host names may contain wildcards such as *.example.com.", "items": {"type": "string"}, "type": "array"}, "host": {"description": "The host of the direct proxy.", "type": "string"}, "pacUri": {"description": "The URI of the PAC script used to configure the proxy.", "type": "string"}, "port": {"description": "The port of the direct proxy.", "format": "int32", "type": "integer"}}, "type": "object"}, "RemoteLockEvent": {"description": "The device or profile has been remotely locked via the LOCK command.", "id": "RemoteLockEvent", "properties": {"adminPackageName": {"description": "Package name of the admin app requesting the change.", "type": "string"}, "adminUserId": {"description": "User ID of the admin app from the which the change was requested.", "format": "int32", "type": "integer"}, "targetUserId": {"description": "User ID in which the change was requested in.", "format": "int32", "type": "integer"}}, "type": "object"}, "RemoveEsimParams": {"description": "Parameters associated with the REMOVE_ESIM command to remove an eSIM profile from the device.", "id": "RemoveEsimParams", "properties": {"iccId": {"description": "Required. ICC ID of the eSIM profile to be deleted.", "type": "string"}}, "type": "object"}, "RemovePolicyApplicationsRequest": {"description": "Request to remove ApplicationPolicy objects in the given policy.", "id": "RemovePolicyApplicationsRequest", "properties": {"packageNames": {"description": "Required. Package names to be removed. Entries that are not found are ignored. There must be at least one entry in package_names.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "RemovePolicyApplicationsResponse": {"description": "Response to a request to remove ApplicationPolicy objects in the given policy.", "id": "RemovePolicyApplicationsResponse", "properties": {"policy": {"$ref": "Policy", "description": "The updated policy after ApplicationPolicy objects have been removed."}}, "type": "object"}, "RequestDeviceInfoParams": {"description": "Parameters associated with the REQUEST_DEVICE_INFO command to get device related information.", "id": "RequestDeviceInfoParams", "properties": {"deviceInfo": {"description": "Required. Type of device information to be requested.", "enum": ["DEVICE_INFO_UNSPECIFIED", "EID"], "enumDescriptions": ["This value is disallowed.", "Request the identifier for eSIM. The user will be asked to approve the disclosure of the information before the result can be returned. If the user doesn't approve the disclosure, USER_DECLINED will be returned. This is supported only for personally owned devices with work profiles and Android versions 13 and above."], "type": "string"}}, "type": "object"}, "RequestDeviceInfoStatus": {"description": "Status of the REQUEST_DEVICE_INFO command.", "id": "RequestDeviceInfoStatus", "properties": {"eidInfo": {"$ref": "EidInfo", "description": "Information related to the EIDs of the device."}, "status": {"description": "Output only. Status of a REQUEST_DEVICE_INFO command.", "enum": ["STATUS_UNSPECIFIED", "SUCCEEDED", "PENDING_USER_ACTION", "USER_DECLINED", "UNSUPPORTED"], "enumDescriptions": ["Unspecified. This value is not used.", "Device information has been successfully delivered.", "The user has not completed the actions required to share device information.", "The user declined sharing device information.", "The requested device info is not supported on this device, e.g. eSIM is not supported on the device."], "readOnly": true, "type": "string"}}, "type": "object"}, "ScreenBrightnessSettings": {"description": "Controls for the screen brightness settings.", "id": "ScreenBrightnessSettings", "properties": {"screenBrightness": {"description": "Optional. The screen brightness between 1 and 255 where 1 is the lowest and 255 is the highest brightness. A value of 0 (default) means no screen brightness set. Any other value is rejected. screenBrightnessMode must be either BRIGHTNESS_AUTOMATIC or BRIGHTNESS_FIXED to set this. Supported on Android 9 and above on fully managed devices. A NonComplianceDetail with API_LEVEL is reported if the Android version is less than 9. Supported on work profiles on company-owned devices on Android 15 and above.", "format": "int32", "type": "integer"}, "screenBrightnessMode": {"description": "Optional. Controls the screen brightness mode.", "enum": ["SCREEN_BRIGHTNESS_MODE_UNSPECIFIED", "BRIGHTNESS_USER_CHOICE", "BRIGHTNESS_AUTOMATIC", "BRIGHTNESS_FIXED"], "enumDescriptions": ["Unspecified. Defaults to BRIGHTNESS_USER_CHOICE.", "The user is allowed to configure the screen brightness. screenBrightness must not be set.", "The screen brightness mode is automatic in which the brightness is automatically adjusted and the user is not allowed to configure the screen brightness. screenBrightness can still be set and it is taken into account while the brightness is automatically adjusted. Supported on Android 9 and above on fully managed devices. A NonComplianceDetail with API_LEVEL is reported if the Android version is less than 9. Supported on work profiles on company-owned devices on Android 15 and above.", "The screen brightness mode is fixed in which the brightness is set to screenBrightness and the user is not allowed to configure the screen brightness. screenBrightness must be set. Supported on Android 9 and above on fully managed devices. A NonComplianceDetail with API_LEVEL is reported if the Android version is less than 9. Supported on work profiles on company-owned devices on Android 15 and above."], "type": "string"}}, "type": "object"}, "ScreenTimeoutSettings": {"description": "Controls the screen timeout settings.", "id": "ScreenTimeoutSettings", "properties": {"screenTimeout": {"description": "Optional. Controls the screen timeout duration. The screen timeout duration must be greater than 0, otherwise it is rejected. Additionally, it should not be greater than maximumTimeToLock, otherwise the screen timeout is set to maximumTimeToLock and a NonComplianceDetail with INVALID_VALUE reason and SCREEN_TIMEOUT_GREATER_THAN_MAXIMUM_TIME_TO_LOCK specific reason is reported. If the screen timeout is less than a certain lower bound, it is set to the lower bound. The lower bound may vary across devices. If this is set, screenTimeoutMode must be SCREEN_TIMEOUT_ENFORCED. Supported on Android 9 and above on fully managed devices. A NonComplianceDetail with API_LEVEL is reported if the Android version is less than 9. Supported on work profiles on company-owned devices on Android 15 and above.", "format": "google-duration", "type": "string"}, "screenTimeoutMode": {"description": "Optional. Controls whether the user is allowed to configure the screen timeout.", "enum": ["SCREEN_TIMEOUT_MODE_UNSPECIFIED", "SCREEN_TIMEOUT_USER_CHOICE", "SCREEN_TIMEOUT_ENFORCED"], "enumDescriptions": ["Unspecified. Defaults to SCREEN_TIMEOUT_USER_CHOICE.", "The user is allowed to configure the screen timeout. screenTimeout must not be set.", "The screen timeout is set to screenTimeout and the user is not allowed to configure the timeout. screenTimeout must be set. Supported on Android 9 and above on fully managed devices. A NonComplianceDetail with API_LEVEL is reported if the Android version is less than 9. Supported on work profiles on company-owned devices on Android 15 and above."], "type": "string"}}, "type": "object"}, "SecurityPosture": {"description": "The security posture of the device, as determined by the current device state and the policies applied.", "id": "SecurityPosture", "properties": {"devicePosture": {"description": "Device's security posture value.", "enum": ["POSTURE_UNSPECIFIED", "SECURE", "AT_RISK", "POTENTIALLY_COMPROMISED"], "enumDescriptions": ["Unspecified. There is no posture detail for this posture value.", "This device is secure.", "This device may be more vulnerable to malicious actors than is recommended for use with corporate data.", "This device may be compromised and corporate data may be accessible to unauthorized actors."], "type": "string"}, "postureDetails": {"description": "Additional details regarding the security posture of the device.", "items": {"$ref": "PostureDetail"}, "type": "array"}}, "type": "object"}, "SetupAction": {"description": "An action executed during setup.", "id": "SetupAction", "properties": {"description": {"$ref": "UserFacingMessage", "description": "Description of this action."}, "launchApp": {"$ref": "LaunchAppAction", "description": "An action to launch an app. The app will be launched with an intent containing an extra with key com.google.android.apps.work.clouddpc.EXTRA_LAUNCHED_AS_SETUP_ACTION set to the boolean value true to indicate that this is a setup action flow. If SetupAction references an app, the corresponding installType in the application policy must be set as REQUIRED_FOR_SETUP or said setup will fail."}, "title": {"$ref": "UserFacingMessage", "description": "Title of this action."}}, "type": "object"}, "SigninDetail": {"description": "A resource containing sign in details for an enterprise. Use enterprises to manage SigninDetails for a given enterprise.For an enterprise, we can have any number of SigninDetails that is uniquely identified by combination of the following three fields (signin_url, allow_personal_usage, token_tag). One cannot create two SigninDetails with the same (signin_url, allow_personal_usage, token_tag). (token_tag is an optional field).Patch: The operation updates the current list of SigninDetails with the new list of SigninDetails. If the stored SigninDetail configuration is passed, it returns the same signin_enrollment_token and qr_code. If we pass multiple identical SigninDetail configurations that are not stored, it will store the first one amongst those SigninDetail configurations. if the configuration already exists we cannot request it more than once in a particular patch API call, otherwise it will give a duplicate key error and the whole operation will fail. If we remove certain SigninDetail configuration from the request then it will get removed from the storage. We can then request another signin_enrollment_token and qr_code for the same SigninDetail configuration.", "id": "SigninDetail", "properties": {"allowPersonalUsage": {"description": "Controls whether personal usage is allowed on a device provisioned with this enrollment token.For company-owned devices: Enabling personal usage allows the user to set up a work profile on the device. Disabling personal usage requires the user provision the device as a fully managed device.For personally-owned devices: Enabling personal usage allows the user to set up a work profile on the device. Disabling personal usage will prevent the device from provisioning. Personal usage cannot be disabled on personally-owned device.", "enum": ["ALLOW_PERSONAL_USAGE_UNSPECIFIED", "PERSONAL_USAGE_ALLOWED", "PERSONAL_USAGE_DISALLOWED", "PERSONAL_USAGE_DISALLOWED_USERLESS"], "enumDescriptions": ["Personal usage restriction is not specified", "Personal usage is allowed", "Personal usage is disallowed", "Device is not associated with a single user, and thus both personal usage and corporate identity authentication are not expected. Important: This setting is mandatory for dedicated device enrollment and it is a breaking change. This change needs to be implemented before January 2025.For additional details see the dedicated device provisioning guide (https://developers.google.com/android/management/provision-device#company-owned_devices_for_work_use_only). "], "type": "string"}, "defaultStatus": {"description": "Optional. Whether the sign-in URL should be used by default for the enterprise. The SigninDetail with defaultStatus set to SIGNIN_DETAIL_IS_DEFAULT is used for Google account enrollment method. Only one of an enterprise's signinDetails can have defaultStatus set to SIGNIN_DETAIL_IS_DEFAULT. If an Enterprise has at least one signinDetails and none of them have defaultStatus set to SIGNIN_DETAIL_IS_DEFAULT then the first one from the list is selected and has set defaultStatus to SIGNIN_DETAIL_IS_DEFAULT. If no signinDetails specified for the Enterprise then the Google Account device enrollment will fail.", "enum": ["SIGNIN_DETAIL_DEFAULT_STATUS_UNSPECIFIED", "SIGNIN_DETAIL_IS_DEFAULT", "SIGNIN_DETAIL_IS_NOT_DEFAULT"], "enumDescriptions": ["Equivalent to SIGNIN_DETAIL_IS_NOT_DEFAULT.", "The sign-in URL will be used by default for the enterprise.", "The sign-in URL will not be used by default for the enterprise."], "type": "string"}, "qrCode": {"description": "A JSON string whose UTF-8 representation can be used to generate a QR code to enroll a device with this enrollment token. To enroll a device using NFC, the NFC record must contain a serialized java.util.Properties representation of the properties in the JSON. This is a read-only field generated by the server.", "type": "string"}, "signinEnrollmentToken": {"description": "An enterprise wide enrollment token used to trigger custom sign-in flow. This is a read-only field generated by the server.", "type": "string"}, "signinUrl": {"description": "Sign-in URL for authentication when device is provisioned with a sign-in enrollment token. The sign-in endpoint should finish authentication flow with a URL in the form of https://enterprise.google.com/android/enroll?et= for a successful login, or https://enterprise.google.com/android/enroll/invalid for a failed login.", "type": "string"}, "tokenTag": {"description": "An EMM-specified metadata to distinguish between instances of SigninDetail.", "type": "string"}}, "type": "object"}, "SignupUrl": {"description": "An enterprise signup URL.", "id": "SignupUrl", "properties": {"name": {"description": "The name of the resource. Use this value in the signupUrl field when calling enterprises.create to complete the enterprise signup flow.", "type": "string"}, "url": {"description": "A URL where an enterprise admin can register their enterprise. The page can't be rendered in an iframe.", "type": "string"}}, "type": "object"}, "SoftwareInfo": {"description": "Information about device software.", "id": "SoftwareInfo", "properties": {"androidBuildNumber": {"description": "Android build ID string meant for displaying to the user. For example, shamu-userdebug 6.0.1 MOB30I 2756745 dev-keys.", "type": "string"}, "androidBuildTime": {"description": "Build time.", "format": "google-datetime", "type": "string"}, "androidDevicePolicyVersionCode": {"description": "The Android Device Policy app version code.", "format": "int32", "type": "integer"}, "androidDevicePolicyVersionName": {"description": "The Android Device Policy app version as displayed to the user.", "type": "string"}, "androidVersion": {"description": "The user-visible Android version string. For example, 6.0.1.", "type": "string"}, "bootloaderVersion": {"description": "The system bootloader version number, e.g. 0.6.7.", "type": "string"}, "deviceBuildSignature": {"description": "SHA-256 hash of android.content.pm.Signature (https://developer.android.com/reference/android/content/pm/Signature.html) associated with the system package, which can be used to verify that the system build hasn't been modified.", "type": "string"}, "deviceKernelVersion": {"description": "Kernel version, for example, ********-g103d848.", "type": "string"}, "primaryLanguageCode": {"description": "An IETF BCP 47 language code for the primary locale on the device.", "type": "string"}, "securityPatchLevel": {"description": "Security patch level, e.g. 2016-05-01.", "type": "string"}, "systemUpdateInfo": {"$ref": "SystemUpdateInfo", "description": "Information about a potential pending system update."}}, "type": "object"}, "SpecificNonComplianceContext": {"description": "Additional context for SpecificNonComplianceReason.", "id": "SpecificNonComplianceContext", "properties": {"oncWifiContext": {"$ref": "OncWifiContext", "description": "Additional context for non-compliance related to Wi-Fi configuration. See ONC_WIFI_INVALID_VALUE and ONC_WIFI_API_LEVEL"}, "passwordPoliciesContext": {"$ref": "PasswordPoliciesContext", "description": "Additional context for non-compliance related to password policies. See PASSWORD_POLICIES_PASSWORD_EXPIRED and PASSWORD_POLICIES_PASSWORD_NOT_SUFFICIENT."}}, "type": "object"}, "StartLostModeParams": {"description": "Parameters associated with the START_LOST_MODE command to put the device into lost mode. At least one of the parameters, not including the organization name, must be provided in order for the device to be put into lost mode.", "id": "StartLostModeParams", "properties": {"lostEmailAddress": {"description": "The email address displayed to the user when the device is in lost mode.", "type": "string"}, "lostMessage": {"$ref": "UserFacingMessage", "description": "The message displayed to the user when the device is in lost mode."}, "lostOrganization": {"$ref": "UserFacingMessage", "description": "The organization name displayed to the user when the device is in lost mode."}, "lostPhoneNumber": {"$ref": "UserFacingMessage", "description": "The phone number that will be called when the device is in lost mode and the call owner button is tapped."}, "lostStreetAddress": {"$ref": "UserFacingMessage", "description": "The street address displayed to the user when the device is in lost mode."}}, "type": "object"}, "StartLostModeStatus": {"description": "Status of the START_LOST_MODE command to put the device into lost mode.", "id": "StartLostModeStatus", "properties": {"status": {"description": "The status. See StartLostModeStatus.", "enum": ["STATUS_UNSPECIFIED", "SUCCESS", "RESET_PASSWORD_RECENTLY", "USER_EXIT_LOST_MODE_RECENTLY", "ALREADY_IN_LOST_MODE"], "enumDescriptions": ["Unspecified. This value is not used.", "The device was put into lost mode.", "The device could not be put into lost mode because the admin reset the device's password recently.", "The device could not be put into lost mode because the user exited lost mode recently.", "The device is already in lost mode."], "type": "string"}}, "type": "object"}, "Status": {"description": "The Status type defines a logical error model that is suitable for different programming environments, including REST APIs and RPC APIs. It is used by gRPC (https://github.com/grpc). Each Status message contains three pieces of data: error code, error message, and error details.You can find out more about this error model and how to work with it in the API Design Guide (https://cloud.google.com/apis/design/errors).", "id": "Status", "properties": {"code": {"description": "The status code, which should be an enum value of google.rpc.Code.", "format": "int32", "type": "integer"}, "details": {"description": "A list of messages that carry the error details. There is a common set of message types for APIs to use.", "items": {"additionalProperties": {"description": "Properties of the object. Contains field @type with type URL.", "type": "any"}, "type": "object"}, "type": "array"}, "message": {"description": "A developer-facing error message, which should be in English. Any user-facing error message should be localized and sent in the google.rpc.Status.details field, or localized by the client.", "type": "string"}}, "type": "object"}, "StatusReportingSettings": {"description": "Settings controlling the behavior of status reports.", "id": "StatusReportingSettings", "properties": {"applicationReportingSettings": {"$ref": "ApplicationReportingSettings", "description": "Application reporting settings. Only applicable if application_reports_enabled is true."}, "applicationReportsEnabled": {"description": "Whether app reports are enabled.", "type": "boolean"}, "commonCriteriaModeEnabled": {"description": "Whether Common Criteria Mode reporting is enabled. This is supported only on company-owned devices.", "type": "boolean"}, "deviceSettingsEnabled": {"description": "Whether device settings reporting is enabled.", "type": "boolean"}, "displayInfoEnabled": {"description": "Whether displays reporting is enabled. Report data is not available for personally owned devices with work profiles.", "type": "boolean"}, "hardwareStatusEnabled": {"description": "Whether hardware status reporting is enabled. Report data is not available for personally owned devices with work profiles.", "type": "boolean"}, "memoryInfoEnabled": {"description": "Whether memory event reporting is enabled.", "type": "boolean"}, "networkInfoEnabled": {"description": "Whether network info reporting is enabled.", "type": "boolean"}, "powerManagementEventsEnabled": {"description": "Whether power management event reporting is enabled. Report data is not available for personally owned devices with work profiles.", "type": "boolean"}, "softwareInfoEnabled": {"description": "Whether software info reporting is enabled.", "type": "boolean"}, "systemPropertiesEnabled": {"description": "Whether system properties reporting is enabled.", "type": "boolean"}}, "type": "object"}, "StopLostModeParams": {"description": "Parameters associated with the STOP_LOST_MODE command to take the device out of lost mode.", "id": "StopLostModeParams", "properties": {}, "type": "object"}, "StopLostModeStatus": {"description": "Status of the STOP_LOST_MODE command to take the device out of lost mode.", "id": "StopLostModeStatus", "properties": {"status": {"description": "The status. See StopLostModeStatus.", "enum": ["STATUS_UNSPECIFIED", "SUCCESS", "NOT_IN_LOST_MODE"], "enumDescriptions": ["Unspecified. This value is not used.", "The device was taken out of lost mode.", "The device is not in lost mode."], "type": "string"}}, "type": "object"}, "StopLostModeUserAttemptEvent": {"description": "A lost mode event indicating the user has attempted to stop lost mode.", "id": "StopLostModeUserAttemptEvent", "properties": {"status": {"description": "The status of the attempt to stop lost mode.", "enum": ["STATUS_UNSPECIFIED", "ATTEMPT_SUCCEEDED", "ATTEMPT_FAILED"], "enumDescriptions": ["This value is not used.", "Indicates that the user successfully stopped lost mode.", "Indicates that the user's attempt to stop lost mode failed."], "type": "string"}}, "type": "object"}, "SystemUpdate": {"description": "Configuration for managing system updatesNote: Google Play system updates (https://source.android.com/docs/core/ota/modular-system) (also called Mainline updates) are automatically downloaded but require a device reboot to be installed. Refer to the mainline section in Manage system updates (https://developer.android.com/work/dpc/system-updates#mainline) for further details.", "id": "SystemUpdate", "properties": {"endMinutes": {"description": "If the type is WINDOWED, the end of the maintenance window, measured as the number of minutes after midnight in device's local time. This value must be between 0 and 1439, inclusive. If this value is less than start_minutes, then the maintenance window spans midnight. If the maintenance window specified is smaller than 30 minutes, the actual window is extended to 30 minutes beyond the start time.", "format": "int32", "type": "integer"}, "freezePeriods": {"description": "An annually repeating time period in which over-the-air (OTA) system updates are postponed to freeze the OS version running on a device. To prevent freezing the device indefinitely, each freeze period must be separated by at least 60 days.", "items": {"$ref": "FreezePeriod"}, "type": "array"}, "startMinutes": {"description": "If the type is WINDOWED, the start of the maintenance window, measured as the number of minutes after midnight in the device's local time. This value must be between 0 and 1439, inclusive.", "format": "int32", "type": "integer"}, "type": {"description": "The type of system update to configure.", "enum": ["SYSTEM_UPDATE_TYPE_UNSPECIFIED", "AUTOMATIC", "WINDOWED", "POSTPONE"], "enumDescriptions": ["Follow the default update behavior for the device, which typically requires the user to accept system updates.", "Install automatically as soon as an update is available.", "Install automatically within a daily maintenance window. This also configures Play apps to be updated within the window. This is strongly recommended for kiosk devices because this is the only way apps persistently pinned to the foreground can be updated by Play.If autoUpdateMode is set to AUTO_UPDATE_HIGH_PRIORITY for an app, then the maintenance window is ignored for that app and it is updated as soon as possible even outside of the maintenance window.", "Postpone automatic install up to a maximum of 30 days. This policy does not affect security updates (e.g. monthly security patches)."], "type": "string"}}, "type": "object"}, "SystemUpdateInfo": {"description": "Information about a potential pending system update.", "id": "SystemUpdateInfo", "properties": {"updateReceivedTime": {"description": "The time when the update was first available. A zero value indicates that this field is not set. This field is set only if an update is available (that is, updateStatus is neither UPDATE_STATUS_UNKNOWN nor UP_TO_DATE).", "format": "google-datetime", "type": "string"}, "updateStatus": {"description": "The status of an update: whether an update exists and what type it is.", "enum": ["UPDATE_STATUS_UNKNOWN", "UP_TO_DATE", "UNKNOWN_UPDATE_AVAILABLE", "SECURITY_UPDATE_AVAILABLE", "OS_UPDATE_AVAILABLE"], "enumDescriptions": ["It is unknown whether there is a pending system update. This happens when, for example, the device API level is less than 26, or if the version of Android Device Policy is outdated.", "There is no pending system update available on the device.", "There is a pending system update available, but its type is not known.", "There is a pending security update available.", "There is a pending OS update available."], "type": "string"}}, "type": "object"}, "TelephonyInfo": {"description": "Telephony information associated with a given SIM card on the device. Only supported on fully managed devices starting from Android API level 23.", "id": "TelephonyInfo", "properties": {"activationState": {"description": "Output only. Activation state of the SIM card on the device. This is applicable for eSIMs only. This is supported on all devices for API level 35 and above. This is always ACTIVATION_STATE_UNSPECIFIED for physical SIMs and for devices below API level 35.", "enum": ["ACTIVATION_STATE_UNSPECIFIED", "ACTIVATED", "NOT_ACTIVATED"], "enumDescriptions": ["Activation state is not specified.", "The SIM card is activated.", "The SIM card is not activated."], "readOnly": true, "type": "string"}, "carrierName": {"description": "The carrier name associated with this SIM card.", "type": "string"}, "configMode": {"description": "Output only. The configuration mode of the SIM card on the device. This is applicable for eSIMs only. This is supported on all devices for API level 35 and above. This is always CONFIG_MODE_UNSPECIFIED for physical SIMs and for devices below API level 35.", "enum": ["CONFIG_MODE_UNSPECIFIED", "ADMIN_CONFIGURED", "USER_CONFIGURED"], "enumDescriptions": ["The configuration mode is unspecified.", "The admin has configured this SIM.", "The user has configured this SIM."], "readOnly": true, "type": "string"}, "iccId": {"description": "Output only. The ICCID associated with this SIM card.", "readOnly": true, "type": "string"}, "phoneNumber": {"description": "The phone number associated with this SIM card.", "type": "string"}}, "type": "object"}, "TermsAndConditions": {"description": "A terms and conditions page to be accepted during provisioning.", "id": "TermsAndConditions", "properties": {"content": {"$ref": "UserFacingMessage", "description": "A well-formatted HTML string. It will be parsed on the client with android.text.Html#fromHtml."}, "header": {"$ref": "UserFacingMessage", "description": "A short header which appears above the HTML content."}}, "type": "object"}, "UsageLog": {"description": "Controls types of device activity logs collected from the device and reported via Pub/Sub notification (https://developers.google.com/android/management/notifications).", "id": "UsageLog", "properties": {"enabledLogTypes": {"description": "Specifies which log types are enabled. Note that users will receive on-device messaging when usage logging is enabled.", "items": {"enum": ["LOG_TYPE_UNSPECIFIED", "SECURITY_LOGS", "NETWORK_ACTIVITY_LOGS"], "enumDescriptions": ["This value is not used.", "Enable logging of on-device security events, like when the device password is incorrectly entered or removable storage is mounted. See UsageLogEvent for a complete description of the logged security events. Supported for fully managed devices on Android 7 and above. Supported for company-owned devices with a work profile on Android 12 and above, on which only security events from the work profile are logged. Can be overridden by the application delegated scope SECURITY_LOGS", "Enable logging of on-device network events, like DNS lookups and TCP connections. See UsageLogEvent for a complete description of the logged network events. Supported for fully managed devices on Android 8 and above. Supported for company-owned devices with a work profile on Android 12 and above, on which only network events from the work profile are logged. Can be overridden by the application delegated scope NETWORK_ACTIVITY_LOGS"], "type": "string"}, "type": "array"}, "uploadOnCellularAllowed": {"description": "Specifies which of the enabled log types can be uploaded over mobile data. By default logs are queued for upload when the device connects to WiFi.", "items": {"enum": ["LOG_TYPE_UNSPECIFIED", "SECURITY_LOGS", "NETWORK_ACTIVITY_LOGS"], "enumDescriptions": ["This value is not used.", "Enable logging of on-device security events, like when the device password is incorrectly entered or removable storage is mounted. See UsageLogEvent for a complete description of the logged security events. Supported for fully managed devices on Android 7 and above. Supported for company-owned devices with a work profile on Android 12 and above, on which only security events from the work profile are logged. Can be overridden by the application delegated scope SECURITY_LOGS", "Enable logging of on-device network events, like DNS lookups and TCP connections. See UsageLogEvent for a complete description of the logged network events. Supported for fully managed devices on Android 8 and above. Supported for company-owned devices with a work profile on Android 12 and above, on which only network events from the work profile are logged. Can be overridden by the application delegated scope NETWORK_ACTIVITY_LOGS"], "type": "string"}, "type": "array"}}, "type": "object"}, "UsageLogEvent": {"description": "An event logged on the device.", "id": "UsageLogEvent", "properties": {"adbShellCommandEvent": {"$ref": "AdbShellCommandEvent", "description": "A shell command was issued over ADB via “adb shell command”. Part of SECURITY_LOGS."}, "adbShellInteractiveEvent": {"$ref": "AdbShellInteractiveEvent", "description": "An ADB interactive shell was opened via “adb shell”. Part of SECURITY_LOGS."}, "appProcessStartEvent": {"$ref": "AppProcessStartEvent", "description": "An app process was started. Part of SECURITY_LOGS."}, "backupServiceToggledEvent": {"$ref": "BackupServiceToggledEvent", "description": "An admin has enabled or disabled backup service. Part of SECURITY_LOGS."}, "certAuthorityInstalledEvent": {"$ref": "CertAuthorityInstalledEvent", "description": "A new root certificate was installed into the system's trusted credential storage. Part of SECURITY_LOGS."}, "certAuthorityRemovedEvent": {"$ref": "CertAuthorityRemovedEvent", "description": "A root certificate was removed from the system's trusted credential storage. Part of SECURITY_LOGS."}, "certValidationFailureEvent": {"$ref": "CertValidationFailureEvent", "description": "An X.509v3 certificate failed to validate, currently this validation is performed on the Wi-FI access point and failure may be due to a mismatch upon server certificate validation. However it may in the future include other validation events of an X.509v3 certificate. Part of SECURITY_LOGS."}, "connectEvent": {"$ref": "ConnectEvent", "description": "A TCP connect event was initiated through the standard network stack. Part of NETWORK_ACTIVITY_LOGS."}, "cryptoSelfTestCompletedEvent": {"$ref": "CryptoSelfTestCompletedEvent", "description": "Validates whether Android’s built-in cryptographic library (BoringSSL) is valid. Should always succeed on device boot, if it fails, the device should be considered untrusted. Part of SECURITY_LOGS."}, "dnsEvent": {"$ref": "DnsEvent", "description": "A DNS lookup event was initiated through the standard network stack. Part of NETWORK_ACTIVITY_LOGS."}, "enrollmentCompleteEvent": {"$ref": "EnrollmentCompleteEvent", "description": "Device has completed enrollment. Part of AMAPI_LOGS."}, "eventId": {"description": "Unique id of the event.", "format": "int64", "type": "string"}, "eventTime": {"description": "Device timestamp when the event was logged.", "format": "google-datetime", "type": "string"}, "eventType": {"description": "The particular usage log event type that was reported on the device. Use this to determine which event field to access.", "enum": ["EVENT_TYPE_UNSPECIFIED", "ADB_SHELL_COMMAND", "ADB_SHELL_INTERACTIVE", "APP_PROCESS_START", "KEYGUARD_DISMISSED", "KEYGUARD_DISMISS_AUTH_ATTEMPT", "KEYGUARD_SECURED", "FILE_PULLED", "FILE_PUSHED", "CERT_AUTHORITY_INSTALLED", "CERT_AUTHORITY_REMOVED", "CERT_VALIDATION_FAILURE", "CRYPTO_SELF_TEST_COMPLETED", "KEY_DESTRUCTION", "KEY_GENERATED", "KEY_IMPORT", "KEY_INTEGRITY_VIOLATION", "LOGGING_STARTED", "LOGGING_STOPPED", "LOG_BUFFER_SIZE_CRITICAL", "MEDIA_MOUNT", "MEDIA_UNMOUNT", "OS_SHUTDOWN", "OS_STARTUP", "REMOTE_LOCK", "WIPE_FAILURE", "CONNECT", "DNS", "STOP_LOST_MODE_USER_ATTEMPT", "LOST_MODE_OUTGOING_PHONE_CALL", "LOST_MODE_LOCATION", "ENROLLMENT_COMPLETE", "BACKUP_SERVICE_TOGGLED"], "enumDescriptions": ["This value is not used", "Indicates adb_shell_command_event has been set.", "Indicates adb_shell_interactive_event has been set.", "Indicates app_process_start_event has been set.", "Indicates keyguard_dismissed_event has been set.", "Indicates keyguard_dismiss_auth_attempt_event has been set.", "Indicates keyguard_secured_event has been set.", "Indicates file_pulled_event has been set.", "Indicates file_pushed_event has been set.", "Indicates cert_authority_installed_event has been set.", "Indicates cert_authority_removed_event has been set.", "Indicates cert_validation_failure_event has been set.", "Indicates crypto_self_test_completed_event has been set.", "Indicates key_destruction_event has been set.", "Indicates key_generated_event has been set.", "Indicates key_import_event has been set.", "Indicates key_integrity_violation_event has been set.", "Indicates logging_started_event has been set.", "Indicates logging_stopped_event has been set.", "Indicates log_buffer_size_critical_event has been set.", "Indicates media_mount_event has been set.", "Indicates media_unmount_event has been set.", "Indicates os_shutdown_event has been set.", "Indicates os_startup_event has been set.", "Indicates remote_lock_event has been set.", "Indicates wipe_failure_event has been set.", "Indicates connect_event has been set.", "Indicates dns_event has been set.", "Indicates stopLostModeUserAttemptEvent has been set.", "Indicates lostModeOutgoingPhoneCallEvent has been set.", "Indicates lostModeLocationEvent has been set.", "Indicates enrollment_complete_event has been set.", "Indicates backupServiceToggledEvent has been set."], "type": "string"}, "filePulledEvent": {"$ref": "FilePulledEvent", "description": "A file was downloaded from the device. Part of SECURITY_LOGS."}, "filePushedEvent": {"$ref": "FilePushedEvent", "description": "A file was uploaded onto the device. Part of SECURITY_LOGS."}, "keyDestructionEvent": {"$ref": "KeyDestructionEvent", "description": "A cryptographic key including user installed, admin installed and system maintained private key is removed from the device either by the user or management. Part of SECURITY_LOGS."}, "keyGeneratedEvent": {"$ref": "KeyGeneratedEvent", "description": "A cryptographic key including user installed, admin installed and system maintained private key is installed on the device either by the user or management. Part of SECURITY_LOGS."}, "keyImportEvent": {"$ref": "KeyImportEvent", "description": "A cryptographic key including user installed, admin installed and system maintained private key is imported on the device either by the user or management. Part of SECURITY_LOGS."}, "keyIntegrityViolationEvent": {"$ref": "KeyIntegrityViolationEvent", "description": "A cryptographic key including user installed, admin installed and system maintained private key is determined to be corrupted due to storage corruption, hardware failure or some OS issue. Part of SECURITY_LOGS."}, "keyguardDismissAuthAttemptEvent": {"$ref": "KeyguardDismissAuthAttemptEvent", "description": "An attempt was made to unlock the device. Part of SECURITY_LOGS."}, "keyguardDismissedEvent": {"$ref": "KeyguardDismissedEvent", "description": "The keyguard was dismissed. Part of SECURITY_LOGS."}, "keyguardSecuredEvent": {"$ref": "KeyguardSecuredEvent", "description": "The device was locked either by user or timeout. Part of SECURITY_LOGS."}, "logBufferSizeCriticalEvent": {"$ref": "LogBufferSizeCriticalEvent", "description": "The audit log buffer has reached 90% of its capacity, therefore older events may be dropped. Part of SECURITY_LOGS."}, "loggingStartedEvent": {"$ref": "LoggingStartedEvent", "description": "usageLog policy has been enabled. Part of SECURITY_LOGS."}, "loggingStoppedEvent": {"$ref": "LoggingStoppedEvent", "description": "usageLog policy has been disabled. Part of SECURITY_LOGS."}, "lostModeLocationEvent": {"$ref": "LostModeLocationEvent", "description": "A lost mode location update when a device in lost mode."}, "lostModeOutgoingPhoneCallEvent": {"$ref": "LostModeOutgoingPhoneCallEvent", "description": "An outgoing phone call has been made when a device in lost mode."}, "mediaMountEvent": {"$ref": "MediaMountEvent", "description": "Removable media was mounted. Part of SECURITY_LOGS."}, "mediaUnmountEvent": {"$ref": "MediaUnmountEvent", "description": "Removable media was unmounted. Part of SECURITY_LOGS."}, "osShutdownEvent": {"$ref": "OsShutdownEvent", "description": "Device was shutdown. Part of SECURITY_LOGS."}, "osStartupEvent": {"$ref": "OsStartupEvent", "description": "Device was started. Part of SECURITY_LOGS."}, "remoteLockEvent": {"$ref": "RemoteLockEvent", "description": "The device or profile has been remotely locked via the LOCK command. Part of SECURITY_LOGS."}, "stopLostModeUserAttemptEvent": {"$ref": "StopLostModeUserAttemptEvent", "description": "An attempt to take a device out of lost mode."}, "wipeFailureEvent": {"$ref": "WipeFailureEvent", "description": "The work profile or company-owned device failed to wipe when requested. This could be user initiated or admin initiated e.g. delete was received. Part of SECURITY_LOGS."}}, "type": "object"}, "User": {"description": "A user belonging to an enterprise.", "id": "User", "properties": {"accountIdentifier": {"description": "A unique identifier you create for this user, such as user342 or asset#44418. This field must be set when the user is created and can't be updated. This field must not contain personally identifiable information (PII). This identifier must be 1024 characters or less; otherwise, the update policy request will fail.", "type": "string"}}, "type": "object"}, "UserFacingMessage": {"description": "Provides a user-facing message with locale info. The maximum message length is 4096 characters.", "id": "UserFacingMessage", "properties": {"defaultMessage": {"description": "The default message displayed if no localized message is specified or the user's locale doesn't match with any of the localized messages. A default message must be provided if any localized messages are provided.", "type": "string"}, "localizedMessages": {"additionalProperties": {"type": "string"}, "description": "A map containing pairs, where locale is a well-formed BCP 47 language (https://www.w3.org/International/articles/language-tags/) code, such as en-US, es-ES, or fr.", "type": "object"}}, "type": "object"}, "WebApp": {"description": "A web app.", "id": "WebApp", "properties": {"displayMode": {"description": "The display mode of the web app.", "enum": ["DISPLAY_MODE_UNSPECIFIED", "MINIMAL_UI", "STANDALONE", "FULL_SCREEN"], "enumDescriptions": ["Not used.", "Opens the web app with a minimal set of browser UI elements for controlling navigation and viewing the page URL.", "Opens the web app to look and feel like a standalone native application. The browser UI elements and page URL are not visible, however the system status bar and back button are visible.", "Opens the web app in full screen without any visible controls. The browser UI elements, page URL, system status bar and back button are not visible, and the web app takes up the entirety of the available display area."], "type": "string"}, "icons": {"description": "A list of icons for the web app. Must have at least one element.", "items": {"$ref": "WebAppIcon"}, "type": "array"}, "name": {"description": "The name of the web app, which is generated by the server during creation in the form enterprises/{enterpriseId}/webApps/{packageName}.", "type": "string"}, "startUrl": {"description": "The start URL, i.e. the URL that should load when the user opens the application.", "type": "string"}, "title": {"description": "The title of the web app as displayed to the user (e.g., amongst a list of other applications, or as a label for an icon).", "type": "string"}, "versionCode": {"description": "The current version of the app.Note that the version can automatically increase during the lifetime of the web app, while Google does internal housekeeping to keep the web app up-to-date.", "format": "int64", "type": "string"}}, "type": "object"}, "WebAppIcon": {"description": "An icon for a web app. Supported formats are: png, jpg and webp.", "id": "WebAppIcon", "properties": {"imageData": {"description": "The actual bytes of the image in a base64url encoded string (c.f. RFC4648, section 5 \"Base 64 Encoding with URL and Filename Safe Alphabet\"). - The image type can be png or jpg. - The image should ideally be square. - The image should ideally have a size of 512x512. ", "type": "string"}}, "type": "object"}, "WebToken": {"description": "A web token used to access the managed Google Play iframe.", "id": "WebToken", "properties": {"enabledFeatures": {"description": "The features to enable. Use this if you want to control exactly which feature(s) will be activated; leave empty to allow all features.Restrictions / things to note: - If no features are listed here, all features are enabled — this is the default behavior where you give access to all features to your admins. - This must not contain any FEATURE_UNSPECIFIED values. - Repeated values are ignored ", "items": {"enum": ["FEATURE_UNSPECIFIED", "PLAY_SEARCH", "PRIVATE_APPS", "WEB_APPS", "STORE_BUILDER", "MANAGED_CONFIGURATIONS", "ZERO_TOUCH_CUSTOMER_MANAGEMENT"], "enumDescriptions": ["Unspecified feature.", "The Managed Play search apps page (https://developers.google.com/android/management/apps#search-apps).", "The private apps page (https://developers.google.com/android/management/apps#private-apps).", "The Web Apps page (https://developers.google.com/android/management/apps#web-apps).", "The organize apps page (https://developers.google.com/android/management/apps#organize-apps).", "The managed configurations page (https://developers.google.com/android/management/managed-configurations-iframe).", "The zero-touch iframe (https://developers.google.com/android/management/zero-touch-iframe)."], "type": "string"}, "type": "array"}, "name": {"description": "The name of the web token, which is generated by the server during creation in the form enterprises/{enterpriseId}/webTokens/{webTokenId}.", "type": "string"}, "parentFrameUrl": {"description": "The URL of the parent frame hosting the iframe with the embedded UI. To prevent XSS, the iframe may not be hosted at other URLs. The URL must use the https scheme.", "type": "string"}, "permissions": {"deprecated": true, "description": "Permissions available to an admin in the embedded UI. An admin must have all of these permissions in order to view the UI. This field is deprecated.", "items": {"enum": ["WEB_TOKEN_PERMISSION_UNSPECIFIED", "APPROVE_APPS"], "enumDescriptions": ["This value is ignored.", "The permission to approve apps for the enterprise."], "type": "string"}, "type": "array"}, "value": {"description": "The token value which is used in the hosting page to generate the iframe with the embedded UI. This is a read-only field generated by the server.", "type": "string"}}, "type": "object"}, "WifiRoamingPolicy": {"description": "Wi-Fi roaming policy.", "id": "WifiRoamingPolicy", "properties": {"wifiRoamingSettings": {"description": "Optional. Wi-Fi roaming settings. SSIDs provided in this list must be unique, the policy will be rejected otherwise.", "items": {"$ref": "WifiRoamingSetting"}, "type": "array"}}, "type": "object"}, "WifiRoamingSetting": {"description": "Wi-Fi roaming setting.", "id": "WifiRoamingSetting", "properties": {"wifiRoamingMode": {"description": "Required. Wi-Fi roaming mode for the specified SSID.", "enum": ["WIFI_ROAMING_MODE_UNSPECIFIED", "WIFI_ROAMING_DISABLED", "WIFI_ROAMING_DEFAULT", "WIFI_ROAMING_AGGRESSIVE"], "enumDescriptions": ["Unspecified. Defaults to WIFI_ROAMING_DEFAULT.", "Wi-Fi roaming is disabled. Supported on Android 15 and above on fully managed devices and work profiles on company-owned devices. A NonComplianceDetail with MANAGEMENT_MODE is reported for other management modes. A NonComplianceDetail with API_LEVEL is reported if the Android version is less than 15.", "Default Wi-Fi roaming mode of the device.", "Aggressive roaming mode which allows quicker Wi-Fi roaming. Supported on Android 15 and above on fully managed devices and work profiles on company-owned devices. A NonComplianceDetail with MANAGEMENT_MODE is reported for other management modes. A NonComplianceDetail with API_LEVEL is reported if the Android version is less than 15. A NonComplianceDetail with DEVICE_INCOMPATIBLE is reported if the device does not support aggressive roaming mode."], "type": "string"}, "wifiSsid": {"description": "Required. SSID of the Wi-Fi network.", "type": "string"}}, "type": "object"}, "WifiSsid": {"description": "Represents a Wi-Fi SSID.", "id": "WifiSsid", "properties": {"wifiSsid": {"description": "Required. Wi-Fi SSID represented as a string.", "type": "string"}}, "type": "object"}, "WifiSsidPolicy": {"description": "Restrictions on which Wi-Fi SSIDs the device can connect to. Note that this does not affect which networks can be configured on the device. Supported on company-owned devices running Android 13 and above.", "id": "WifiSsidPolicy", "properties": {"wifiSsidPolicyType": {"description": "Type of the Wi-Fi SSID policy to be applied.", "enum": ["WIFI_SSID_POLICY_TYPE_UNSPECIFIED", "WIFI_SSID_DENYLIST", "WIFI_SSID_ALLOWLIST"], "enumDescriptions": ["Defaults to WIFI_SSID_DENYLIST. wifiSsids must not be set. There are no restrictions on which SSID the device can connect to.", "The device cannot connect to any Wi-Fi network whose SSID is in wifiSsids, but can connect to other networks.", "The device can make Wi-Fi connections only to the SSIDs in wifiSsids. wifiSsids must not be empty. The device will not be able to connect to any other Wi-Fi network."], "type": "string"}, "wifiSsids": {"description": "Optional. List of Wi-Fi SSIDs that should be applied in the policy. This field must be non-empty when WifiSsidPolicyType is set to WIFI_SSID_ALLOWLIST. If this is set to a non-empty list, then a NonComplianceDetail detail with API_LEVEL is reported if the Android version is less than 13 and a NonComplianceDetail with MANAGEMENT_MODE is reported for non-company-owned devices.", "items": {"$ref": "WifiSsid"}, "type": "array"}}, "type": "object"}, "WipeAction": {"description": "An action to reset a company owned device or delete a work profile. Note: blockAction must also be specified.", "id": "WipeAction", "properties": {"preserveFrp": {"description": "Whether the factory-reset protection data is preserved on the device. This setting doesn’t apply to work profiles.", "type": "boolean"}, "wipeAfterDays": {"description": "Number of days the policy is non-compliant before the device or work profile is wiped. wipeAfterDays must be greater than blockAfterDays.", "format": "int32", "type": "integer"}}, "type": "object"}, "WipeFailureEvent": {"description": "The work profile or company-owned device failed to wipe when requested. This could be user initiated or admin initiated e.g. delete was received. Intentionally empty.", "id": "WipeFailureEvent", "properties": {}, "type": "object"}, "WipeParams": {"description": "Parameters associated with the WIPE command to wipe the device.", "id": "WipeParams", "properties": {"wipeDataFlags": {"description": "Optional. Flags to determine what data to wipe.", "items": {"enum": ["WIPE_DATA_FLAG_UNSPECIFIED", "PRESERVE_RESET_PROTECTION_DATA", "WIPE_EXTERNAL_STORAGE", "WIPE_ESIMS"], "enumDescriptions": ["This value is ignored.", "Preserve the factory reset protection data on the device.", "Additionally wipe the device's external storage (such as SD cards).", "For company-owned devices, this removes all eSIMs from the device when the device is wiped. In personally-owned devices, this will remove managed eSIMs (eSIMs which are added via the ADD_ESIM command) on the devices and no personally owned eSIMs will be removed."], "type": "string"}, "type": "array"}, "wipeReason": {"$ref": "UserFacingMessage", "description": "Optional. A short message displayed to the user before wiping the work profile on personal devices. This has no effect on company owned devices. The maximum message length is 200 characters."}}, "type": "object"}, "WorkAccountSetupConfig": {"description": "Controls the work account setup configuration, such as details of whether a Google authenticated account is required.", "id": "WorkAccountSetupConfig", "properties": {"authenticationType": {"description": "Optional. The authentication type of the user on the device.", "enum": ["AUTHENTICATION_TYPE_UNSPECIFIED", "AUTHENTICATION_TYPE_NOT_ENFORCED", "GOOGLE_AUTHENTICATED"], "enumDescriptions": ["Unspecified. Defaults to AUTHENTICATION_TYPE_NOT_ENFORCED.", "Authentication status of user on device is not enforced.", "Requires device to be managed with a Google authenticated account."], "type": "string"}, "requiredAccountEmail": {"description": "Optional. The specific google work account email address to be added. This field is only relevant if authenticationType is GOOGLE_AUTHENTICATED. This must be an enterprise account and not a consumer account. Once set and a Google authenticated account is added to the device, changing this field will have no effect, and thus recommended to be set only once.", "type": "string"}}, "type": "object"}}, "servicePath": "", "title": "Android Management API", "version": "v1", "version_module": true}