{"auth": {"oauth2": {"scopes": {"https://www.googleapis.com/auth/admob.readonly": {"description": "See your AdMob data"}, "https://www.googleapis.com/auth/admob.report": {"description": "See your AdMob data"}}}}, "basePath": "", "baseUrl": "https://admob.googleapis.com/", "batchPath": "batch", "canonicalName": "AdMob", "description": "The AdMob API allows publishers to programmatically get information about their AdMob account. ", "discoveryVersion": "v1", "documentationLink": "https://developers.google.com/admob/api/", "fullyEncodeReservedExpansion": true, "icons": {"x16": "http://www.google.com/images/icons/product/search-16.gif", "x32": "http://www.google.com/images/icons/product/search-32.gif"}, "id": "admob:v1beta", "kind": "discovery#restDescription", "mtlsRootUrl": "https://admob.mtls.googleapis.com/", "name": "admob", "ownerDomain": "google.com", "ownerName": "Google", "parameters": {"$.xgafv": {"description": "V1 error format.", "enum": ["1", "2"], "enumDescriptions": ["v1 error format", "v2 error format"], "location": "query", "type": "string"}, "access_token": {"description": "OAuth access token.", "location": "query", "type": "string"}, "alt": {"default": "json", "description": "Data format for response.", "enum": ["json", "media", "proto"], "enumDescriptions": ["Responses with Content-Type of application/json", "Media download with context-dependent Content-Type", "Responses with Content-Type of application/x-protobuf"], "location": "query", "type": "string"}, "callback": {"description": "JSONP", "location": "query", "type": "string"}, "fields": {"description": "Selector specifying which fields to include in a partial response.", "location": "query", "type": "string"}, "key": {"description": "API key. Your API key identifies your project and provides you with API access, quota, and reports. Required unless you provide an OAuth 2.0 token.", "location": "query", "type": "string"}, "oauth_token": {"description": "OAuth 2.0 token for the current user.", "location": "query", "type": "string"}, "prettyPrint": {"default": "true", "description": "Returns response with indentations and line breaks.", "location": "query", "type": "boolean"}, "quotaUser": {"description": "Available to use for quota purposes for server-side applications. Can be any arbitrary string assigned to a user, but should not exceed 40 characters.", "location": "query", "type": "string"}, "uploadType": {"description": "Legacy upload protocol for media (e.g. \"media\", \"multipart\").", "location": "query", "type": "string"}, "upload_protocol": {"description": "Upload protocol for media (e.g. \"raw\", \"multipart\").", "location": "query", "type": "string"}}, "protocol": "rest", "resources": {"accounts": {"methods": {"get": {"description": "Gets information about the specified AdMob publisher account.", "flatPath": "v1beta/accounts/{accountsId}", "httpMethod": "GET", "id": "admob.accounts.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Resource name of the publisher account to retrieve. Example: accounts/pub-****************", "location": "path", "pattern": "^accounts/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta/{+name}", "response": {"$ref": "PublisherAccount"}, "scopes": ["https://www.googleapis.com/auth/admob.readonly", "https://www.googleapis.com/auth/admob.report"], "streamingType": "NONE"}, "list": {"description": "Lists the AdMob publisher account that was most recently signed in to from the AdMob UI. For more information, see https://support.google.com/admob/answer/********.", "flatPath": "v1beta/accounts", "httpMethod": "GET", "id": "admob.accounts.list", "parameterOrder": [], "parameters": {"pageSize": {"description": "Maximum number of accounts to return.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "The value returned by the last `ListPublisherAccountsResponse`; indicates that this is a continuation of a prior `ListPublisherAccounts` call, and that the system should return the next page of data.", "location": "query", "type": "string"}}, "path": "v1beta/accounts", "response": {"$ref": "ListPublisherAccountsResponse"}, "scopes": ["https://www.googleapis.com/auth/admob.readonly", "https://www.googleapis.com/auth/admob.report"], "streamingType": "NONE"}}, "resources": {"adSources": {"methods": {"list": {"description": "List the ad sources.", "flatPath": "v1beta/accounts/{accountsId}/adSources", "httpMethod": "GET", "id": "admob.accounts.adSources.list", "parameterOrder": ["parent"], "parameters": {"pageSize": {"description": "The maximum number of ad sources to return. If unspecified or 0, at most 10,000 ad sources will be returned. The maximum value is 20,000; values above 10,000 will be coerced to 20,000.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "A page token, received from a previous `ListAdSources` call. Provide this to retrieve the subsequent page.", "location": "query", "type": "string"}, "parent": {"description": "Required. The parent which owns this collection of ad sources. Format: accounts/{publisher_id}", "location": "path", "pattern": "^accounts/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta/{+parent}/adSources", "response": {"$ref": "ListAdSourcesResponse"}, "scopes": ["https://www.googleapis.com/auth/admob.readonly", "https://www.googleapis.com/auth/admob.report"], "streamingType": "NONE"}}, "resources": {"adapters": {"methods": {"list": {"description": "List the adapters of the ad source.", "flatPath": "v1beta/accounts/{accountsId}/adSources/{adSourcesId}/adapters", "httpMethod": "GET", "id": "admob.accounts.adSources.adapters.list", "parameterOrder": ["parent"], "parameters": {"pageSize": {"description": "The maximum number of adapters to return. If unspecified or 0, at most 10,000 adapters will be returned. The maximum value is 20,000; values above 20,000 will be coerced to 20,000.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "A page token, received from a previous `ListAdapters` call. Provide this to retrieve the subsequent page.", "location": "query", "type": "string"}, "parent": {"description": "Required. The parent which owns this collection of adapters. Format: accounts/{publisher_id}/adSources/{ad_source_id}", "location": "path", "pattern": "^accounts/[^/]+/adSources/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta/{+parent}/adapters", "response": {"$ref": "ListAdaptersResponse"}, "scopes": ["https://www.googleapis.com/auth/admob.readonly"], "streamingType": "NONE"}}}}}, "adUnitMappings": {"methods": {"batchCreate": {"description": "<PERSON>ch create the ad unit mappings under the specific AdMob account. The maximum allowed batch size is 100. This method has limited access. If you see a 403 permission denied error, please reach out to your account manager for access.", "flatPath": "v1beta/accounts/{accountsId}/adUnitMappings:batchCreate", "httpMethod": "POST", "id": "admob.accounts.adUnitMappings.batchCreate", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. The AdMob account which owns this collection of ad unit mappings. Format: accounts/{publisher_id} See https://support.google.com/admob/answer/2784578 for instructions on how to find your AdMob publisher ID.", "location": "path", "pattern": "^accounts/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta/{+parent}/adUnitMappings:batchCreate", "request": {"$ref": "BatchCreateAdUnitMappingsRequest"}, "response": {"$ref": "BatchCreateAdUnitMappingsResponse"}, "streamingType": "NONE"}}}, "adUnits": {"methods": {"create": {"description": "Creates an ad unit under the specified AdMob account. This method has limited access. If you see a 403 permission denied error, please reach out to your account manager for access.", "flatPath": "v1beta/accounts/{accountsId}/adUnits", "httpMethod": "POST", "id": "admob.accounts.adUnits.create", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. Resource name of the account to create the specified ad unit for. Example: accounts/pub-****************", "location": "path", "pattern": "^accounts/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta/{+parent}/adUnits", "request": {"$ref": "AdUnit"}, "response": {"$ref": "AdUnit"}, "streamingType": "NONE"}, "list": {"description": "List the ad units under the specified AdMob account.", "flatPath": "v1beta/accounts/{accountsId}/adUnits", "httpMethod": "GET", "id": "admob.accounts.adUnits.list", "parameterOrder": ["parent"], "parameters": {"pageSize": {"description": "The maximum number of ad units to return. If unspecified or 0, at most 10,000 ad units will be returned. The maximum value is 20,000; values above 20,000 will be coerced to 20,000.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "The value returned by the last `ListAdUnitsResponse`; indicates that this is a continuation of a prior `ListAdUnits` call, and that the system should return the next page of data.", "location": "query", "type": "string"}, "parent": {"description": "Required. Resource name of the account to list ad units for. Example: accounts/pub-****************", "location": "path", "pattern": "^accounts/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta/{+parent}/adUnits", "response": {"$ref": "ListAdUnitsResponse"}, "scopes": ["https://www.googleapis.com/auth/admob.readonly"], "streamingType": "NONE"}}, "resources": {"adUnitMappings": {"methods": {"create": {"description": "Create an ad unit mapping under the specific AdMob account and ad unit. This method has limited access. If you see a 403 permission denied error, please reach out to your account manager for access.", "flatPath": "v1beta/accounts/{accountsId}/adUnits/{adUnitsId}/adUnitMappings", "httpMethod": "POST", "id": "admob.accounts.adUnits.adUnitMappings.create", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. The parent which owns the ad unit mapping. Format: accounts/{publisher_id}/adUnits/{ad_unit_id}", "location": "path", "pattern": "^accounts/[^/]+/adUnits/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta/{+parent}/adUnitMappings", "request": {"$ref": "AdUnitMapping"}, "response": {"$ref": "AdUnitMapping"}, "streamingType": "NONE"}, "list": {"description": "List ad unit mappings under the specified AdMob account and ad unit. This method has limited access. If you see a 403 permission denied error, please reach out to your account manager for access.", "flatPath": "v1beta/accounts/{accountsId}/adUnits/{adUnitsId}/adUnitMappings", "httpMethod": "GET", "id": "admob.accounts.adUnits.adUnitMappings.list", "parameterOrder": ["parent"], "parameters": {"filter": {"description": "The filter string that uses [EBNF grammar syntax](https://google.aip.dev/assets/misc/ebnf-filtering.txt). Possible field to filter by is: - \"DISPLAY_NAME\" Possible filter function is: - `IN`: Used to filter fields that represent a singleton including \"DISPLAY_NAME\". The filter functions can be added together using `AND`. `OR` functionality is not supported. Example: filter: IN(DISPLAY_NAME, \"Test Ad Unit Mapping 1\", \"Test Ad Unit Mapping 2\")", "location": "query", "type": "string"}, "pageSize": {"description": "The maximum number of ad unit mappings to return. If unspecified or 0, at most 10,000 ad unit mappings will be returned. The maximum value is 20,000; values above 20,000 will be coerced to 20,000.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "A page token, received from a previous `ListAdUnitMappings` call. Provide this to retrieve the subsequent page.", "location": "query", "type": "string"}, "parent": {"description": "Required. The parent which owns this collection of ad unit mappings. Format: accounts/{publisher_id}/adUnits/{ad_unit_id}", "location": "path", "pattern": "^accounts/[^/]+/adUnits/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta/{+parent}/adUnitMappings", "response": {"$ref": "ListAdUnitMappingsResponse"}, "scopes": ["https://www.googleapis.com/auth/admob.readonly"], "streamingType": "NONE"}}}}}, "apps": {"methods": {"create": {"description": "Creates an app under the specified AdMob account. This method has limited access. If you see a 403 permission denied error, please reach out to your account manager for access.", "flatPath": "v1beta/accounts/{accountsId}/apps", "httpMethod": "POST", "id": "admob.accounts.apps.create", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. Resource name of the account for which the app is being created. Example: accounts/pub-****************", "location": "path", "pattern": "^accounts/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta/{+parent}/apps", "request": {"$ref": "App"}, "response": {"$ref": "App"}, "streamingType": "NONE"}, "list": {"description": "List the apps under the specified AdMob account.", "flatPath": "v1beta/accounts/{accountsId}/apps", "httpMethod": "GET", "id": "admob.accounts.apps.list", "parameterOrder": ["parent"], "parameters": {"pageSize": {"description": "The maximum number of apps to return. If unspecified or 0, at most 10,000 apps will be returned. The maximum value is 20,000; values above 20,000 will be coerced to 20,000.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "The value returned by the last `ListAppsResponse`; indicates that this is a continuation of a prior `ListApps` call, and that the system should return the next page of data.", "location": "query", "type": "string"}, "parent": {"description": "Required. Resource name of the account to list apps for. Example: accounts/pub-****************", "location": "path", "pattern": "^accounts/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta/{+parent}/apps", "response": {"$ref": "ListAppsResponse"}, "scopes": ["https://www.googleapis.com/auth/admob.readonly"], "streamingType": "NONE"}}}, "campaignReport": {"methods": {"generate": {"description": "Generates Campaign Report based on provided specifications.", "flatPath": "v1beta/accounts/{accountsId}/campaignReport:generate", "httpMethod": "POST", "id": "admob.accounts.campaignReport.generate", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Resource name of the account to generate the report for. Example: accounts/pub-****************", "location": "path", "pattern": "^accounts/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta/{+parent}/campaignReport:generate", "request": {"$ref": "GenerateCampaignReportRequest"}, "response": {"$ref": "GenerateCampaignReportResponse"}, "scopes": ["https://www.googleapis.com/auth/admob.readonly", "https://www.googleapis.com/auth/admob.report"], "streamingType": "NONE"}}}, "mediationGroups": {"methods": {"create": {"description": "Create a mediation group under the specific AdMob account. This method has limited access. If you see a 403 permission denied error, please reach out to your account manager for access.", "flatPath": "v1beta/accounts/{accountsId}/mediationGroups", "httpMethod": "POST", "id": "admob.accounts.mediationGroups.create", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. The parent which owns the mediation group. Format: accounts/{publisher_id}", "location": "path", "pattern": "^accounts/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta/{+parent}/mediationGroups", "request": {"$ref": "MediationGroup"}, "response": {"$ref": "MediationGroup"}, "streamingType": "NONE"}, "list": {"description": "List mediation groups under the specified AdMob account. This method has limited access. If you see a 403 permission denied error, please reach out to your account manager for access.", "flatPath": "v1beta/accounts/{accountsId}/mediationGroups", "httpMethod": "GET", "id": "admob.accounts.mediationGroups.list", "parameterOrder": ["parent"], "parameters": {"filter": {"description": "The filter string that uses [EBNF grammar syntax](https://google.aip.dev/assets/misc/ebnf-filtering.txt). Possible fields to filter by are: - \"AD_SOURCE_IDS\" - \"AD_UNIT_IDS\" - \"APP_IDS\" - \"DISPLAY_NAME\" - \"FORMAT\" - \"MEDIATION_GROUP_ID\" - \"PLATFORM\" - \"STATE\" - \"TARGETED_REGION_CODES\" Possible filter functions are: - `IN`: Used to filter fields that represent a singleton including \"MEDIATION_GROUP_ID\", \"DISPLAY_NAME\", \"STATE\", \"PLATFORM\", and \"FORMAT\". - `CONTAINS_ANY`: Used to filter fields that represent a collection including \"AD_SOURCE_IDS\", \"AD_UNIT_IDS\", \"APP_IDS\", and \"TARGETED_REGION_CODES\". The filter functions can be added together using `AND`. `OR` functionality is not supported. Example: filter: IN(DISPLAY_NAME, \"Test Group 1\", \"Test Group 2\") AND IN(PLATFORM, \"ANDRO<PERSON>\") AND CONTAINS_ANY(AD_SOURCE_IDS, \"5450213213286189855\")", "location": "query", "type": "string"}, "pageSize": {"description": "The maximum number of mediation groups to return. If unspecified or 0, at most 10,000 mediation groups will be returned. The maximum value is 20,000; values above 20,000 will be coerced to 20,000.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "The value returned by the last `ListMediationGroupsResponse`; indicates that this is a continuation of a prior `ListMediationGroups` call, and that the system should return the next page of data.", "location": "query", "type": "string"}, "parent": {"description": "Required. Resource name of the account to list mediation groups for. Example: accounts/pub-****************", "location": "path", "pattern": "^accounts/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta/{+parent}/mediationGroups", "response": {"$ref": "ListMediationGroupsResponse"}, "scopes": ["https://www.googleapis.com/auth/admob.readonly"], "streamingType": "NONE"}, "patch": {"description": "Update the specified mediation group under the specified AdMob account. This method has limited access. If you see a 403 permission denied error, please reach out to your account manager for access.", "flatPath": "v1beta/accounts/{accountsId}/mediationGroups/{mediationGroupsId}", "httpMethod": "PATCH", "id": "admob.accounts.mediationGroups.patch", "parameterOrder": ["name"], "parameters": {"name": {"description": "Resource name for this mediation group. Format is: accounts/{publisher_id}/mediationGroups/{mediation_group_id} Example: accounts/pub-****************/mediationGroups/**********", "location": "path", "pattern": "^accounts/[^/]+/mediationGroups/[^/]+$", "required": true, "type": "string"}, "updateMask": {"description": "List of mediation group fields to be updated. Updates to repeated fields such as items in a list will fully replace the existing value(s) with the new value(s). Updates to individual values in a map can be done by indexing by the key. The following field masks are supported for mediation group updates: - \"mediation_group_lines[\\\"{mediation_group_line_id}\\\"]\" clang-format off - \"mediation_group_lines[\\\"{mediation_group_line_id}\\\"].ad_unit_mappings[\\\"{ad_unit_id}\\\"]\" clang-format on - \"mediation_group_lines[\\\"{mediation_group_line_id}\\\"].cpm_micros\" - \"mediation_group_lines[\\\"{mediation_group_line_id}\\\"].cpm_mode\" - \"mediation_group_lines[\\\"{mediation_group_line_id}\\\"].state\" - \"mediation_group_lines[\\\"{mediation_group_line_id}\\\"].display_name\" - \"targeting.ad_unit_ids\" To update a mediation group with a new mediation group line, use a distinct negative number for the \"mediation_group_line_id\". For Example: update_mask { paths: \"mediation_group_lines[\\\"**********12345\\\"].cpm_micros\" }", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1beta/{+name}", "request": {"$ref": "MediationGroup"}, "response": {"$ref": "MediationGroup"}, "streamingType": "NONE"}}, "resources": {"mediationAbExperiments": {"methods": {"create": {"description": "Create an A/B testing experiment for a specified AdMob account and a mediation group. This method has limited access. If you see a 403 permission denied error, please reach out to your account manager for access.", "flatPath": "v1beta/accounts/{accountsId}/mediationGroups/{mediationGroupsId}/mediationAbExperiments", "httpMethod": "POST", "id": "admob.accounts.mediationGroups.mediationAbExperiments.create", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. The parent which owns the mediation group. Format: accounts/{publisher_id}/mediationGroups/{mediation_group_id}", "location": "path", "pattern": "^accounts/[^/]+/mediationGroups/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta/{+parent}/mediationAbExperiments", "request": {"$ref": "MediationAbExperiment"}, "response": {"$ref": "MediationAbExperiment"}, "streamingType": "NONE"}, "stop": {"description": "Stop the mediation A/B experiment and choose a variant. This method has limited access. If you see a 403 permission denied error, please reach out to your account manager for access.", "flatPath": "v1beta/accounts/{accountsId}/mediationGroups/{mediationGroupsId}/mediationAbExperiments:stop", "httpMethod": "POST", "id": "admob.accounts.mediationGroups.mediationAbExperiments.stop", "parameterOrder": ["name"], "parameters": {"name": {"description": "Name of the mediation group, the experiment for which to choose a variant for. Example: accounts/pub-****************/mediationGroups/**********/ mediationAbExperiments", "location": "path", "pattern": "^accounts/[^/]+/mediationGroups/[^/]+/mediationAbExperiments$", "required": true, "type": "string"}}, "path": "v1beta/{+name}:stop", "request": {"$ref": "StopMediationAbExperimentRequest"}, "response": {"$ref": "MediationAbExperiment"}, "streamingType": "NONE"}}}}}, "mediationReport": {"methods": {"generate": {"description": "Generates an AdMob Mediation report based on the provided report specification. Returns result of a server-side streaming RPC. The result is returned in a sequence of responses.", "flatPath": "v1beta/accounts/{accountsId}/mediationReport:generate", "httpMethod": "POST", "id": "admob.accounts.mediationReport.generate", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Resource name of the account to generate the report for. Example: accounts/pub-****************", "location": "path", "pattern": "^accounts/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta/{+parent}/mediationReport:generate", "request": {"$ref": "GenerateMediationReportRequest"}, "response": {"$ref": "GenerateMediationReportResponse"}, "scopes": ["https://www.googleapis.com/auth/admob.readonly", "https://www.googleapis.com/auth/admob.report"], "streamingType": "SERVER_SIDE"}}}, "networkReport": {"methods": {"generate": {"description": "Generates an AdMob Network report based on the provided report specification. Returns result of a server-side streaming RPC. The result is returned in a sequence of responses.", "flatPath": "v1beta/accounts/{accountsId}/networkReport:generate", "httpMethod": "POST", "id": "admob.accounts.networkReport.generate", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Resource name of the account to generate the report for. Example: accounts/pub-****************", "location": "path", "pattern": "^accounts/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta/{+parent}/networkReport:generate", "request": {"$ref": "GenerateNetworkReportRequest"}, "response": {"$ref": "GenerateNetworkReportResponse"}, "scopes": ["https://www.googleapis.com/auth/admob.readonly", "https://www.googleapis.com/auth/admob.report"], "streamingType": "SERVER_SIDE"}}}}}}, "revision": "********", "rootUrl": "https://admob.googleapis.com/", "schemas": {"AdSource": {"description": "Definition of a mediation ad source.", "id": "AdSource", "properties": {"adSourceId": {"description": "ID of this ad source.", "type": "string"}, "name": {"description": "Resource name of this ad source. Format is: accounts/{publisher_id}/adSources/{ad_source_id}", "type": "string"}, "title": {"description": "Display name of this ad source.", "type": "string"}}, "type": "object"}, "AdUnit": {"description": "Describes an AdMob ad unit.", "id": "AdUnit", "properties": {"adFormat": {"description": "AdFormat of the ad unit. Possible values are as follows: \"APP_OPEN\" - App Open ad format. \"BANNER\" - Banner ad format. \"BANNER_INTERSTITIAL\" - Legacy format that can be used as either banner or interstitial. This format can no longer be created but can be targeted by mediation groups. \"INTERSTITIAL\" - A full screen ad. Supported ad types are \"RICH_MEDIA\" and \"VIDEO\". \"NATIVE\" - Native ad format. \"REWARDED\" - An ad that, once viewed, gets a callback verifying the view so that a reward can be given to the user. Supported ad types are \"RICH_MEDIA\" (interactive) and video where video can not be excluded. \"REWARDED_INTERSTITIAL\" - Rewarded Interstitial ad format. Only supports video ad type. See https://support.google.com/admob/answer/9884467.", "type": "string"}, "adTypes": {"description": "Ad media type supported by this ad unit. Possible values as follows: \"RICH_MEDIA\" - Text, image, and other non-video media. \"VIDEO\" - Video media.", "items": {"type": "string"}, "type": "array"}, "adUnitId": {"description": "The externally visible ID of the ad unit which can be used to integrate with the AdMob SDK. This is a read only property. Example: ca-app-pub-****************/**********", "type": "string"}, "appId": {"description": "The externally visible ID of the app this ad unit is associated with. Example: ca-app-pub-****************~**********", "type": "string"}, "displayName": {"description": "The display name of the ad unit as shown in the AdMob UI, which is provided by the user. The maximum length allowed is 80 characters.", "type": "string"}, "name": {"description": "Resource name for this ad unit. Format is accounts/{publisher_id}/adUnits/{ad_unit_id_fragment} Example: accounts/pub-****************/adUnits/**********", "type": "string"}, "rewardSettings": {"$ref": "AdUnitRewardSettings", "description": "Optional. Settings for a rewarded ad unit. This can be set or unset only when the ad_format is \"REWARDED\"."}}, "type": "object"}, "AdUnitMapping": {"description": "Settings to map an AdMob ad unit to a 3rd party ad unit.", "id": "AdUnitMapping", "properties": {"adUnitConfigurations": {"additionalProperties": {"type": "string"}, "description": "Settings for the specified ad unit to make an ad request to 3rd party ad network. Key-value pairs with values set by the user for the keys requested by the ad network. Please see https://support.google.com/admob/answer/3245073 for details on how to configure the network settings.", "type": "object"}, "adapterId": {"description": "The ID of mediation ad source adapter used by this ad unit mapping. The adapter determines the information needed in the ad_network_settings.", "format": "int64", "type": "string"}, "displayName": {"description": "Optional. The display name of this ad unit mapping instance.", "type": "string"}, "name": {"description": "Resource name of this ad unit mapping. Format is: accounts/{publisher_id}/adUnits/{ad_unit_id_fragment}/adUnitMappings/{ad_unit_mapping_id} Example: accounts/pub-**********123456/adUnits/**********/adUnitMappings/*********", "type": "string"}, "state": {"description": "Output only. The status of this ad unit mapping.", "enum": ["STATE_UNSPECIFIED", "ENABLED"], "enumDescriptions": ["The default value.", "The ad unit mapping is active and used to serve ads or collecting stats."], "readOnly": true, "type": "string"}}, "type": "object"}, "AdUnitRewardSettings": {"description": "Settings for a rewarded ad unit.", "id": "AdUnitRewardSettings", "properties": {"unitAmount": {"description": "Reward amount for this ad unit.", "format": "int64", "type": "string"}, "unitType": {"description": "Reward item for this ad unit.", "type": "string"}}, "type": "object"}, "Adapter": {"description": "Describes adapters supported by each mediation ad source. Adapters correspond to a specific SDK implementation of the ad source, and are each associated with a single platform and a list of supported ad unit formats. Adapters may also require setting some configurations to perform ad requests. Configurations can be specified in the AdUnitMapping by setting the [ad_unit_configurations](#AdUnitMapping.ad_unit_configurations) key/value pairs. For example, the ad_unit_configurations can be used to pass various IDs to the adapter's third-party SDK.", "id": "Adapter", "properties": {"adapterConfigMetadata": {"description": "Output only. Configuration metadata associated with this adapter.", "items": {"$ref": "AdapterAdapterConfigMetadata"}, "readOnly": true, "type": "array"}, "adapterId": {"description": "Output only. ID of this adapter. It is used to set [adapter_id](#AdUnitMapping.adapter_id).", "readOnly": true, "type": "string"}, "formats": {"description": "Output only. Indicates the formats of the ad units supported by this adapter.", "items": {"type": "string"}, "readOnly": true, "type": "array"}, "name": {"description": "Output only. Resource name of the adapter. Format is: accounts/{publisher_id}/adSources/{ad_source_id}/adapters/{adapter_id}.", "readOnly": true, "type": "string"}, "platform": {"description": "Output only. Mobile application platform supported by this adapter. Supported values are: IOS, ANDROID, WINDOWS_PHONE", "readOnly": true, "type": "string"}, "title": {"description": "Output only. The display name of this adapter.", "readOnly": true, "type": "string"}}, "type": "object"}, "AdapterAdapterConfigMetadata": {"description": "Configuration metadata associated with this adapter. They are used to define the ad_unit_configurations associated with AdUnitMappings for the this adapter.", "id": "AdapterAdapterConfigMetadata", "properties": {"adapterConfigMetadataId": {"description": "This is used to fill the key of the [ad_unit_configurations](#AdUnitMapping.ad_unit_configurations).", "type": "string"}, "adapterConfigMetadataLabel": {"description": "Name of the adapter configuration metadata.", "type": "string"}, "isRequired": {"description": "Whether this metadata is required for configuring the AdUnitMappings.", "type": "boolean"}}, "type": "object"}, "App": {"description": "Describes an AdMob app for a specific platform (For example: Android or iOS).", "id": "App", "properties": {"appApprovalState": {"description": "Output only. The approval state for the app. The field is read-only.", "enum": ["APP_APPROVAL_STATE_UNSPECIFIED", "ACTION_REQUIRED", "IN_REVIEW", "APPROVED"], "enumDescriptions": ["Default value for an unset field. Do not use.", "The app requires additional user action to be approved. Please refer to https://support.google.com/admob/answer/10564477 for details and next steps.", "The app is pending review.", "The app is approved and can serve ads."], "readOnly": true, "type": "string"}, "appId": {"description": "The externally visible ID of the app which can be used to integrate with the AdMob SDK. This is a read only property. Example: ca-app-pub-****************~**********", "type": "string"}, "linkedAppInfo": {"$ref": "AppLinkedAppInfo", "description": "Immutable. The information for an app that is linked to an app store. This field is present if and only if the app is linked to an app store."}, "manualAppInfo": {"$ref": "AppManualAppInfo", "description": "The information for an app that is not linked to any app store. After an app is linked, this information is still retrivable. If no name is provided for the app upon creation, a placeholder name will be used."}, "name": {"description": "Resource name for this app. Format is accounts/{publisher_id}/apps/{app_id_fragment} Example: accounts/pub-****************/apps/**********", "type": "string"}, "platform": {"description": "Describes the platform of the app. Limited to \"IOS\" and \"ANDROID\".", "type": "string"}}, "type": "object"}, "AppLinkedAppInfo": {"description": "Information from the app store if the app is linked to an app store.", "id": "AppLinkedAppInfo", "properties": {"androidAppStores": {"description": "Optional. The app store information for published Android apps. This field is only used for apps on the Android platform and will be ignored if the PLATFORM is set to iOS. The default value is the Google Play App store. This field can be updated after app is created. If the app is not published, this field will not be included in the response.", "items": {"enum": ["ANDROID_APP_STORE_UNSPECIFIED", "GOOGLE_PLAY_APP_STORE", "AMAZON_APP_STORE", "OPPO_APP_STORE", "SAMSUNG_APP_STORE", "VIVO_APP_STORE", "XIAOMI_APP_STORE"], "enumDescriptions": ["Default value for an unset field. Do not use.", "Google Play Store.", "Amazon Appstore.", "Oppo App Market.", "Samsung Galaxy Store.", "VIVO App Store.", "<PERSON><PERSON>."], "type": "string"}, "type": "array"}, "appStoreId": {"description": "The app store ID of the app; present if and only if the app is linked to an app store. If the app is added to the Google Play store, it will be the application ID of the app. For example: \"com.example.myapp\". See https://developer.android.com/studio/build/application-id. If the app is added to the Apple App Store, it will be app store ID. For example \"105169111\". Note that setting the app store id is considered an irreversible action. Once an app is linked, it cannot be unlinked.", "type": "string"}, "displayName": {"description": "Output only. Display name of the app as it appears in the app store. This is an output-only field, and may be empty if the app cannot be found in the store.", "readOnly": true, "type": "string"}}, "type": "object"}, "AppManualAppInfo": {"description": "Information provided for manual apps which are not linked to an application store (Example: Google Play, App Store).", "id": "AppManualAppInfo", "properties": {"displayName": {"description": "The display name of the app as shown in the AdMob UI, which is provided by the user. The maximum length allowed is 80 characters.", "type": "string"}}, "type": "object"}, "BatchCreateAdUnitMappingsRequest": {"description": "Request to create a batch of ad unit mappings under the specific AdMob account.", "id": "BatchCreateAdUnitMappingsRequest", "properties": {"requests": {"description": "Required. The request message specifying the ad unit mappings to create. A maximum of 100 ad unit mappings can be created in a batch. If the number of ad unit mappings in the batch request exceed 100, the entire request will be rejected and no ad unit mappings will be created.", "items": {"$ref": "CreateAdUnitMappingRequest"}, "type": "array"}}, "type": "object"}, "BatchCreateAdUnitMappingsResponse": {"description": "Response containing a batch of created ad unit mappings.", "id": "BatchCreateAdUnitMappingsResponse", "properties": {"adUnitMappings": {"description": "The Ad units mappings created under the requested account.", "items": {"$ref": "AdUnitMapping"}, "type": "array"}}, "type": "object"}, "CampaignReportSpec": {"description": "The specification for generating a Campaign report. For example, the specification to get IMPRESSIONS and CLICKS sliced by CAMPAIGN_ID can look like the following example: { \"date_range\": { \"start_date\": {\"year\": 2021, \"month\": 12, \"day\": 1}, \"end_date\": {\"year\": 2021, \"month\": 12, \"day\": 30} }, \"dimensions\": [\"CAMPAIGN_ID\"], \"metrics\": [\"IMPRESSIONS\", \"CLICKS\"], }", "id": "CampaignReportSpec", "properties": {"dateRange": {"$ref": "DateRange", "description": "The date range for which the report is generated. The max range is 30 days."}, "dimensions": {"description": "List of dimensions of the report. The value combination of these dimensions determines the row of the report. If no dimensions are specified, the report returns a single row of requested metrics for the entire account.", "items": {"enum": ["DIMENSION_UNSPECIFIED", "DATE", "CAMPAIGN_ID", "CAMPAIGN_NAME", "AD_ID", "AD_NAME", "PLACEMENT_ID", "PLACEMENT_NAME", "PLACEMENT_PLATFORM", "COUNTRY", "FORMAT"], "enumDescriptions": ["Default value of an unspecified dimension. Do not use.", "A date in the YYYYMMDD format (for example, \"********\").", "The unique ID of the campaign, for example \"*********\", which is consistent with the campaign id from campaign list api.", "The name of the campaign.", "The unique ID of the ad, for example \"*********\".", "The name of the ad, which may not be unique.", "The vendor-specific unique ID of the app where the ad is placed. For example, com.goog.bar for Android and ********* for iOS apps.", "The name of the app where the ad is placed.", "The platform of the app where the ad is placed.", "The name of the targeted country.", "Format of the ad unit (for example, \"banner\", \"native\"), an ad delivery dimension."], "type": "string"}, "type": "array"}, "languageCode": {"description": "Language used for any localized text, such as certain applicable dimension values. The language tag is defined in the IETF BCP47. Defaults to 'en-US' if unspecified or invalid.", "type": "string"}, "metrics": {"description": "List of metrics of the report. A report must specify at least one metric.", "items": {"enum": ["METRIC_UNSPECIFIED", "IMPRESSIONS", "CLICKS", "CLICK_THROUGH_RATE", "INSTALLS", "ESTIMATED_COST", "AVERAGE_CPI", "INTERACTIONS"], "enumDescriptions": ["Default value for an unset field. Do not use.", "The number of times an ad is shown to the user.", "The number of times a user clicks an ad.", "The number of clicks an ad receives divided by the number of times the ad is shown.", "The number of times apps were downloaded to devices.", "The estimated cost of serving a campaign ad instead of an ad that earns revenue. The currency is the reporting currency for campaigns.", "The average cost per install. This means the average amount that was spent to acquire a new user. The currency is the reporting currency for campaigns.", "The number of times a user interacts with an ad. This consists of clicks and engagements."], "type": "string"}, "type": "array"}}, "type": "object"}, "CreateAdUnitMappingRequest": {"description": "Request to create an ad unit mapping under the specific AdMob account and ad unit.", "id": "CreateAdUnitMappingRequest", "properties": {"adUnitMapping": {"$ref": "AdUnitMapping", "description": "Required. The ad unit mapping to create."}, "parent": {"description": "Required. The parent which owns the ad unit mapping. Format: accounts/{publisher_id}/adUnits/{ad_unit_id}", "type": "string"}}, "type": "object"}, "Date": {"description": "Represents a whole or partial calendar date, such as a birthday. The time of day and time zone are either specified elsewhere or are insignificant. The date is relative to the Gregorian Calendar. This can represent one of the following: * A full date, with non-zero year, month, and day values. * A month and day, with a zero year (for example, an anniversary). * A year on its own, with a zero month and a zero day. * A year and month, with a zero day (for example, a credit card expiration date). Related types: * google.type.TimeOfDay * google.type.DateTime * google.protobuf.Timestamp", "id": "Date", "properties": {"day": {"description": "Day of a month. Must be from 1 to 31 and valid for the year and month, or 0 to specify a year by itself or a year and month where the day isn't significant.", "format": "int32", "type": "integer"}, "month": {"description": "Month of a year. Must be from 1 to 12, or 0 to specify a year without a month and day.", "format": "int32", "type": "integer"}, "year": {"description": "Year of the date. Must be from 1 to 9999, or 0 to specify a date without a year.", "format": "int32", "type": "integer"}}, "type": "object"}, "DateRange": {"description": "Specification of a single date range. Both dates are inclusive.", "id": "DateRange", "properties": {"endDate": {"$ref": "Date", "description": "End date of the date range, inclusive. Must be greater than or equal to the start date."}, "startDate": {"$ref": "Date", "description": "Start date of the date range, inclusive. Must be less than or equal to the end date."}}, "type": "object"}, "GenerateCampaignReportRequest": {"description": "Request to generate campaign report.", "id": "GenerateCampaignReportRequest", "properties": {"reportSpec": {"$ref": "CampaignReportSpec", "description": "Campaign report specification."}}, "type": "object"}, "GenerateCampaignReportResponse": {"description": "Campaign Report API response.", "id": "GenerateCampaignReportResponse", "properties": {"rows": {"description": "The campaign report data from the specified publisher. At most 100000 rows will be returned from the API.", "items": {"$ref": "ReportRow"}, "type": "array"}}, "type": "object"}, "GenerateMediationReportRequest": {"description": "Request to generate an AdMob Mediation report.", "id": "GenerateMediationReportRequest", "properties": {"reportSpec": {"$ref": "MediationReportSpec", "description": "Network report specification."}}, "type": "object"}, "GenerateMediationReportResponse": {"description": "The streaming response for the AdMob Mediation report where the first response contains the report header, then a stream of row responses, and finally a footer as the last response message. For example: [{ \"header\": { \"date_range\": { \"start_date\": {\"year\": 2018, \"month\": 9, \"day\": 1}, \"end_date\": {\"year\": 2018, \"month\": 9, \"day\": 1} }, \"localization_settings\": { \"currency_code\": \"USD\", \"language_code\": \"en-US\" } } }, { \"row\": { \"dimension_values\": { \"DATE\": {\"value\": \"20180918\"}, \"APP\": { \"value\": \"ca-app-pub-8123415297019784~1001342552\", \"display_label\": \"My app name!\" } }, \"metric_values\": { \"ESTIMATED_EARNINGS\": {\"decimal_value\": \"1324746\"} } } }, { \"footer\": {\"matching_row_count\": 1} }]", "id": "GenerateMediationReportResponse", "properties": {"footer": {"$ref": "ReportFooter", "description": "Additional information about the generated report, such as warnings about the data."}, "header": {"$ref": "ReportHeader", "description": "Report generation settings that describes the report contents, such as the report date range and localization settings."}, "row": {"$ref": "ReportRow", "description": "Actual report data."}}, "type": "object"}, "GenerateNetworkReportRequest": {"description": "Request to generate an AdMob Network report.", "id": "GenerateNetworkReportRequest", "properties": {"reportSpec": {"$ref": "NetworkReportSpec", "description": "Network report specification."}}, "type": "object"}, "GenerateNetworkReportResponse": {"description": "The streaming response for the AdMob Network report where the first response contains the report header, then a stream of row responses, and finally a footer as the last response message. For example: [{ \"header\": { \"dateRange\": { \"startDate\": {\"year\": 2018, \"month\": 9, \"day\": 1}, \"endDate\": {\"year\": 2018, \"month\": 9, \"day\": 1} }, \"localizationSettings\": { \"currencyCode\": \"USD\", \"languageCode\": \"en-US\" } } }, { \"row\": { \"dimensionValues\": { \"DATE\": {\"value\": \"20180918\"}, \"APP\": { \"value\": \"ca-app-pub-8123415297019784~1001342552\", displayLabel: \"My app name!\" } }, \"metricValues\": { \"ESTIMATED_EARNINGS\": {\"microsValue\": 6500000} } } }, { \"footer\": {\"matchingRowCount\": 1} }]", "id": "GenerateNetworkReportResponse", "properties": {"footer": {"$ref": "ReportFooter", "description": "Additional information about the generated report, such as warnings about the data."}, "header": {"$ref": "ReportHeader", "description": "Report generation settings that describes the report contents, such as the report date range and localization settings."}, "row": {"$ref": "ReportRow", "description": "Actual report data."}}, "type": "object"}, "ListAdSourcesResponse": {"description": "Response for the ListAdSourcesRequest.", "id": "ListAdSourcesResponse", "properties": {"adSources": {"description": "The ad sources.", "items": {"$ref": "AdSource"}, "type": "array"}, "nextPageToken": {"description": "Used to set the `page_token` in the `ListAdSourcesRequest` to retrieve the next page. If this field is omitted, there are no subsequent pages.", "type": "string"}}, "type": "object"}, "ListAdUnitMappingsResponse": {"description": "Response for the ListAdUnitMappingsRequest.", "id": "ListAdUnitMappingsResponse", "properties": {"adUnitMappings": {"description": "The ad unit mappings from the specified account and ad unit.", "items": {"$ref": "AdUnitMapping"}, "type": "array"}, "nextPageToken": {"description": "Used to set the `page_token` in the `ListAdUnitMappingsRequest` to retrieve the next page. If this field is omitted, there are no subsequent pages.", "type": "string"}}, "type": "object"}, "ListAdUnitsResponse": {"description": "Response for the ad units list request.", "id": "ListAdUnitsResponse", "properties": {"adUnits": {"description": "The resulting ad units for the requested account.", "items": {"$ref": "AdUnit"}, "type": "array"}, "nextPageToken": {"description": "If not empty, indicates that there may be more ad units for the request; this value should be passed in a new `ListAdUnitsRequest`.", "type": "string"}}, "type": "object"}, "ListAdaptersResponse": {"description": "Response for the ListAdaptersRequest.", "id": "ListAdaptersResponse", "properties": {"adapters": {"description": "The adapter.", "items": {"$ref": "Adapter"}, "type": "array"}, "nextPageToken": {"description": "Used to set the `page_token` in the `ListAdapterRequest` to retrieve the next page. If this field is omitted, there are no subsequent pages.", "type": "string"}}, "type": "object"}, "ListAppsResponse": {"description": "Response for the apps list request.", "id": "ListAppsResponse", "properties": {"apps": {"description": "The resulting apps for the requested account.", "items": {"$ref": "App"}, "type": "array"}, "nextPageToken": {"description": "If not empty, indicates that there may be more apps for the request; this value should be passed in a new `ListAppsRequest`.", "type": "string"}}, "type": "object"}, "ListMediationGroupsResponse": {"description": "Response for the mediation groups list request.", "id": "ListMediationGroupsResponse", "properties": {"mediationGroups": {"description": "The resulting mediation groups for the requested account.", "items": {"$ref": "MediationGroup"}, "type": "array"}, "nextPageToken": {"description": "If not empty, indicates that there may be more mediation groups for the request; this value should be passed in a new `ListMediationGroupsRequest`.", "type": "string"}}, "type": "object"}, "ListPublisherAccountsResponse": {"description": "Response for the publisher account list request.", "id": "ListPublisherAccountsResponse", "properties": {"account": {"description": "Publisher that the client credentials can access.", "items": {"$ref": "PublisherAccount"}, "type": "array"}, "nextPageToken": {"description": "If not empty, indicates that there might be more accounts for the request; you must pass this value in a new `ListPublisherAccountsRequest`.", "type": "string"}}, "type": "object"}, "LocalizationSettings": {"description": "Localization settings for reports, such as currency and language. It affects how metrics are calculated.", "id": "LocalizationSettings", "properties": {"currencyCode": {"description": "Currency code of the earning related metrics, which is the 3-letter code defined in ISO 4217. The daily average rate is used for the currency conversion. Defaults to the account currency code if unspecified.", "type": "string"}, "languageCode": {"description": "Language used for any localized text, such as some dimension value display labels. The language tag defined in the IETF BCP47. Defaults to 'en-US' if unspecified.", "type": "string"}}, "type": "object"}, "MediationAbExperiment": {"description": "The mediation A/B experiment.", "id": "MediationAbExperiment", "properties": {"controlMediationLines": {"description": "Output only. The experiment mediation lines for control. They are inherited from the parent mediation group. It is an output only field.", "items": {"$ref": "MediationAbExperimentExperimentMediationLine"}, "readOnly": true, "type": "array"}, "displayName": {"description": "The display name for the mediation A/B experiment.", "type": "string"}, "endTime": {"description": "Output only. The time at which the experiment was ended or target to end (in UTC).", "format": "google-datetime", "readOnly": true, "type": "string"}, "experimentId": {"description": "Output only. Unique identifier for the mediation A/B experiment. It is an output only property.", "readOnly": true, "type": "string"}, "mediationGroupId": {"description": "Output only. The mediation group id this experiment belongs to. This can be used for filtering the experiments in the list experiments API.", "readOnly": true, "type": "string"}, "name": {"description": "Resource name for this experiment. The format is accounts/{publisher_id}/ mediationGroups/{mediation_group_id}/mediationAbExperiment/ {mediation_group_experiment_id}. For example: accounts/pub-****************/mediationGroups/**********/ mediationAbExperiment/12345", "type": "string"}, "startTime": {"description": "Output only. The time at which the experiment was started (in UTC).", "format": "google-datetime", "readOnly": true, "type": "string"}, "state": {"description": "Output only. The state of the experiment. It is an output only field.", "enum": ["EXPERIMENT_STATE_UNSPECIFIED", "EXPIRED", "RUNNING", "ENDED"], "enumDescriptions": ["Default for an unset field. Do not use.", "The experiment has expired.", "The experiment is running and thus used in serving.", "The experiment is ended, but not yet expired. Serving continues in the original configurations while the experiment is ready to call an action."], "readOnly": true, "type": "string"}, "treatmentMediationLines": {"description": "The experiment mediation lines created for the treatment. They will be used for serving when the experiment status is RUNNING.", "items": {"$ref": "MediationAbExperimentExperimentMediationLine"}, "type": "array"}, "treatmentTrafficPercentage": {"description": "The percentage of the mediation A/B experiment traffic that will be send to the treatment (variant B). The remainder is sent to the control (variant A). The percentage is expressed as an integer in the inclusive range of [1,99]. See https://support.google.com/admob/answer/9572326 for details.", "format": "int64", "type": "string"}, "variantLeader": {"description": "Output only. The variant leader for the experiment according to some key metrics.", "enum": ["VARIANT_LEADER_UNSPECIFIED", "CONTROL", "TREATMENT", "INSUFFICIENT_DATA", "TOO_EARLY_TO_CALL", "NO_VARIANT_LEADER"], "enumDescriptions": ["Default value of the enum. Do not use.", "The control leading the experiment.", "The treatment leading the experiment.", "Collected data (impressions) are insufficient to determine a leader.", "Experiment hasn't run long enough to determine a leader.", "Neither variant is a decisive winner in the observed data."], "readOnly": true, "type": "string"}}, "type": "object"}, "MediationAbExperimentExperimentMediationLine": {"description": "The mediation group line for the experiment. It will be used for serving during the run of the experiment.", "id": "MediationAbExperimentExperimentMediationLine", "properties": {"mediationGroupLine": {"$ref": "MediationGroupMediationGroupLine", "description": "The mediation group line used by the experiment."}}, "type": "object"}, "MediationGroup": {"description": "Describes an AdMob Mediation group.", "id": "MediationGroup", "properties": {"displayName": {"description": "User provided name for the mediation group. The maximum length allowed is 120 characters.", "type": "string"}, "mediationAbExperimentState": {"description": "Output only. The state of the mediation a/b experiment that belongs to this mediation group.", "enum": ["EXPERIMENT_STATE_UNSPECIFIED", "RUNNING", "NOT_RUNNING"], "enumDescriptions": ["Default value for an unset field. Do not use.", "The mediation group has an experiment in the RUNNING state.", "There is no experiment in the RUNNING state in the mediation group."], "readOnly": true, "type": "string"}, "mediationGroupId": {"description": "The ID of the mediation group. Example: \"**********\". This is a read only property.", "type": "string"}, "mediationGroupLines": {"additionalProperties": {"$ref": "MediationGroupMediationGroupLine"}, "description": "The mediation lines used for serving for this mediation group. Key is the ID of the mediation group line. For creation, use distinct negative values as placeholder.", "type": "object"}, "name": {"description": "Resource name for this mediation group. Format is: accounts/{publisher_id}/mediationGroups/{mediation_group_id} Example: accounts/pub-****************/mediationGroups/**********", "type": "string"}, "state": {"description": "The status of the mediation group. Only enabled mediation groups will be served.", "enum": ["STATE_UNSPECIFIED", "ENABLED", "DISABLED"], "enumDescriptions": ["Default value for an unset field. Do not use.", "The mediation group is active and used to serve ads or to collect stats.", "The mediation group has been disabled and is no longer serving ads or collecting stats. Disabled mediation groups can be re-enabled."], "type": "string"}, "targeting": {"$ref": "MediationGroupTargeting", "description": "Set of criteria targeted by this mediation group, such as ad units and geo locations."}}, "type": "object"}, "MediationGroupMediationGroupLine": {"description": "Settings for an ad network used by a mediation group.", "id": "MediationGroupMediationGroupLine", "properties": {"adSourceId": {"description": "The ID of the ad source this mediation line is associated with.", "type": "string"}, "adUnitMappings": {"additionalProperties": {"type": "string"}, "description": "References of the ad unit mappings for each ad unit associated with this mediation line. Key is the ad unit ID, value is resource name of the ad unit mapping. For mediation lines where the ad source id is the AdMob Network, ad unit mappings will be ignored.", "type": "object"}, "cpmMicros": {"description": "The CPM for this allocation line. $0.01 is the minimum allowed amount. For LIVE CPM modes, the default amount is $0.01. This value is ignored if `cpm_mode` is `LIVE`. **Warning:** \"USD\" is the only supported currency at the moment. The unit is in micros.", "format": "int64", "type": "string"}, "cpmMode": {"description": "Indicates how the CPM for this mediation line is provided. Note that `MANUAL` and `LIVE` are the only fully-supported mode at the moment. Please use the AdMob UI (https://admob.google.com) if you wish to create or update to other cpm modes.", "enum": ["CPM_MODE_UNSPECIFIED", "LIVE", "MANUAL", "ANO"], "enumDescriptions": ["Default value for an unset field. Do not use.", "CPM is calculated per ad request based on the ad auction.", "CPM is set by the user manually.", "CPM is retrieved from the ad network using the publisher specified credentials. Output only."], "type": "string"}, "displayName": {"description": "User-provided label for this mediation line. The maximum length allowed is 255 characters.", "type": "string"}, "experimentVariant": {"description": "Output only. The Mediation A/B experiment variant to which the mediation group line belongs to.", "enum": ["VARIANT_UNSPECIFIED", "VARIANT_A", "VARIANT_B", "ORIGINAL"], "enumDescriptions": ["Default value for an unset field. Do not use.", "The mediation group line belongs to the variant A.", "The mediation group line belongs to the variant B.", "The mediation group line isn't a part of an experiment."], "readOnly": true, "type": "string"}, "id": {"description": "The 16 digit ID for this mediation line e.g. **********012345. When creating a new mediation group line, use a distinct negative integer as the ID place holder.", "type": "string"}, "state": {"description": "The status of the mediation group line. Only enabled mediation group lines will be served.", "enum": ["STATE_UNSPECIFIED", "ENABLED", "DISABLED", "REMOVED"], "enumDescriptions": ["Default value for an unset field. Do not use.", "The mediation group line is active and used to serve ads.", "The mediation group line has been disabled and is no longer served. Disabled mediation group lines can be re-enabled.", "The mediation group line will be removed and no longer served. Removed mediation group lines can not be re-enabled and will no longer be visible. This is an input only field."], "type": "string"}}, "type": "object"}, "MediationGroupTargeting": {"description": "Set of criteria targeted by this mediation group. For example, a mediation group can target specific ad unit IDs, platform, format and geo location.", "id": "MediationGroupTargeting", "properties": {"adUnitIds": {"description": "Ad units targeted by this mediation group. Example: \"ca-app-pub-1234/8790\".", "items": {"type": "string"}, "type": "array"}, "excludedRegionCodes": {"description": "The Unicode country/region code (CLDR) of a location, such as \"US\". Unset if this mediation group does not exclude any region.", "items": {"type": "string"}, "type": "array"}, "format": {"description": "Ad format targeted by this mediation group. Examples: \"banner\", \"native\".", "type": "string"}, "idfaTargeting": {"description": "The parameter can be used to target ad requests based on the availability of the IDFA. If set to ALL, the mediation group applies to all ad requests (with or without IDFA). If set to AVAILABLE, the mediation group applies to ad requests with IDFA. If set to NOT_AVAILABLE, the mediation group applies to ad requests without IDFA. Doesn't need to be specified for an ANDROID device.", "enum": ["IDFA_TARGETING_UNSPECIFIED", "ALL", "AVAILABLE", "NOT_AVAILABLE"], "enumDescriptions": ["Default value. This value is unused.", "All the devices are targeted, regardless of IDFA availability.", "Only the devices with IDFA availability are targeted.", "Only the devices without IDFA availability are targeted."], "type": "string"}, "platform": {"description": "Describes the platform of the app. Examples: \"IOS\", \"Android\".", "type": "string"}, "targetedRegionCodes": {"description": "The Unicode country/region code (CLDR) of a location, such as \"US\". Unset if this mediation group targets all available regions. For more information, see http://www.unicode.org/reports/tr35/#unicode_region_subtag.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "MediationReportSpec": {"description": "The specification for generating an AdMob Mediation report. For example, the specification to get observed ECPM sliced by ad source and app for the 'US' and 'CN' countries can look like the following example: { \"date_range\": { \"start_date\": {\"year\": 2021, \"month\": 9, \"day\": 1}, \"end_date\": {\"year\": 2021, \"month\": 9, \"day\": 30} }, \"dimensions\": [\"AD_SOURCE\", \"APP\", \"COUNTRY\"], \"metrics\": [\"OBSERVED_ECPM\"], \"dimension_filters\": [ { \"dimension\": \"COUNTRY\", \"matches_any\": {\"values\": [{\"value\": \"US\", \"value\": \"CN\"}]} } ], \"sort_conditions\": [ {\"dimension\":\"APP\", order: \"ASCENDING\"} ], \"localization_settings\": { \"currency_code\": \"USD\", \"language_code\": \"en-US\" } } For a better understanding, you can treat the preceding specification like the following pseudo SQL: SELECT AD_SOURCE, APP, COUNTRY, OBSERVED_ECPM FROM MEDIATION_REPORT WHERE DATE >= '2021-09-01' AND DATE <= '2021-09-30' AND COUNTRY IN ('US', 'CN') GROUP BY AD_SOURCE, APP, COUNTRY ORDER BY APP ASC;", "id": "MediationReportSpec", "properties": {"dateRange": {"$ref": "DateRange", "description": "The date range for which the report is generated."}, "dimensionFilters": {"description": "Describes which report rows to match based on their dimension values.", "items": {"$ref": "MediationReportSpecDimensionFilter"}, "type": "array"}, "dimensions": {"description": "List of dimensions of the report. The value combination of these dimensions determines the row of the report. If no dimensions are specified, the report returns a single row of requested metrics for the entire account.", "items": {"enum": ["DIMENSION_UNSPECIFIED", "DATE", "MONTH", "WEEK", "AD_SOURCE", "AD_SOURCE_INSTANCE", "AD_UNIT", "APP", "MEDIATION_GROUP", "COUNTRY", "FORMAT", "PLATFORM", "MOBILE_OS_VERSION", "GMA_SDK_VERSION", "APP_VERSION_NAME", "SERVING_RESTRICTION"], "enumDescriptions": ["Default value for an unset field. Do not use.", "A date in the YYYYMMDD format (for example, \"********\"). Requests can specify at most one time dimension.", "A month in the YYYYMM format (for example, \"202107\"). Requests can specify at most one time dimension.", "The date of the first day of a week in the YYYYMMDD format (for example, \"********\"). Requests can specify at most one time dimension.", "The [unique ID of the ad source](/admob/api/v1/ad_sources) (for example, \"5450213213286189855\" and \"AdMob Network\" as label value).", "The unique ID of the ad source instance (for example, \"ca-app-pub-1234:asi:5678\" and \"AdMob (default)\" as label value).", "The unique ID of the ad unit (for example, \"ca-app-pub-1234/8790\"). If AD_UNIT dimension is specified, then APP is included automatically.", "The unique ID of the mobile application (for example, \"ca-app-pub-1234~1234\").", "The unique ID of the mediation group (for example, \"ca-app-pub-1234:mg:1234\" and \"AdMob (default)\" as label value).", "CLDR country code of the place where the ad views/clicks occur (for example, \"US\" or \"FR\"). This is a geography dimension.", "Format of the ad unit (for example, \"banner\", \"native\"), an ad delivery dimension.", "Mobile OS platform of the app (for example, \"Android\" or \"iOS\").", "Mobile operating system version, e.g. \"iOS 13.5.1\".", "GMA SDK version, e.g. \"iOS 7.62.0\".", "For Android, the app version name can be found in versionName in PackageInfo. For iOS, the app version name can be found in CFBundleShortVersionString.", "Restriction mode for ads serving (e.g. \"Non-personalized ads\")."], "type": "string"}, "type": "array"}, "localizationSettings": {"$ref": "LocalizationSettings", "description": "Localization settings of the report."}, "maxReportRows": {"description": "Maximum number of report data rows to return. If the value is not set, the API returns as many rows as possible, up to 100000. Acceptable values are 1-100000, inclusive. Values larger than 100000 return an error.", "format": "int32", "type": "integer"}, "metrics": {"description": "List of metrics of the report. A report must specify at least one metric.", "items": {"enum": ["METRIC_UNSPECIFIED", "AD_REQUESTS", "CLICKS", "ESTIMATED_EARNINGS", "IMPRESSIONS", "IMPRESSION_CTR", "MATCHED_REQUESTS", "MATCH_RATE", "OBSERVED_ECPM"], "enumDescriptions": ["Default value for an unset field. Do not use.", "The number of requests. The value is an integer.", "The number of times a user clicks an ad. The value is an integer.", "The estimated earnings of the AdMob publisher. The currency unit (USD, EUR, or other) of the earning metrics are determined by the localization setting for currency. The amount is in micros. For example, $6.50 would be represented as 6500000. Estimated earnings per mediation group and per ad source instance level is supported dating back to October 20, 2019. Third-party estimated earnings will show 0 for dates prior to October 20, 2019.", "The total number of ads shown to users. The value is an integer.", "The ratio of clicks over impressions. The value is a double precision (approximate) decimal value.", "The number of times ads are returned in response to a request. The value is an integer.", "The ratio of matched ad requests over the total ad requests. The value is a double precision (approximate) decimal value.", "The third-party ad network's estimated average eCPM. The currency unit (USD, EUR, or other) of the earning metrics are determined by the localization setting for currency. The amount is in micros. For example, $2.30 would be represented as 2300000. The estimated average eCPM per mediation group and per ad source instance level is supported dating back to October 20, 2019. Third-party estimated average eCPM will show 0 for dates prior to October 20, 2019."], "type": "string"}, "type": "array"}, "sortConditions": {"description": "Describes the sorting of report rows. The order of the condition in the list defines its precedence; the earlier the condition, the higher its precedence. If no sort conditions are specified, the row ordering is undefined.", "items": {"$ref": "MediationReportSpecSortCondition"}, "type": "array"}, "timeZone": {"description": "A report time zone. Accepts an IANA TZ name values, such as \"America/Los_Angeles.\" If no time zone is defined, the account default takes effect. Check default value by the get account action. **Warning:** The \"America/Los_Angeles\" is the only supported value at the moment.", "type": "string"}}, "type": "object"}, "MediationReportSpecDimensionFilter": {"description": "Describes which report rows to match based on their dimension values.", "id": "MediationReportSpecDimensionFilter", "properties": {"dimension": {"description": "Applies the filter criterion to the specified dimension.", "enum": ["DIMENSION_UNSPECIFIED", "DATE", "MONTH", "WEEK", "AD_SOURCE", "AD_SOURCE_INSTANCE", "AD_UNIT", "APP", "MEDIATION_GROUP", "COUNTRY", "FORMAT", "PLATFORM", "MOBILE_OS_VERSION", "GMA_SDK_VERSION", "APP_VERSION_NAME", "SERVING_RESTRICTION"], "enumDescriptions": ["Default value for an unset field. Do not use.", "A date in the YYYYMMDD format (for example, \"********\"). Requests can specify at most one time dimension.", "A month in the YYYYMM format (for example, \"202107\"). Requests can specify at most one time dimension.", "The date of the first day of a week in the YYYYMMDD format (for example, \"********\"). Requests can specify at most one time dimension.", "The [unique ID of the ad source](/admob/api/v1/ad_sources) (for example, \"5450213213286189855\" and \"AdMob Network\" as label value).", "The unique ID of the ad source instance (for example, \"ca-app-pub-1234:asi:5678\" and \"AdMob (default)\" as label value).", "The unique ID of the ad unit (for example, \"ca-app-pub-1234/8790\"). If AD_UNIT dimension is specified, then APP is included automatically.", "The unique ID of the mobile application (for example, \"ca-app-pub-1234~1234\").", "The unique ID of the mediation group (for example, \"ca-app-pub-1234:mg:1234\" and \"AdMob (default)\" as label value).", "CLDR country code of the place where the ad views/clicks occur (for example, \"US\" or \"FR\"). This is a geography dimension.", "Format of the ad unit (for example, \"banner\", \"native\"), an ad delivery dimension.", "Mobile OS platform of the app (for example, \"Android\" or \"iOS\").", "Mobile operating system version, e.g. \"iOS 13.5.1\".", "GMA SDK version, e.g. \"iOS 7.62.0\".", "For Android, the app version name can be found in versionName in PackageInfo. For iOS, the app version name can be found in CFBundleShortVersionString.", "Restriction mode for ads serving (e.g. \"Non-personalized ads\")."], "type": "string"}, "matchesAny": {"$ref": "StringList", "description": "Matches a row if its value for the specified dimension is in one of the values specified in this condition."}}, "type": "object"}, "MediationReportSpecSortCondition": {"description": "Sorting direction to be applied on a dimension or a metric.", "id": "MediationReportSpecSortCondition", "properties": {"dimension": {"description": "Sort by the specified dimension.", "enum": ["DIMENSION_UNSPECIFIED", "DATE", "MONTH", "WEEK", "AD_SOURCE", "AD_SOURCE_INSTANCE", "AD_UNIT", "APP", "MEDIATION_GROUP", "COUNTRY", "FORMAT", "PLATFORM", "MOBILE_OS_VERSION", "GMA_SDK_VERSION", "APP_VERSION_NAME", "SERVING_RESTRICTION"], "enumDescriptions": ["Default value for an unset field. Do not use.", "A date in the YYYYMMDD format (for example, \"********\"). Requests can specify at most one time dimension.", "A month in the YYYYMM format (for example, \"202107\"). Requests can specify at most one time dimension.", "The date of the first day of a week in the YYYYMMDD format (for example, \"********\"). Requests can specify at most one time dimension.", "The [unique ID of the ad source](/admob/api/v1/ad_sources) (for example, \"5450213213286189855\" and \"AdMob Network\" as label value).", "The unique ID of the ad source instance (for example, \"ca-app-pub-1234:asi:5678\" and \"AdMob (default)\" as label value).", "The unique ID of the ad unit (for example, \"ca-app-pub-1234/8790\"). If AD_UNIT dimension is specified, then APP is included automatically.", "The unique ID of the mobile application (for example, \"ca-app-pub-1234~1234\").", "The unique ID of the mediation group (for example, \"ca-app-pub-1234:mg:1234\" and \"AdMob (default)\" as label value).", "CLDR country code of the place where the ad views/clicks occur (for example, \"US\" or \"FR\"). This is a geography dimension.", "Format of the ad unit (for example, \"banner\", \"native\"), an ad delivery dimension.", "Mobile OS platform of the app (for example, \"Android\" or \"iOS\").", "Mobile operating system version, e.g. \"iOS 13.5.1\".", "GMA SDK version, e.g. \"iOS 7.62.0\".", "For Android, the app version name can be found in versionName in PackageInfo. For iOS, the app version name can be found in CFBundleShortVersionString.", "Restriction mode for ads serving (e.g. \"Non-personalized ads\")."], "type": "string"}, "metric": {"description": "Sort by the specified metric.", "enum": ["METRIC_UNSPECIFIED", "AD_REQUESTS", "CLICKS", "ESTIMATED_EARNINGS", "IMPRESSIONS", "IMPRESSION_CTR", "MATCHED_REQUESTS", "MATCH_RATE", "OBSERVED_ECPM"], "enumDescriptions": ["Default value for an unset field. Do not use.", "The number of requests. The value is an integer.", "The number of times a user clicks an ad. The value is an integer.", "The estimated earnings of the AdMob publisher. The currency unit (USD, EUR, or other) of the earning metrics are determined by the localization setting for currency. The amount is in micros. For example, $6.50 would be represented as 6500000. Estimated earnings per mediation group and per ad source instance level is supported dating back to October 20, 2019. Third-party estimated earnings will show 0 for dates prior to October 20, 2019.", "The total number of ads shown to users. The value is an integer.", "The ratio of clicks over impressions. The value is a double precision (approximate) decimal value.", "The number of times ads are returned in response to a request. The value is an integer.", "The ratio of matched ad requests over the total ad requests. The value is a double precision (approximate) decimal value.", "The third-party ad network's estimated average eCPM. The currency unit (USD, EUR, or other) of the earning metrics are determined by the localization setting for currency. The amount is in micros. For example, $2.30 would be represented as 2300000. The estimated average eCPM per mediation group and per ad source instance level is supported dating back to October 20, 2019. Third-party estimated average eCPM will show 0 for dates prior to October 20, 2019."], "type": "string"}, "order": {"description": "Sorting order of the dimension or metric.", "enum": ["SORT_ORDER_UNSPECIFIED", "ASCENDING", "DESCENDING"], "enumDescriptions": ["Default value for an unset field. Do not use.", "Sort dimension value or metric value in ascending order.", "Sort dimension value or metric value in descending order."], "type": "string"}}, "type": "object"}, "NetworkReportSpec": {"description": "The specification for generating an AdMob Network report. For example, the specification to get clicks and estimated earnings for only the 'US' and 'CN' countries can look like the following example: { 'date_range': { 'start_date': {'year': 2021, 'month': 9, 'day': 1}, 'end_date': {'year': 2021, 'month': 9, 'day': 30} }, 'dimensions': ['DATE', 'APP', 'COUNTRY'], 'metrics': ['CLICKS', 'ESTIMATED_EARNINGS'], 'dimension_filters': [ { 'dimension': 'COUNTRY', 'matches_any': {'values': [{'value': 'US', 'value': 'CN'}]} } ], 'sort_conditions': [ {'dimension':'APP', order: 'ASCENDING'}, {'metric':'CLICKS', order: 'DESCENDING'} ], 'localization_settings': { 'currency_code': 'USD', 'language_code': 'en-US' } } For a better understanding, you can treat the preceding specification like the following pseudo SQL: SELECT DATE, APP, COUNTRY, CLIC<PERSON>, ESTIMATED_EARNINGS FROM NETWORK_REPORT WHERE DATE >= '2021-09-01' AND DATE <= '2021-09-30' AND COUNTRY IN ('US', 'CN') GROUP BY DATE, APP, COUNTRY ORDER BY APP ASC, CLICKS DESC;", "id": "NetworkReportSpec", "properties": {"dateRange": {"$ref": "DateRange", "description": "The date range for which the report is generated."}, "dimensionFilters": {"description": "Describes which report rows to match based on their dimension values.", "items": {"$ref": "NetworkReportSpecDimensionFilter"}, "type": "array"}, "dimensions": {"description": "List of dimensions of the report. The value combination of these dimensions determines the row of the report. If no dimensions are specified, the report returns a single row of requested metrics for the entire account.", "items": {"enum": ["DIMENSION_UNSPECIFIED", "DATE", "MONTH", "WEEK", "AD_UNIT", "APP", "AD_TYPE", "COUNTRY", "FORMAT", "PLATFORM", "MOBILE_OS_VERSION", "GMA_SDK_VERSION", "APP_VERSION_NAME", "SERVING_RESTRICTION"], "enumDescriptions": ["Default value for an unset field. Do not use.", "A date in the YYYYMMDD format (for example, \"********\"). Requests can specify at most one time dimension.", "A month in the YYYYMM format (for example, \"202107\"). Requests can specify at most one time dimension.", "The date of the first day of a week in the YYYYMMDD format (for example, \"********\"). Requests can specify at most one time dimension.", "The unique ID of the ad unit (for example, \"ca-app-pub-1234/1234\"). If AD_UNIT dimension is specified, then APP is included automatically.", "The unique ID of the mobile application (for example, \"ca-app-pub-1234~1234\").", "Type of the ad (for example, \"text\" or \"image\"), an ad delivery dimension. **Warning:** The dimension is incompatible with [AD_REQUESTS](#Metric.ENUM_VALUES.AD_REQUESTS), [MATCH_RATE](#Metric.ENUM_VALUES.MATCH_RATE) and [IMPRESSION_RPM](#Metric.ENUM_VALUES.IMPRESSION_RPM) metrics.", "CLDR country code of the place where the ad views/clicks occur (for example, \"US\" or \"FR\"). This is a geography dimension.", "Format of the ad unit (for example, \"banner\", \"native\"), an ad delivery dimension.", "Mobile OS platform of the app (for example, \"Android\" or \"iOS\").", "Mobile operating system version, e.g. \"iOS 13.5.1\".", "GMA SDK version, e.g. \"iOS 7.62.0\".", "For Android, the app version name can be found in versionName in PackageInfo. For iOS, the app version name can be found in CFBundleShortVersionString.", "Restriction mode for ads serving (e.g. \"Non-personalized ads\")."], "type": "string"}, "type": "array"}, "localizationSettings": {"$ref": "LocalizationSettings", "description": "Localization settings of the report."}, "maxReportRows": {"description": "Maximum number of report data rows to return. If the value is not set, the API returns as many rows as possible, up to 100000. Acceptable values are 1-100000, inclusive. Values larger than 100000 return an error.", "format": "int32", "type": "integer"}, "metrics": {"description": "List of metrics of the report. A report must specify at least one metric.", "items": {"enum": ["METRIC_UNSPECIFIED", "AD_REQUESTS", "CLICKS", "ESTIMATED_EARNINGS", "IMPRESSIONS", "IMPRESSION_CTR", "IMPRESSION_RPM", "MATCHED_REQUESTS", "MATCH_RATE", "SHOW_RATE"], "enumDescriptions": ["Default value for an unset field. Do not use.", "The number of ad requests. The value is an integer. **Warning:** The metric is incompatible with [AD_TYPE](#Dimension.ENUM_VALUES.AD_TYPE) dimension.", "The number of times a user clicks an ad. The value is an integer.", "The estimated earnings of the AdMob publisher. The currency unit (USD, EUR, or other) of the earning metrics are determined by the localization setting for currency. The amount is in micros. For example, $6.50 would be represented as 6500000.", "The total number of ads shown to users. The value is an integer.", "The ratio of clicks over impressions. The value is a double precision (approximate) decimal value.", "The estimated earnings per thousand ad impressions. The value is in micros. For example, $1.03 would be represented as 1030000. Equivalent to eCPM in the AdMob UI. **Warning:** The metric is incompatible with [AD_TYPE](#Dimension.ENUM_VALUES.AD_TYPE) dimension.", "The number of times ads are returned in response to a request. The value is an integer.", "The ratio of matched ad requests over the total ad requests. The value is a double precision (approximate) decimal value. **Warning:** The metric is incompatible with [AD_TYPE](#Dimension.ENUM_VALUES.AD_TYPE) dimension.", "The ratio of ads that are displayed over ads that are returned, defined as impressions / matched requests. The value is a double precision (approximate) decimal value."], "type": "string"}, "type": "array"}, "sortConditions": {"description": "Describes the sorting of report rows. The order of the condition in the list defines its precedence; the earlier the condition, the higher its precedence. If no sort conditions are specified, the row ordering is undefined.", "items": {"$ref": "NetworkReportSpecSortCondition"}, "type": "array"}, "timeZone": {"description": "A report time zone. Accepts an IANA TZ name values, such as \"America/Los_Angeles.\" If no time zone is defined, the account default takes effect. Check default value by the get account action. **Warning:** The \"America/Los_Angeles\" is the only supported value at the moment.", "type": "string"}}, "type": "object"}, "NetworkReportSpecDimensionFilter": {"description": "Describes which report rows to match based on their dimension values.", "id": "NetworkReportSpecDimensionFilter", "properties": {"dimension": {"description": "Applies the filter criterion to the specified dimension.", "enum": ["DIMENSION_UNSPECIFIED", "DATE", "MONTH", "WEEK", "AD_UNIT", "APP", "AD_TYPE", "COUNTRY", "FORMAT", "PLATFORM", "MOBILE_OS_VERSION", "GMA_SDK_VERSION", "APP_VERSION_NAME", "SERVING_RESTRICTION"], "enumDescriptions": ["Default value for an unset field. Do not use.", "A date in the YYYYMMDD format (for example, \"********\"). Requests can specify at most one time dimension.", "A month in the YYYYMM format (for example, \"202107\"). Requests can specify at most one time dimension.", "The date of the first day of a week in the YYYYMMDD format (for example, \"********\"). Requests can specify at most one time dimension.", "The unique ID of the ad unit (for example, \"ca-app-pub-1234/1234\"). If AD_UNIT dimension is specified, then APP is included automatically.", "The unique ID of the mobile application (for example, \"ca-app-pub-1234~1234\").", "Type of the ad (for example, \"text\" or \"image\"), an ad delivery dimension. **Warning:** The dimension is incompatible with [AD_REQUESTS](#Metric.ENUM_VALUES.AD_REQUESTS), [MATCH_RATE](#Metric.ENUM_VALUES.MATCH_RATE) and [IMPRESSION_RPM](#Metric.ENUM_VALUES.IMPRESSION_RPM) metrics.", "CLDR country code of the place where the ad views/clicks occur (for example, \"US\" or \"FR\"). This is a geography dimension.", "Format of the ad unit (for example, \"banner\", \"native\"), an ad delivery dimension.", "Mobile OS platform of the app (for example, \"Android\" or \"iOS\").", "Mobile operating system version, e.g. \"iOS 13.5.1\".", "GMA SDK version, e.g. \"iOS 7.62.0\".", "For Android, the app version name can be found in versionName in PackageInfo. For iOS, the app version name can be found in CFBundleShortVersionString.", "Restriction mode for ads serving (e.g. \"Non-personalized ads\")."], "type": "string"}, "matchesAny": {"$ref": "StringList", "description": "Matches a row if its value for the specified dimension is in one of the values specified in this condition."}}, "type": "object"}, "NetworkReportSpecSortCondition": {"description": "Sorting direction to be applied on a dimension or a metric.", "id": "NetworkReportSpecSortCondition", "properties": {"dimension": {"description": "Sort by the specified dimension.", "enum": ["DIMENSION_UNSPECIFIED", "DATE", "MONTH", "WEEK", "AD_UNIT", "APP", "AD_TYPE", "COUNTRY", "FORMAT", "PLATFORM", "MOBILE_OS_VERSION", "GMA_SDK_VERSION", "APP_VERSION_NAME", "SERVING_RESTRICTION"], "enumDescriptions": ["Default value for an unset field. Do not use.", "A date in the YYYYMMDD format (for example, \"********\"). Requests can specify at most one time dimension.", "A month in the YYYYMM format (for example, \"202107\"). Requests can specify at most one time dimension.", "The date of the first day of a week in the YYYYMMDD format (for example, \"********\"). Requests can specify at most one time dimension.", "The unique ID of the ad unit (for example, \"ca-app-pub-1234/1234\"). If AD_UNIT dimension is specified, then APP is included automatically.", "The unique ID of the mobile application (for example, \"ca-app-pub-1234~1234\").", "Type of the ad (for example, \"text\" or \"image\"), an ad delivery dimension. **Warning:** The dimension is incompatible with [AD_REQUESTS](#Metric.ENUM_VALUES.AD_REQUESTS), [MATCH_RATE](#Metric.ENUM_VALUES.MATCH_RATE) and [IMPRESSION_RPM](#Metric.ENUM_VALUES.IMPRESSION_RPM) metrics.", "CLDR country code of the place where the ad views/clicks occur (for example, \"US\" or \"FR\"). This is a geography dimension.", "Format of the ad unit (for example, \"banner\", \"native\"), an ad delivery dimension.", "Mobile OS platform of the app (for example, \"Android\" or \"iOS\").", "Mobile operating system version, e.g. \"iOS 13.5.1\".", "GMA SDK version, e.g. \"iOS 7.62.0\".", "For Android, the app version name can be found in versionName in PackageInfo. For iOS, the app version name can be found in CFBundleShortVersionString.", "Restriction mode for ads serving (e.g. \"Non-personalized ads\")."], "type": "string"}, "metric": {"description": "Sort by the specified metric.", "enum": ["METRIC_UNSPECIFIED", "AD_REQUESTS", "CLICKS", "ESTIMATED_EARNINGS", "IMPRESSIONS", "IMPRESSION_CTR", "IMPRESSION_RPM", "MATCHED_REQUESTS", "MATCH_RATE", "SHOW_RATE"], "enumDescriptions": ["Default value for an unset field. Do not use.", "The number of ad requests. The value is an integer. **Warning:** The metric is incompatible with [AD_TYPE](#Dimension.ENUM_VALUES.AD_TYPE) dimension.", "The number of times a user clicks an ad. The value is an integer.", "The estimated earnings of the AdMob publisher. The currency unit (USD, EUR, or other) of the earning metrics are determined by the localization setting for currency. The amount is in micros. For example, $6.50 would be represented as 6500000.", "The total number of ads shown to users. The value is an integer.", "The ratio of clicks over impressions. The value is a double precision (approximate) decimal value.", "The estimated earnings per thousand ad impressions. The value is in micros. For example, $1.03 would be represented as 1030000. Equivalent to eCPM in the AdMob UI. **Warning:** The metric is incompatible with [AD_TYPE](#Dimension.ENUM_VALUES.AD_TYPE) dimension.", "The number of times ads are returned in response to a request. The value is an integer.", "The ratio of matched ad requests over the total ad requests. The value is a double precision (approximate) decimal value. **Warning:** The metric is incompatible with [AD_TYPE](#Dimension.ENUM_VALUES.AD_TYPE) dimension.", "The ratio of ads that are displayed over ads that are returned, defined as impressions / matched requests. The value is a double precision (approximate) decimal value."], "type": "string"}, "order": {"description": "Sorting order of the dimension or metric.", "enum": ["SORT_ORDER_UNSPECIFIED", "ASCENDING", "DESCENDING"], "enumDescriptions": ["Default value for an unset field. Do not use.", "Sort dimension value or metric value in ascending order.", "Sort dimension value or metric value in descending order."], "type": "string"}}, "type": "object"}, "PublisherAccount": {"description": "A publisher account contains information relevant to the use of this API, such as the time zone used for the reports.", "id": "PublisherAccount", "properties": {"currencyCode": {"description": "Currency code of the earning-related metrics, which is the 3-letter code defined in ISO 4217. The daily average rate is used for the currency conversion.", "type": "string"}, "name": {"description": "Resource name of this account. Format is accounts/{publisher_id}.", "type": "string"}, "publisherId": {"description": "The unique ID by which this publisher account can be identified in the API requests (for example, pub-**********).", "type": "string"}, "reportingTimeZone": {"description": "The time zone that is used in reports that are generated for this account. The value is a time-zone ID as specified by the CLDR project, for example, \"America/Los_Angeles\".", "type": "string"}}, "type": "object"}, "ReportFooter": {"description": "Groups data available after report generation, for example, warnings and row counts. Always sent as the last message in the stream response.", "id": "ReportFooter", "properties": {"matchingRowCount": {"description": "Total number of rows that matched the request. Warning: This count does NOT always match the number of rows in the response. Do not make that assumption when processing the response.", "format": "int64", "type": "string"}, "warnings": {"description": "Warnings associated with generation of the report.", "items": {"$ref": "ReportWarning"}, "type": "array"}}, "type": "object"}, "ReportHeader": {"description": "Groups data helps to treat the generated report. Always sent as a first message in the stream response.", "id": "ReportHeader", "properties": {"dateRange": {"$ref": "DateRange", "description": "The date range for which the report is generated. This is identical to the range specified in the report request."}, "localizationSettings": {"$ref": "LocalizationSettings", "description": "Localization settings of the report. This is identical to the settings in the report request."}, "reportingTimeZone": {"description": "The report time zone. The value is a time-zone ID as specified by the CLDR project, for example, \"America/Los_Angeles\".", "type": "string"}}, "type": "object"}, "ReportRow": {"description": "A row of the returning report.", "id": "ReportRow", "properties": {"dimensionValues": {"additionalProperties": {"$ref": "ReportRowDimensionValue"}, "description": "Map of dimension values in a row, with keys as enum name of the dimensions.", "type": "object"}, "metricValues": {"additionalProperties": {"$ref": "ReportRowMetricValue"}, "description": "Map of metric values in a row, with keys as enum name of the metrics. If a metric being requested has no value returned, the map will not include it.", "type": "object"}}, "type": "object"}, "ReportRowDimensionValue": {"description": "Representation of a dimension value.", "id": "ReportRowDimensionValue", "properties": {"displayLabel": {"description": "The localized string representation of the value. If unspecified, the display label should be derived from the value.", "type": "string"}, "value": {"description": "Dimension value in the format specified in the report's spec Dimension enum.", "type": "string"}}, "type": "object"}, "ReportRowMetricValue": {"description": "Representation of a metric value.", "id": "ReportRowMetricValue", "properties": {"doubleValue": {"description": "Double precision (approximate) decimal values. Rates are from 0 to 1.", "format": "double", "type": "number"}, "integerValue": {"description": "Metric integer value.", "format": "int64", "type": "string"}, "microsValue": {"description": "Amount in micros. One million is equivalent to one unit. Currency value is in the unit (USD, EUR or other) specified by the request. For example, $6.50 whould be represented as 6500000 micros.", "format": "int64", "type": "string"}}, "type": "object"}, "ReportWarning": {"description": "Warnings associated with generation of the report.", "id": "ReportWarning", "properties": {"description": {"description": "Describes the details of the warning message, in English.", "type": "string"}, "type": {"description": "Type of the warning.", "enum": ["TYPE_UNSPECIFIED", "DATA_BEFORE_ACCOUNT_TIMEZONE_CHANGE", "DATA_DELAYED", "OTHER", "REPORT_CURRENCY_NOT_ACCOUNT_CURRENCY"], "enumDescriptions": ["Default value for an unset field. Do not use.", "Some data in this report is aggregated based on a time zone different from the requested time zone. This could happen if a local time-zone report has the start time before the last time this time zone changed. The description field will contain the date of the last time zone change.", "There is an unusual delay in processing the source data for the requested date range. The report results might be less up to date than usual. AdMob is aware of the issue and is actively working to resolve it.", "Warnings that are exposed without a specific type. Useful when new warning types are added but the API is not changed yet.", "The currency being requested is not the account currency. The earning metrics will be based on the requested currency, and thus not a good estimation of the final payment anymore, due to the currency rate fluctuation."], "type": "string"}}, "type": "object"}, "StopMediationAbExperimentRequest": {"description": "Request to end the mediation A/B experiment and choose a winning variant.", "id": "StopMediationAbExperimentRequest", "properties": {"variantChoice": {"description": "The choice for the winning variant.", "enum": ["VARIANT_CHOICE_UNSPECIFIED", "VARIANT_CHOICE_A", "VARIANT_CHOICE_B"], "enumDescriptions": ["Default value for an unset field. Do not use.", "Use the mediation lines from Variant A and complete the experiment. Variant A contains the original serving lines before the experiment creation.", "Use the mediation lines form Variant B and complete the experiment. Variant B contains the newly added serving lines."], "type": "string"}}, "type": "object"}, "StringList": {"description": "List of string values.", "id": "StringList", "properties": {"values": {"description": "The string values.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}}, "servicePath": "", "title": "AdMob API", "version": "v1beta", "version_module": true}