#pragma once

// @generated by torchgen/gen.py from Operator.h

#include <string_view>
#include <tuple>
#include <vector>

// Forward declarations of any types needed in the operator signatures.
// We can't directly include these classes because it will cause circular include dependencies.
// This file is included by TensorBody.h, which defines the Tensor class.
#include <ATen/core/ATen_fwd.h>

namespace at {
namespace _ops {


struct TORCH_API _dirichlet_grad {
  using schema = at::Tensor (const at::Tensor &, const at::Tensor &, const at::Tensor &);
  using ptr_schema = schema*;
  // See Note [static constexpr char* members for windows NVCC]
  static constexpr const char* name = "aten::_dirichlet_grad";
  static constexpr const char* overload_name = "";
  static constexpr const char* schema_str = "_dirichlet_grad(Tensor x, Tensor alpha, Tensor total) -> Tensor";
  static at::Tensor call(const at::Tensor & x, const at::Tensor & alpha, const at::Tensor & total);
  static at::Tensor redispatch(c10::DispatchKeySet dispatchKeySet, const at::Tensor & x, const at::Tensor & alpha, const at::Tensor & total);
};

struct TORCH_API _dirichlet_grad_out {
  using schema = at::Tensor & (const at::Tensor &, const at::Tensor &, const at::Tensor &, at::Tensor &);
  using ptr_schema = schema*;
  // See Note [static constexpr char* members for windows NVCC]
  static constexpr const char* name = "aten::_dirichlet_grad";
  static constexpr const char* overload_name = "out";
  static constexpr const char* schema_str = "_dirichlet_grad.out(Tensor x, Tensor alpha, Tensor total, *, Tensor(a!) out) -> Tensor(a!)";
  static at::Tensor & call(const at::Tensor & x, const at::Tensor & alpha, const at::Tensor & total, at::Tensor & out);
  static at::Tensor & redispatch(c10::DispatchKeySet dispatchKeySet, const at::Tensor & x, const at::Tensor & alpha, const at::Tensor & total, at::Tensor & out);
};

}} // namespace at::_ops
