"""
Test script for helmet detection functionality
"""

import sys
import os
sys.path.append('src')

from helmet_detector import HelmetDetector
import cv2

def main():
    print("🚀 Starting Helmet Detection Test...")
    print("=" * 50)
    
    # Initialize detector
    try:
        detector = HelmetDetector()
        print("✅ Helmet detector initialized successfully!")
    except Exception as e:
        print(f"❌ Error initializing detector: {e}")
        return
    
    # Create output directory
    os.makedirs("test_outputs", exist_ok=True)
    
    # Test with sample images from the cloned repository
    test_images = [
        "helmet_detection_model/source_files/construction-safety.jpg",
        "helmet_detection_model/source_files/portrait-of-woman-with-mask-and-man-with-safety-glasses-on-a-construction-HX01FH.jpg",
        "helmet_detection_model/source_files/two-young-construction-workers-wearing-555864.jpg"
    ]
    
    print(f"\n🔍 Testing with {len(test_images)} sample images...")
    
    for i, image_path in enumerate(test_images, 1):
        if os.path.exists(image_path):
            print(f"\n📸 Processing Image {i}: {os.path.basename(image_path)}")
            print("-" * 40)
            
            output_path = f"test_outputs/result_{i}_{os.path.basename(image_path)}"
            result = detector.process_image(image_path, output_path)
            
            if 'error' not in result:
                compliance = result['compliance']
                detections = result['detections']
                
                print(f"🔢 Total detections: {len(detections)}")
                print(f"👥 Total persons: {compliance['total_persons']}")
                print(f"⛑️  Persons with helmets: {compliance['persons_with_helmets']}")
                print(f"🚫 Persons without helmets: {compliance['persons_without_helmets']}")
                print(f"📊 Compliance rate: {compliance['compliance_rate']:.2%}")
                print(f"✅ Is compliant: {'YES' if compliance['is_compliant'] else 'NO'}")
                
                if compliance['violations']:
                    print(f"⚠️  VIOLATIONS DETECTED: {len(compliance['violations'])} person(s) without helmet!")
                
                print(f"💾 Output saved to: {output_path}")
                
                # Print detailed detections
                print("\n🔍 Detailed Detections:")
                for j, detection in enumerate(detections):
                    print(f"  {j+1}. {detection['class_name']} (confidence: {detection['confidence']:.2f})")
                
            else:
                print(f"❌ Error processing image: {result['error']}")
        else:
            print(f"⚠️  Image not found: {image_path}")
    
    print(f"\n🎉 Test completed! Check the 'test_outputs' folder for processed images.")
    print("=" * 50)

if __name__ == "__main__":
    main()
