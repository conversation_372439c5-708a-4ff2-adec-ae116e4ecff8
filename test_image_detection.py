import os
import sys
import base64
import google.generativeai as genai
from PIL import Image

# Set up Gemini API
GEMINI_API_KEY = "AIzaSyBntkJYuOFqBaUqhcDB1umoL34Kl3kkKEU"
genai.configure(api_key=GEMINI_API_KEY)

def test_image_detection(image_path):
    """Test helmet detection with the provided image"""
    try:
        # Check if image exists
        if not os.path.exists(image_path):
            print(f"Error: Image not found at {image_path}")
            return
        
        print(f"Testing image: {image_path}")
        
        # Load and display image info
        with Image.open(image_path) as img:
            print(f"Image size: {img.size}")
            print(f"Image mode: {img.mode}")
        
        # Initialize Gemini model
        model = genai.GenerativeModel('gemini-1.5-flash')
        
        # Load image for Gemini
        image = Image.open(image_path)
        
        # Create prompt for helmet detection
        prompt = """
        Analyze this image for safety helmet detection. Please provide:
        1. Are there any people in the image?
        2. If yes, are they wearing safety helmets?
        3. Count how many people are wearing helmets vs not wearing helmets
        4. Provide a confidence score for your detection
        5. Describe what you see in the image
        
        Format your response as JSON with the following structure:
        {
            "people_detected": number,
            "helmets_detected": number,
            "no_helmet_detected": number,
            "confidence": "high/medium/low",
            "description": "description of what you see"
        }
        """
        
        print("Sending request to Gemini...")
        
        # Generate response
        response = model.generate_content([prompt, image])
        
        print("Response from Gemini:")
        print(response.text)
        
        return response.text
        
    except Exception as e:
        print(f"Error during detection: {str(e)}")
        return None

if __name__ == "__main__":
    # Test with the provided image
    test_image_path = "test-images/image.png"
    
    print("=== Helmet Detection Test ===")
    result = test_image_detection(test_image_path)
    
    if result:
        print("\n=== Test Successful ===")
    else:
        print("\n=== Test Failed ===")
